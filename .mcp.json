{"mcpServers": {"memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAT6xsuj4AZGlUTkK2erRIOa8-0DK5"}}, "mcp_server_mysql": {"command": "npx", "args": ["-y", "@benborla29/mcp-server-mysql"], "env": {"MYSQL_HOST": "127.0.0.1", "MYSQL_PORT": "3307", "MYSQL_USER": "root", "MYSQL_PASS": "root", "MYSQL_DB": "blog_api_v3", "ALLOW_INSERT_OPERATION": "false", "ALLOW_UPDATE_OPERATION": "false", "ALLOW_DELETE_OPERATION": "false", "PATH": "/Users/<USER>/.nvm/versions/node/v22.14.0/bin:/usr/bin:/bin", "NODE_PATH": "/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules"}}, "qdrant": {"type": "sse", "url": "http://localhost:9058/sse"}, "n8n-mcp": {"command": "npx", "args": ["n8n-mcp"], "env": {"MCP_MODE": "stdio", "LOG_LEVEL": "error", "DISABLE_CONSOLE_OUTPUT": "true", "N8N_API_URL": "http://localhost:5678", "N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJiNTMwNzkzZC02YjVkLTQwNzYtODMyYy03Yzc3NTYwYmU3ZGUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUyODIxNjk2fQ.q9Fe1seIC81oJju5bqXOIPym-f8Cz1oz6eYAn12xcv8"}}, "jaeger-mcp-server": {"command": "npx", "args": ["-y", "jaeger-mcp-server"], "env": {"JAEGER_URL": "http://localhost", "JAEGER_PORT": "16686"}}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "env": {}}, "browsermcp": {"command": "npx", "args": ["@browsermcp/mcp@latest"]}, "console-ninja": {"command": "npx", "args": ["-y", "-c", "node ~/.console-ninja/mcp/"]}, "serena": {"type": "sse", "url": "http://localhost:9121/sse"}, "code-context": {"command": "npx", "args": ["-y", "@zilliz/code-context-mcp@latest"], "env": {"EMBEDDING_PROVIDER": "Gemini", "GEMINI_API_KEY": "AIzaSyBX67YNJNyMpp1W7bqTPDnzeAUSaoaWu1w", "MILVUS_TOKEN": "484667d28463ce3537f651f23bbd50febcdeacfb0b04f6acf425c5c40810927a3e447438ac4d1b41f02a0c0e9591c7745c07414c", "EMBEDDING_MODEL": "gemini-embedding-001"}}}, "projectSettings": {"autoStart": ["memory", "sequential-thinking", "context7", "brave-search", "mcp_server_mysql", "n8n-mcp", "playwright", "browsermcp", "serena"], "defaultServer": "memory"}, "approvedServers": ["memory", "sequential-thinking", "context7", "brave-search", "mcp_server_mysql", "n8n-mcp", "playwright", "browsermcp", "serena"], "rejectedServers": []}