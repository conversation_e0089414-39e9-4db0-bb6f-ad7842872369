# API Endpoint Requests

## Missing Endpoints from Current Implementation

Based on the analysis of the Swagger documentation and current UI implementation, the following API endpoints should be requested from the backend team:

### 1. 2FA Endpoints (Update path structure)

- `/api/cms/v1/2fa/complete-login` - Complete login with 2FA
- `/api/cms/v1/2fa/enable` - Enable 2FA for user
- `/api/cms/v1/2fa/disable` - Disable 2FA for user
- `/api/cms/v1/2fa/verify` - Verify 2FA token

### 2. Analytics Endpoints (Currently only under /api/v1/)

Request these to be moved to `/api/cms/v1/` structure:

- `/api/cms/v1/analytics/active-sessions`
- `/api/cms/v1/analytics/active-users`
- `/api/cms/v1/analytics/aggregation`
- `/api/cms/v1/analytics/bulk-events`
- `/api/cms/v1/analytics/daily`
- `/api/cms/v1/analytics/events`
- `/api/cms/v1/analytics/events/time-range`
- `/api/cms/v1/analytics/insights`
- `/api/cms/v1/analytics/journeys/{journey_id}`
- `/api/cms/v1/analytics/journeys/{journey_id}/dropoff`
- `/api/cms/v1/analytics/journeys/{journey_id}/funnel`
- `/api/cms/v1/analytics/monthly`
- `/api/cms/v1/analytics/realtime`
- `/api/cms/v1/analytics/reports`
- `/api/cms/v1/analytics/steps/{step_id}`
- `/api/cms/v1/analytics/tenant`

### 3. Additional Auth Endpoints

- `/api/cms/v1/auth/logout-all` - Logout from all devices/sessions

### 4. Enhanced User Management

- User analytics and activity tracking endpoints
- Enhanced user role management endpoints

## Notes

- All endpoints should follow the `/api/cms/v1/` structure for consistency
- Ensure proper authentication and authorization for all endpoints
- Include proper error handling and response codes
- Consider pagination for list endpoints
- Include proper documentation for request/response schemas
