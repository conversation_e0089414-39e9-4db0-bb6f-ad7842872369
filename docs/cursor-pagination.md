# Cursor-Based Pagination Implementation Guide

## 📋 Overview

This document provides a comprehensive guide for implementing cursor-based pagination in frontend modules. Cursor pagination is more efficient than offset-based pagination for large datasets and provides consistent results even when data is being modified.

## 🔧 API Specification

### Request Format

```bash
GET /api/cms/v1/{endpoint}?limit={number}&cursor={base64_encoded_cursor}
```

### Request Parameters

| Parameter | Type   | Required | Default | Description                               |
| --------- | ------ | -------- | ------- | ----------------------------------------- |
| `limit`   | number | No       | 10      | Items per page (1-100)                    |
| `cursor`  | string | No       | -       | Base64 encoded cursor token for next page |
| `search`  | string | No       | -       | Search query (endpoint specific)          |

### Response Format

```typescript
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": "",
    "path": "/api/cms/v1/{endpoint}",
    "timestamp": "2025-06-13T15:32:37+07:00",
    "details": null
  },
  "data": T[],                    // Array of items
  "meta": {
    "next_cursor"?: string,       // Base64 encoded token for next page
    "has_more": boolean           // More data available
  }
}
```

### Cursor Token Format

The cursor is a base64 encoded JSON object:

```json
{
  "type": "id",
  "value": 10,
  "direction": "next"
}
```

**Example:**

- Raw: `{"type":"id","value":10,"direction":"next"}`
- Encoded: `eyJ0eXBlIjoiaWQiLCJ2YWx1ZSI6MTAsImRpcmVjdGlvbiI6Im5leHQifQ==`

## 🚀 Frontend Implementation

### 1. State Management

```typescript
// State variables
const [cursorAfter, setCursorAfter] = useState<string | null>(null);
const [cursorHistory, setCursorHistory] = useState<string[]>([]);
const [items, setItems] = useState<T[]>([]);
const [loading, setLoading] = useState(false);

// Computed state
const isFirstPage = cursorHistory.length === 0;
```

### 2. Data Fetching Function

```typescript
const fetchData = useCallback(
  async (payload?: any) => {
    setLoading(true);

    const params = {
      ...query,
      ...filters,
      limit: 10, // or your preferred limit
      ...payload,
    };

    const cleanedParams = cleanParams(params);
    console.log('🔍 API Request params:', cleanedParams);

    try {
      const response = await getItems(cleanedParams);
      if (response.status.success) {
        console.log('✅ API Response meta:', response.meta);
        setItems(response.data);

        // Update cursor from API response
        const nextCursor = response.meta.next_cursor ?? null;
        console.log('🔄 Setting next cursor:', nextCursor);
        setCursorAfter(nextCursor);
      } else {
        message.error(response.status.message);
      }
    } catch (error) {
      console.error('❌ API Error:', error);
      message.error('Failed to fetch data');
    } finally {
      setLoading(false);
    }
  },
  [filters, query, limit],
);
```

### 3. Navigation Handlers

```typescript
const handleNext = () => {
  if (cursorAfter) {
    const currentCursor = cursorAfter;
    console.log('🔄 Next clicked - Current cursor:', currentCursor);
    console.log('📚 Current history:', cursorHistory);

    // Add current cursor to history
    setCursorHistory((prev) => {
      const newHistory = [...prev, currentCursor];
      console.log('📚 New history:', newHistory);
      return newHistory;
    });

    // Fetch next page
    fetchData({ cursor: cursorAfter });
  }
};

const handleBack = () => {
  console.log('⬅️ Back clicked - Current history:', cursorHistory);

  if (cursorHistory.length > 0) {
    // Remove the last cursor from history
    setCursorHistory((prev) => {
      const newHistory = prev.slice(0, -1);
      console.log('📚 New history after back:', newHistory);
      return newHistory;
    });

    if (cursorHistory.length <= 1) {
      // Go back to first page
      console.log('🏠 Going to first page');
      fetchData();
    } else {
      // Go to previous page using the previous cursor
      const previousCursor = cursorHistory[cursorHistory.length - 2];
      console.log('⬅️ Going to previous page with cursor:', previousCursor);
      fetchData({ cursor: previousCursor });
    }
  }
};
```

### 4. Component Integration

```typescript
// Initialize data loading
useEffectOnce(() => {
  setCursorHistory([]);
  fetchData();
});

// Render pagination component
<CursorPaginationV2
  onBack={handleBack}
  onNext={handleNext}
  disabledBack={isFirstPage}
  disabledNext={!cursorAfter}
  loading={loading}
  isFirstPage={isFirstPage}
/>
```

## 📁 Complete Module Example

### API Service (`api.ts`)

```typescript
import { apiService } from '../../services/api.service';
import { YourModuleListResponse, YourModuleResponse } from './type';

const url = `/api/cms/v1/your-module`;

export async function getItems(params: any): Promise<YourModuleListResponse> {
  const response = await apiService.get(url, {
    params,
  });
  return response.data;
}

export async function getItem(id: string): Promise<YourModuleResponse> {
  const response = await apiService.get(`${url}/${id}`);
  return response.data;
}

// ... other CRUD operations
```

### Type Definitions (`type.ts`)

```typescript
export interface YourModule {
  id: number;
  name: string;
  // ... other fields
}

export interface YourModuleListResponse {
  status: {
    code: number;
    message: string;
    success: boolean;
    error_code?: string;
    path: string;
    timestamp: string;
  };
  data: YourModule[];
  meta: {
    next_cursor?: string;
    has_more: boolean;
  };
}
```

### List Component (`list/index.tsx`)

```typescript
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { Button, Col, Row, Space, Table, message } from 'antd';
import queryString from 'query-string';
import CursorPaginationV2 from '../../../components/pagination/cursor-pagination-v2';
import { cleanParams } from '../../../services/utils.service';
import { getItems } from '../api';
import { YourModule } from '../type';

export default function List() {
  const { t } = useTranslation('your-module');
  const { search } = useLocation();
  const query = queryString.parse(search);

  // State management
  const [cursorAfter, setCursorAfter] = useState<string | null>(null);
  const [cursorHistory, setCursorHistory] = useState<string[]>([]);
  const [items, setItems] = useState<YourModule[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({});
  const [limit] = useState<number>(10);

  // Computed state
  const isFirstPage = cursorHistory.length === 0;

  // Data fetching
  const fetchData = useCallback(
    async (payload?: any) => {
      setLoading(true);

      const params = {
        ...query,
        ...filters,
        limit,
        ...payload,
      };

      const cleanedParams = cleanParams(params);
      console.log('🔍 API Request params:', cleanedParams);

      try {
        const response = await getItems(cleanedParams);
        if (response.status.success) {
          console.log('✅ API Response meta:', response.meta);
          setItems(response.data);

          const nextCursor = response.meta.next_cursor ?? null;
          console.log('🔄 Setting next cursor:', nextCursor);
          setCursorAfter(nextCursor);
        } else {
          message.error(response.status.message);
        }
      } catch (error) {
        console.error('❌ API Error:', error);
        message.error('Failed to fetch data');
      } finally {
        setLoading(false);
      }
    },
    [filters, query, limit],
  );

  // Navigation handlers
  const handleNext = () => {
    if (cursorAfter) {
      const currentCursor = cursorAfter;
      console.log('🔄 Next clicked - Current cursor:', currentCursor);
      console.log('📚 Current history:', cursorHistory);

      setCursorHistory((prev) => {
        const newHistory = [...prev, currentCursor];
        console.log('📚 New history:', newHistory);
        return newHistory;
      });

      fetchData({ cursor: cursorAfter });
    }
  };

  const handleBack = () => {
    console.log('⬅️ Back clicked - Current history:', cursorHistory);

    if (cursorHistory.length > 0) {
      setCursorHistory((prev) => {
        const newHistory = prev.slice(0, -1);
        console.log('📚 New history after back:', newHistory);
        return newHistory;
      });

      if (cursorHistory.length <= 1) {
        console.log('🏠 Going to first page');
        fetchData();
      } else {
        const previousCursor = cursorHistory[cursorHistory.length - 2];
        console.log('⬅️ Going to previous page with cursor:', previousCursor);
        fetchData({ cursor: previousCursor });
      }
    }
  };

  // Initialize
  useEffectOnce(() => {
    setCursorHistory([]);
    fetchData();
  });

  // Table columns
  const columns = [
    {
      title: t('id'),
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: t('name'),
      dataIndex: 'name',
      key: 'name',
    },
    // ... other columns
  ];

  return (
    <div>
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold">{t('title')}</div>
        <div className="gap-4">
          <Button type="primary">
            {t('btnAdd')}
          </Button>
        </div>
      </div>

      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        />

        <Row justify="end" className="p-4">
          <Col>
            <CursorPaginationV2
              onBack={handleBack}
              onNext={handleNext}
              disabledBack={isFirstPage}
              disabledNext={!cursorAfter}
              loading={loading}
              isFirstPage={isFirstPage}
            />
          </Col>
        </Row>
      </Space>
    </div>
  );
}
```

## 🎯 Navigation Flow

### State Transitions

```text
Initial State:
- cursorHistory: []
- cursorAfter: null
- isFirstPage: true
- Back button: DISABLED

After First Load:
- cursorHistory: []
- cursorAfter: "cursor1" (from API)
- isFirstPage: true
- Back button: DISABLED
- Next button: ENABLED

After Click Next:
- cursorHistory: ["cursor1"]
- cursorAfter: "cursor2" (from API)
- isFirstPage: false
- Back button: ENABLED
- Next button: ENABLED

After Click Next Again:
- cursorHistory: ["cursor1", "cursor2"]
- cursorAfter: "cursor3" (from API)
- isFirstPage: false
- Back button: ENABLED
- Next button: ENABLED

After Click Back:
- cursorHistory: ["cursor1"]
- cursorAfter: "cursor2" (from API)
- isFirstPage: false
- Back button: ENABLED
- Next button: ENABLED

After Click Back Again:
- cursorHistory: []
- cursorAfter: "cursor1" (from API)
- isFirstPage: true
- Back button: DISABLED
- Next button: ENABLED
```

## 🔍 Debugging

### Console Logs

The implementation includes comprehensive logging:

```typescript
// Request logging
console.log('🔍 API Request params:', cleanedParams);

// Response logging
console.log('✅ API Response meta:', response.meta);
console.log('🔄 Setting next cursor:', nextCursor);

// Navigation logging
console.log('🔄 Next clicked - Current cursor:', currentCursor);
console.log('📚 Current history:', cursorHistory);
console.log('⬅️ Back clicked - Current history:', cursorHistory);
console.log('🏠 Going to first page');
console.log('⬅️ Going to previous page with cursor:', previousCursor);
```

### Common Issues

1. **Back button not activating**: Check `isFirstPage` calculation
2. **Wrong API parameter**: Ensure using `cursor` not `cursorAfter`
3. **Cursor format error**: Verify base64 encoding on backend
4. **State not updating**: Check `setCursorAfter` with correct response field

## ✅ Implementation Checklist

### Frontend Checklist

- [ ] State variables: `cursorAfter`, `cursorHistory`, `isFirstPage`
- [ ] API service with correct parameter name (`cursor`)
- [ ] Response handling with `response.meta.next_cursor`
- [ ] Navigation handlers: `handleNext`, `handleBack`
- [ ] Cursor history management
- [ ] Pagination component integration
- [ ] Debug logging (optional)
- [ ] Error handling
- [ ] Loading states

### Backend Checklist

- [ ] Accept `cursor` parameter
- [ ] Decode base64 cursor token
- [ ] Parse cursor JSON: `{type, value, direction}`
- [ ] Apply cursor to database query
- [ ] Generate next cursor token
- [ ] Return `next_cursor` and `has_more` in meta
- [ ] Handle invalid cursor gracefully

## 🚨 Common Pitfalls

1. **Parameter Name Mismatch**: Always use `cursor` in API requests
2. **Response Field**: Always read `response.meta.next_cursor`
3. **History Management**: Don't rely on `cursorBefore` from API
4. **State Updates**: Use functional updates for cursor history
5. **Base64 Encoding**: Ensure proper encoding/decoding on both ends

## 📚 Reference Implementation

See these modules for working examples:

- `src/pages/rbac-permission/list/index.tsx`
- `src/pages/rbac-role/list/index.tsx`
- `src/pages/rbac-permission-group/list/index.tsx`
- `src/pages/zrbac-resource/list/index.tsx`
