# Onboarding Sample Data Initialization System

## Overview

This document describes the sample data initialization system that runs when a new user completes onboarding and creates their first tenant/organization. The system automatically populates essential data across multiple modules to provide a fully functional starting environment.

## System Architecture

### High-Level Flow

```mermaid
flowchart TD
    A[User Registration] --> B[Email Verification]
    B --> C[User Login]
    C --> D[Onboarding Start]
    D --> E[Create Organization/Tenant]
    E --> F[Initialize Sample Data]
    F --> G[RBAC Module]
    F --> H[Website Module]
    F --> I[Blog Module]
    F --> J[Media Module]
    F --> K[User Preferences]

    G --> L[Create Default Roles]
    G --> M[Assign Owner Role]

    H --> N[Create Default Website]
    H --> O[Website Settings]

    I --> P[Sample Categories]
    I --> Q[Sample Tags]
    I --> R[Welcome Post]

    J --> S[Default Media Folders]

    K --> T[Default User Settings]
```

## Module-Specific Sample Data

### 1. RBAC Module Sample Data

```mermaid
erDiagram
    TENANT ||--o{ ROLE : contains
    ROLE ||--o{ PERMISSION : has
    ROLE ||--o{ USER_ROLE : assigned_to
    USER ||--o{ USER_ROLE : has

    ROLE {
        int id PK
        int tenant_id FK
        string name
        string description
        json permissions
        boolean is_system
    }

    USER_ROLE {
        int id PK
        int tenant_id FK
        int user_id FK
        int role_id FK
        datetime assigned_at
    }
```

**Default Roles Created:**

- **Owner**: Full system access, cannot be deleted
- **Admin**: Administrative access, can manage most resources
- **Editor**: Content creation and editing permissions
- **Viewer**: Read-only access

### 2. Website Module Sample Data

```mermaid
erDiagram
    TENANT ||--o{ WEBSITE : owns
    WEBSITE ||--o{ WEBSITE_SETTINGS : has
    WEBSITE ||--o{ NAVIGATION_MENU : contains
    WEBSITE ||--o{ PAGE : contains

    WEBSITE {
        int id PK
        int tenant_id FK
        string name
        string domain
        string status
        json config
    }

    WEBSITE_SETTINGS {
        int id PK
        int website_id FK
        string key
        json value
        string group
    }

    NAVIGATION_MENU {
        int id PK
        int website_id FK
        string name
        string location
        json items
    }
```

**Default Website Data:**

- Primary website with tenant's organization name
- Basic SEO settings
- Default navigation menus (header, footer)
- Home page and About page

### 3. Blog Module Sample Data

```mermaid
erDiagram
    TENANT ||--o{ BLOG_CATEGORY : contains
    TENANT ||--o{ BLOG_TAG : contains
    TENANT ||--o{ BLOG_POST : contains
    BLOG_CATEGORY ||--o{ BLOG_POST : categorizes
    BLOG_TAG }o--o{ BLOG_POST : labels

    BLOG_CATEGORY {
        int id PK
        int tenant_id FK
        string name
        string slug
        string description
        int parent_id FK
    }

    BLOG_POST {
        int id PK
        int tenant_id FK
        int author_id FK
        int category_id FK
        string title
        string slug
        text content
        string status
        datetime published_at
    }

    BLOG_TAG {
        int id PK
        int tenant_id FK
        string name
        string slug
    }
```

**Default Blog Data:**

- Categories: "General", "News", "Tutorial"
- Tags: "welcome", "getting-started"
- Welcome blog post with onboarding instructions

### 4. Media Module Sample Data

```mermaid
erDiagram
    TENANT ||--o{ MEDIA_FOLDER : contains
    MEDIA_FOLDER ||--o{ MEDIA_FOLDER : has_subfolders
    MEDIA_FOLDER ||--o{ MEDIA_FILE : contains

    MEDIA_FOLDER {
        int id PK
        int tenant_id FK
        string name
        string path
        int parent_id FK
    }

    MEDIA_FILE {
        int id PK
        int tenant_id FK
        int folder_id FK
        string filename
        string mime_type
        int size
        json metadata
    }
```

**Default Media Structure:**

- Root folders: "Images", "Documents", "Videos"
- Subfolders: "Images/Logos", "Images/Blog", "Documents/Templates"

## Sample Data Initialization Process

### Sequence Diagram

```mermaid
sequenceDiagram
    participant U as User
    participant A as Auth Service
    participant O as Onboarding Service
    participant S as Sample Data Service
    participant M as Module Services

    U->>A: Complete Registration
    A->>U: Send Verification Email
    U->>A: Verify Email
    U->>A: Login
    A->>U: JWT Token

    U->>O: Start Onboarding
    O->>O: Validate User
    U->>O: Create Organization
    O->>O: Create Tenant
    O->>S: Trigger Sample Data Init

    S->>M: Init RBAC Data
    M-->>S: Roles Created

    S->>M: Init Website Data
    M-->>S: Website Created

    S->>M: Init Blog Data
    M-->>S: Blog Data Created

    S->>M: Init Media Structure
    M-->>S: Folders Created

    S->>M: Init User Preferences
    M-->>S: Preferences Set

    S->>O: Sample Data Complete
    O->>U: Onboarding Complete
```

## Data Initialization Strategy

### Priority Levels

```mermaid
graph TD
    A[Priority 1: Core] --> B[RBAC Roles & Permissions]
    A --> C[Owner Role Assignment]

    D[Priority 2: Essential] --> E[Default Website]
    D --> F[Basic Settings]
    D --> G[Media Folders]

    H[Priority 3: Content] --> I[Sample Categories]
    H --> J[Welcome Content]
    H --> K[Navigation Menus]
```

### Transactional Approach

```mermaid
flowchart LR
    A[Begin Transaction] --> B{Init RBAC}
    B -->|Success| C{Init Website}
    B -->|Fail| Z[Rollback]
    C -->|Success| D{Init Blog}
    C -->|Fail| Z
    D -->|Success| E{Init Media}
    D -->|Fail| Z
    E -->|Success| F[Commit]
    E -->|Fail| Z
    Z --> G[Log Error]
    F --> H[Send Welcome Email]
```

## Error Handling and Recovery

### Partial Initialization States

```mermaid
stateDiagram-v2
    [*] --> Pending
    Pending --> Initializing: Start Process
    Initializing --> PartiallyComplete: Some Modules Fail
    Initializing --> Complete: All Success
    PartiallyComplete --> Retrying: Retry Failed Modules
    Retrying --> Complete: Success
    Retrying --> Failed: Max Retries
    Complete --> [*]
    Failed --> [*]
```

## Module Dependencies

```mermaid
graph TD
    A[Tenant Creation] --> B[RBAC Module]
    B --> C[User Preferences]
    A --> D[Website Module]
    D --> E[Blog Module]
    D --> F[Media Module]
    E --> F
    B --> D
    B --> E
    B --> F
```

## Configuration Schema

### Sample Data Configuration

```yaml
onboarding:
  sample_data:
    enabled: true
    modules:
      rbac:
        enabled: true
        roles:
          - name: 'Owner'
            system: true
            permissions: ['*']
          - name: 'Admin'
            permissions: ['manage_users', 'manage_content', 'manage_settings']
          - name: 'Editor'
            permissions: ['create_content', 'edit_content', 'manage_media']
          - name: 'Viewer'
            permissions: ['view_content', 'view_media']

      website:
        enabled: true
        default_pages: ['home', 'about', 'contact']
        default_menus: ['header', 'footer']

      blog:
        enabled: true
        categories: ['General', 'News', 'Tutorial']
        welcome_post: true

      media:
        enabled: true
        folder_structure:
          - 'Images/Logos'
          - 'Images/Blog'
          - 'Documents/Templates'
          - 'Videos'
```

## Monitoring and Analytics

### Sample Data Metrics

```mermaid
graph LR
    A[Metrics Collected] --> B[Initialization Time]
    A --> C[Success Rate]
    A --> D[Module Failures]
    A --> E[Retry Attempts]
    A --> F[User Engagement]

    F --> G[First Login After Init]
    F --> H[Sample Data Modifications]
    F --> I[Feature Adoption]
```

## Future Enhancements

1. **Template Selection**: Allow users to choose from different sample data templates during onboarding
2. **Industry-Specific Data**: Provide sample data tailored to specific industries
3. **Progressive Loading**: Load sample data on-demand as users explore features
4. **Customization Options**: Let users configure what sample data to include
5. **Import/Export**: Allow importing sample data from templates or exporting as templates

## Security Considerations

- All sample data is tenant-scoped
- No cross-tenant data leakage
- Sanitized content to prevent XSS
- Rate limiting on sample data generation
- Audit logging for all sample data creation

## Performance Optimization

- Bulk insert operations for efficiency
- Asynchronous processing for non-critical data
- Caching of common sample data templates
- Database indexing on frequently queried sample data
