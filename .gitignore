# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.local
*.log*

# IDE
.vscode/*
!.vscode/extensions.json
.idea
dist
.env
logs
./docs-project-mac
./docs-api
./playwright-report
./test-results
test-results/.last-run.json
test-results/complete-onboarding-flow-C-30588-ding-flow-for-verified-user-chromium/error-context.md
test-results/complete-onboarding-flow-C-481c1--onboarding-steps-correctly-chromium/error-context.md
test-results/complete-onboarding-flow-C-bd28b-dle-login-errors-gracefully-chromium/error-context.md
api-tests

*storybook.log
storybook-static
.serena/cache/typescript/document_symbols_cache_v23-06-25.pkl
.serena/cache/typescript/*.pkl