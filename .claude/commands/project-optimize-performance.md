# Optimize Performance

Please optimize performance for: $ARGUMENTS.

Follow these steps:

1. Profile the application
2. Identify bottlenecks
3. Optimize database queries
4. Implement caching strategies
5. Optimize API endpoints
6. Review resource usage
7. Implement performance monitoring
8. Test performance improvements

Focus on:

- Database query optimization
- Caching implementation
- API response times
- Memory usage
- Concurrent request handling
- Multi-tenant performance
