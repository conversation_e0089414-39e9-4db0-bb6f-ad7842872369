# Deploy Application

Please deploy the application to: $ARGUMENTS.

Follow these steps:

1. Run all tests to ensure code quality
2. Build the application binary
3. Update Docker images if needed
4. Run database migrations
5. Deploy to the target environment
6. Verify deployment health
7. Monitor application logs
8. Update deployment documentation

Commands to use:

- `make build` - Build the application
- `make test` - Run tests
- `make migrate-up` - Run migrations
- `docker-compose up -d` - Start services

Focus on:

- Zero-downtime deployment
- Database migration safety
- Health checks
- Rollback procedures
