# Backup Data

Please backup project data: $ARGUMENTS.

Follow these steps:

1. Backup database
2. Backup configuration files
3. Backup uploaded files
4. Backup logs (if needed)
5. Create backup archive
6. Verify backup integrity
7. Document backup location

Commands to use:

- `scripts/backup-n8n.sh` - Backup N8N data
- Database backup commands
- File system backups

Focus on:

- Data integrity
- Backup completeness
- Recovery procedures
- Backup scheduling
