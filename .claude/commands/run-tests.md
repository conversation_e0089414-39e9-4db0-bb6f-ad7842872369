# Run Tests

Please run and analyze tests for: $ARGUMENTS.

Follow these steps:

1. Run the specific test suite or all tests
2. Analyze test results and coverage
3. Fix any failing tests
4. Improve test coverage if needed
5. Check for flaky tests
6. Optimize test performance
7. Update test documentation

Commands to use:

- `go test ./...` - Run all tests
- `go test -v ./internal/modules/$MODULE/...` - Run specific module tests
- `go test -cover ./...` - Run tests with coverage
- `make test` - Run tests via Makefile

Focus on:

- Test coverage and quality
- Performance of test suite
- Reliability and stability
