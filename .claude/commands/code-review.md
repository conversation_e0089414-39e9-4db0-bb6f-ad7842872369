# Code Review

Please perform a comprehensive code review of: $ARGUMENTS.

Follow these steps:

1. Examine code structure and organization
2. Check for Go best practices and conventions
3. Review error handling and logging
4. Assess test coverage and quality
5. Look for security vulnerabilities
6. Check performance implications
7. Review documentation and comments
8. Suggest improvements and optimizations

Focus on:

- Code quality and maintainability
- Security best practices
- Performance considerations
- Test coverage
- Documentation completeness
