# Clean Project

Please clean up the project: $ARGUMENTS.

Follow these steps:

1. Remove temporary files and directories
2. Clean build artifacts
3. Remove unused dependencies
4. Clean up log files
5. Remove test artifacts
6. Clean Docker containers and images
7. Update .gitignore if needed

Commands to use:

- `make clean` - Clean build artifacts
- `rm -rf ./bin/*` - Remove binaries
- `rm -rf ./tmp/*` - Remove temporary files
- `docker system prune` - Clean Docker

Focus on:

- Disk space optimization
- Build artifact cleanup
- Dependency management
- Development environment cleanup
