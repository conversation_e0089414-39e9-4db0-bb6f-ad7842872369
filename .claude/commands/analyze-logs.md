# Analyze Logs

Please analyze the application logs for issues: $ARGUMENTS.

Follow these steps:

1. Check the main server log file: `server.log`
2. Look for error patterns, warnings, and anomalies
3. Analyze performance metrics and response times
4. Check for database connection issues
5. Review authentication and authorization logs
6. Identify any security concerns
7. Provide recommendations for improvements

Focus on:

- Error patterns and frequency
- Performance bottlenecks
- Security issues
- System health indicators
