# Setup Database

Please setup the database for the Blog API v3 project: $ARGUMENTS.

Follow these steps:

1. Start MySQL container: `docker-compose up -d mysql`
2. Wait for database to be ready
3. Run migrations: `make migrate-up`
4. Run seeders: `make seed`
5. Verify database schema
6. Check sample data
7. Test database connectivity

Commands to use:

- `make migrate-up` - Run all migrations
- `make migrate-status` - Check migration status
- `make seed` - Run seeders
- `make reset-db` - Reset database (development only)

Focus on:

- MySQL 8+ compatibility
- Multi-tenant schema
- Data integrity
- Performance optimization
