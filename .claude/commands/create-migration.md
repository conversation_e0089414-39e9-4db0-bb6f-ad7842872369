# Create Database Migration

Please create a new database migration for: $ARGUMENTS.

Follow these steps:

1. Analyze the current database schema
2. Understand the required changes
3. Create both up and down migration files
4. Follow the project's migration naming convention
5. Include proper constraints and indexes
6. Test the migration in development
7. Update any related models or repositories
8. Document the changes in the migration

Focus on:

- MySQL 8+ compatibility
- Proper constraint definitions
- Index optimization
- Data integrity
- Rollback capability
