# Generate API Documentation

Please generate comprehensive API documentation for: $ARGUMENTS.

Follow these steps:

1. Review all API endpoints in the module
2. Document request/response schemas
3. Include authentication requirements
4. Add example requests and responses
5. Document error codes and messages
6. Include rate limiting information
7. Add multi-tenant considerations
8. Update Bruno test collections

Focus on:

- Complete endpoint coverage
- Clear request/response examples
- Authentication and authorization
- Error handling documentation
- Multi-tenant specifics
