import { useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';

/**
 * Hook để lấy tenant ID từ URL params
 * @returns Tenant ID từ URL params hoặc undefined nếu không có
 */
export const useTenantId = (): string | undefined => {
  const { tenant_id } = useParams<{ tenant_id: string }>();
  const [tenantId, setTenantId] = useState<string | undefined>(tenant_id);

  useEffect(() => {
    if (tenant_id && tenant_id !== tenantId) {
      setTenantId(tenant_id);
    }
  }, [tenant_id, tenantId]);

  console.log('Current tenant_id:', tenantId);
  return tenantId;
};
