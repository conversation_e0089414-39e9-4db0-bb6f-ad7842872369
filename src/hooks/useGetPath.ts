import { useTenantId } from './useTenantId';

export const useGetPath = () => {
  const tenantId = useTenantId();

  return (path: string) => {
    // Remove leading and trailing slashes from the path
    const cleanPath = path.replace(/^\/+|\/+$/g, '');

    // If tenantId is not available yet, return a placeholder or wait
    if (!tenantId) {
      console.warn(
        'TenantId not available yet, returning path without tenant context',
      );
      return `/${cleanPath}`;
    }

    return `/dashboard/${tenantId}/${cleanPath}`;
  };
};
