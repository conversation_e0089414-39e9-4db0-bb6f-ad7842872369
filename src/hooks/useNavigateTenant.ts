import { useNavigate } from 'react-router-dom';
import { useGetPath } from './useGetPath';
import { useTenantId } from './useTenantId';

/**
 * Custom hook that combines useGetPath and useNavigate for easier navigation with tenant context
 * @returns A function that navigates to a path with tenant context
 */
export const useNavigateTenant = () => {
  const navigate = useNavigate();
  const getPath = useGetPath();
  const tenantId = useTenantId();

  return (path: string, options?: { replace?: boolean }) => {
    // If tenantId is not available, wait a bit and try again
    if (!tenantId) {
      console.warn('TenantId not available, delaying navigation');
      setTimeout(() => {
        const fullPath = getPath(path);
        navigate(fullPath, options);
      }, 100);
      return;
    }

    const fullPath = getPath(path);
    navigate(fullPath, options);
  };
};

export default useNavigateTenant;
