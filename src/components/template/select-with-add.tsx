import { PlusOutlined } from '@ant-design/icons';
import { Button, Input, Select } from 'antd';
import React, { useState } from 'react';
import ModalList from '../../pages/tour-attribute/list/modal';

const { Option } = Select;

interface SelectWithAddProps {
  options: string[];
  onAdd: (value: string) => void;
  onChange: (value: string) => void;
}

const SelectWithAdd: React.FC<SelectWithAddProps> = ({
  options,
  onAdd,
  onChange,
}) => {
  const [items, setItems] = useState<string[]>(options);
  const [inputVisible, setInputVisible] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>('');
  const [showModal, setShowModal] = useState<boolean>(false);

  const handleAddItem = () => {
    if (inputValue) {
      setItems([...items, inputValue]);
      onAdd(inputValue);
      setInputValue('');
      setInputVisible(false);
    }
  };

  const handleModalChange = (reload: boolean) => {
    // setItems([...items, value]);
    // onAdd(value);
    setShowModal(false);
  };

  return (
    <>
      <Select
        style={{ width: '100%' }}
        placeholder="Select an item"
        onChange={onChange}
        popupRender={(menu) => (
          <div>
            {menu}
            {inputVisible ? (
              <div style={{ padding: 8 }}>
                <Input
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target?.value)}
                  onPressEnter={handleAddItem}
                  style={{ width: 200, marginRight: 8 }}
                />
                <Button type="primary" onClick={handleAddItem}>
                  Lưu
                </Button>
              </div>
            ) : (
              <Button
                type="dashed"
                style={{ width: '100%', border: 'none' }}
                onClick={() => setShowModal(true)}
              >
                <PlusOutlined /> Thêm mới
              </Button>
            )}
          </div>
        )}
      >
        {items.map((item) => (
          <Option key={item} value={item}>
            {item}
          </Option>
        ))}
      </Select>
      {showModal && (
        <ModalList
          showModal={showModal}
          onChange={handleModalChange}
          group="CUSTOMER_GROUP"
        />
      )}
    </>
  );
};

export default SelectWithAdd;
