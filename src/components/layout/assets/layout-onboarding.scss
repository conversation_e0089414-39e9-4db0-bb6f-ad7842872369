/* Layout Onboarding Styles */
.layout-onboarding {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.05"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.05"/><circle cx="50" cy="10" r="0.5" fill="%23ffffff" opacity="0.03"/><circle cx="10" cy="60" r="0.5" fill="%23ffffff" opacity="0.03"/><circle cx="90" cy="40" r="0.5" fill="%23ffffff" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
  }

  .ant-layout-content {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px;
    min-height: 100vh;
  }

  // Responsive design
  @media (max-width: 768px) {
    .ant-layout-content {
      padding: 16px;
    }
  }
}

/* Override for Ant Design styles */
.layout-onboarding.ant-layout {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Card styles for onboarding forms */
// .layout-onboarding {
//   .ant-card {
//     border-radius: 12px;
//     box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
//     border: none;
//     backdrop-filter: blur(10px);
//     background: rgba(255, 255, 255, 0.95);
//   }

//   .ant-steps {
//     .ant-steps-item-process .ant-steps-item-icon {
//       background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
//     }
//   }
// }
