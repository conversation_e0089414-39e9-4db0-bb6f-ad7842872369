import { Layout } from 'antd';
import React from 'react';
import { Outlet } from 'react-router-dom';
import './assets/layout-onboarding.scss';

const { Content } = Layout;

const LayoutOnboarding: React.FC = () => {
  return (
    <Layout className="layout-onboarding">
      <Content
        style={{
          margin: 0,
          padding: 0,
          minHeight: '100vh',
          background: 'transparent',
        }}
      >
        <Outlet />
      </Content>
    </Layout>
  );
};

export default LayoutOnboarding;
