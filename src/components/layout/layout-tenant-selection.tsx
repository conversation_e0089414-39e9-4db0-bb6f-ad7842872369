import { LogoutOutlined } from '@ant-design/icons';
import { Button, Layout } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Outlet } from 'react-router-dom';
import { useAuthStore } from '../../pages/auth/auth';

const { Header, Content } = Layout;

const LayoutTenantSelection: React.FC = () => {
  const { t } = useTranslation('auth');
  const { logout } = useAuthStore();

  const handleLogout = () => {
    logout();
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header
        style={{
          padding: 0,
          background: '#fff',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          zIndex: 1000,
          paddingLeft: '24px',
          paddingRight: '24px',
        }}
      >
        <div className="flex items-center">
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <h2 style={{ margin: 0, color: '#1890ff', fontSize: '20px' }}>
              {t('tenant_selection.title')}
            </h2>
          </div>
        </div>
        <div className="flex items-center">
          <Button icon={<LogoutOutlined />} onClick={handleLogout}>
            {t('tenant_selection.logout_button')}
          </Button>
        </div>
      </Header>
      <Content
        style={{
          margin: 0,
          padding: 0,
          minHeight: 'calc(100vh - 64px)',
          backgroundColor: '#f5f5f5',
        }}
      >
        <Outlet />
      </Content>
    </Layout>
  );
};

export default LayoutTenantSelection;
