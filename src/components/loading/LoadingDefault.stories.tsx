import type { Meta, StoryObj } from '@storybook/react-webpack5';
import LoadingDefault from './loading-default';

const meta = {
  title: 'Components/Loading/LoadingDefault',
  component: LoadingDefault,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof LoadingDefault>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const InContainer: Story = {
  decorators: [
    (Story) => (
      <div
        style={{ height: '400px', width: '600px', border: '1px solid #ccc' }}
      >
        <Story />
      </div>
    ),
  ],
};
