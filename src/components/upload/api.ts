import { ApiResponse, createUploadService } from '../../services/api.service';
import { MediaFile } from './type';

const url = `/api/cms/v1/media`;

export interface UploadOptions {
  file: File;
  is_public?: boolean;
  folder_id?: number | string;
}

export async function uploadGallery(
  options: UploadOptions,
  config?: any,
): Promise<ApiResponse<MediaFile>> {
  const formData = new FormData();
  formData.append('file', options.file);
  formData.append('is_public', String(options.is_public ?? true));

  if (options.folder_id) {
    formData.append('folder_id', String(options.folder_id));
  }

  // Use dedicated upload service that doesn't have default Content-Type
  const uploadService = createUploadService();

  const response = await uploadService.post<ApiResponse<MediaFile>>(
    `${url}/upload`,
    formData,
    config,
  );

  return response.data;
}
