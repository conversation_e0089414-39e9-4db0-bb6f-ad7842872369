import type { Meta, StoryObj } from '@storybook/react-webpack5';
import StatusTag from './status-tag';

const meta = {
  title: 'Components/Table/StatusTag',
  component: StatusTag,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    value: {
      control: 'text',
      description: 'The status value to display',
    },
    statusList: {
      control: 'object',
      description: 'List of possible statuses with their colors',
    },
  },
} satisfies Meta<typeof StatusTag>;

export default meta;
type Story = StoryObj<typeof meta>;

const defaultStatusList = [
  { label: 'Active', value: 'active', color: 'green' },
  { label: 'Inactive', value: 'inactive', color: 'red' },
  { label: 'Pending', value: 'pending', color: 'orange' },
  { label: 'Draft', value: 'draft', color: 'blue' },
];

export const Active: Story = {
  args: {
    value: 'active',
    statusList: defaultStatusList,
  },
};

export const Inactive: Story = {
  args: {
    value: 'inactive',
    statusList: defaultStatusList,
  },
};

export const Pending: Story = {
  args: {
    value: 'pending',
    statusList: defaultStatusList,
  },
};

export const Draft: Story = {
  args: {
    value: 'draft',
    statusList: defaultStatusList,
  },
};

export const UnknownStatus: Story = {
  args: {
    value: 'unknown',
    statusList: defaultStatusList,
  },
};

export const BlogStatuses: Story = {
  args: {
    value: 'published',
    statusList: [
      { label: 'Published', value: 'published', color: 'green' },
      { label: 'Draft', value: 'draft', color: 'blue' },
      { label: 'Review', value: 'review', color: 'orange' },
      { label: 'Scheduled', value: 'scheduled', color: 'purple' },
      { label: 'Archived', value: 'archived', color: 'gray' },
    ],
  },
};
