import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick-theme.css';
import 'slick-carousel/slick/slick.css';
import { convertRoutesToMenuItems, routes } from '../../router';
import './assets/style.scss';

function LaucherHome() {
  // All hooks must be called at the top level

  const { t } = useTranslation('menu');

  const [isDragging, setIsDragging] = useState(false);

  const dragStartPos = useRef<{ x: number; y: number } | null>(null);

  // Constants and derived values
  const menuItems = convertRoutesToMenuItems(t, routes);
  const menuPerPage = 25;
  const totalMenu = Math.ceil(menuItems.length / menuPerPage);
  const dragThreshold = 10; // Minimum distance to consider as drag

  const settings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
  };

  const groupMenu = [];
  for (let i = 0; i < totalMenu; i++) {
    groupMenu.push(
      menuItems.slice(i * menuPerPage, i * menuPerPage + menuPerPage),
    );
  }

  // Handle touch/mouse start events
  const handlePointerStart = (
    e: React.PointerEvent | React.MouseEvent | React.TouchEvent,
  ) => {
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    dragStartPos.current = { x: clientX, y: clientY };
    setIsDragging(false);
  };

  // Handle touch/mouse move events
  const handlePointerMove = (
    e: React.PointerEvent | React.MouseEvent | React.TouchEvent,
  ) => {
    if (!dragStartPos.current) return;

    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    const deltaX = Math.abs(clientX - dragStartPos.current.x);
    const deltaY = Math.abs(clientY - dragStartPos.current.y);

    // If movement exceeds threshold, consider it a drag
    if (deltaX > dragThreshold || deltaY > dragThreshold) {
      setIsDragging(true);
    }
  };

  // Handle touch/mouse end events
  const handlePointerEnd = () => {
    // Reset drag state after a short delay to prevent immediate clicks
    setTimeout(() => {
      setIsDragging(false);
      dragStartPos.current = null;
    }, 100);
  };

  return (
    <div className="launcher-container">
      <div className="launcher-header">
        <div className="launcher-filter-container">
          <div className="launcher-filter-btn active">Tất cả</div>
          <div className="launcher-filter-btn">Bài viết</div>
        </div>
      </div>

      <div className="launcher-content">
        <Slider {...settings} className="launcher-slider">
          {groupMenu.map((group, index) => (
            <div key={index} className="launcher-page">
              <div className="launcher-grid">
                {group.map((menu, i) => (
                  <Link
                    to={menu.key}
                    key={i}
                    className="launcher-menu-item"
                    onClick={(e) => {
                      if (isDragging) {
                        e.preventDefault();
                      }
                    }}
                    onPointerDown={handlePointerStart}
                    onPointerMove={handlePointerMove}
                    onPointerUp={handlePointerEnd}
                    onTouchStart={handlePointerStart}
                    onTouchMove={handlePointerMove}
                    onTouchEnd={handlePointerEnd}
                    onMouseDown={handlePointerStart}
                    onMouseMove={handlePointerMove}
                    onMouseUp={handlePointerEnd}
                  >
                    <div className="launcher-menu-content">
                      <div className="launcher-menu-icon">{menu.icon}</div>
                      <span className="launcher-menu-label">{menu.label}</span>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          ))}
        </Slider>
      </div>
    </div>
  );
}

export { LaucherHome };
