// Launcher Container
.launcher-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  user-select: none;
  padding: 2rem 1rem;
}

// Header Section
.launcher-header {
  width: 100%;
  max-width: 1024px;
  margin-bottom: 3rem;

  .launcher-filter-container {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
}

// Filter Buttons
.launcher-filter-btn {
  border-radius: 24px;
  height: 44px;
  line-height: 44px;
  padding: 0 20px;
  border: 2px solid transparent;
  color: #64748b;
  font-size: 15px;
  font-weight: 500;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background: #f8fafc;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
    color: #475569;
  }

  &.active {
    background: #3b82f6;
    color: #ffffff;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);

    &:hover {
      background: #2563eb;
      transform: translateY(-1px);
    }
  }
}

// Content Section
.launcher-content {
  width: 100%;
  max-width: 1024px;

  .launcher-slider {
    .slick-list {
      overflow: hidden;
      border-radius: 16px;
    }

    .slick-dots {
      bottom: -50px;

      li {
        margin: 0 6px;

        button:before {
          font-size: 12px;
          color: #cbd5e1;
          transition: all 0.3s ease;
        }

        &.slick-active button:before {
          color: #3b82f6;
          transform: scale(1.2);
        }
      }
    }
  }
}

// Page Container
.launcher-page {
  width: 100%;
  padding: 1rem;
}

// Grid Layout
.launcher-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(4, 1fr);
  gap: 1.5rem;
  min-height: 500px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
  }

  @media (max-width: 640px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }
}

// Menu Item
.launcher-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: inherit;
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.02);

    .launcher-menu-content {
      background: #ffffff;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
    }
  }

  &:active {
    transform: scale(0.98);
  }
}

// Menu Content
.launcher-menu-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem 0.5rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  min-height: 90px;
  justify-content: center;
}

// Menu Icon
.launcher-menu-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    font-size: 1.75rem;
    margin-bottom: 0.375rem;
  }
}

// Menu Label
.launcher-menu-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #475569;
  text-align: center;
  line-height: 1.3;
  transition: color 0.3s ease;

  @media (max-width: 768px) {
    font-size: 0.75rem;
  }
}

// Legacy styles for backward compatibility
.icon-launcher-menu {
  background: url(./images/menu-laucher.svg) center center no-repeat !important;
}

.menu-icon {
  min-width: 36px;
  min-height: 36px;
  height: 36px;
  display: block;
  background-position: center;
  background-repeat: no-repeat;
}

.menu-all {
  background: url(./images/menu-all.svg) center center no-repeat !important;
}
