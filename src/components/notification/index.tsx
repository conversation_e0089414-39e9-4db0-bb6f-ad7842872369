import { BellOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Badge, Button, Dropdown, List, Typography } from 'antd';
import React, { useState, useEffect, useCallback } from 'react';
import './style.scss';
import {
  getNotifications,
  markNotificationRead,
  getUnreadCount,
  Notification,
} from './api';

const { Text } = Typography;

const NotificationComponent: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);
  const [cursor, setCursor] = useState<string | undefined>(undefined);

  const fetchNotifications = useCallback(
    async (reset = false) => {
      setLoading(true);
      try {
        const params = reset ? { limit: 20 } : { limit: 20, cursor };
        const response = await getNotifications(params);

        if (reset) {
          setNotifications(response.data);
        } else {
          setNotifications((prevNotifications) => [
            ...prevNotifications,
            ...response.data,
          ]);
        }

        setHasMore(response.meta.has_more || false);
        if (response.meta.next_cursor) {
          setCursor(response.meta.next_cursor);
        } else {
          setHasMore(false);
        }
      } catch (error) {
        console.error('Lỗi khi tải thông báo:', error);
      } finally {
        setLoading(false);
      }
    },
    [cursor],
  );

  const fetchUnreadCount = useCallback(async () => {
    try {
      const response = await getUnreadCount();
      setUnreadCount(response.data.unread_count);
    } catch (error) {
      console.error('Lỗi khi tải số lượng thông báo chưa đọc:', error);
    }
  }, []);

  useEffect(() => {
    fetchNotifications(true);
    fetchUnreadCount();
  }, [fetchNotifications, fetchUnreadCount]);

  const handleReadAll = async () => {
    try {
      // Đánh dấu tất cả đã đọc trên UI trước
      setNotifications((prevNotifications) =>
        prevNotifications.map((item) => ({ ...item, read: true })),
      );
      setUnreadCount(0);

      // TODO: Thêm API endpoint để đánh dấu tất cả đã đọc
    } catch (error) {
      console.error('Lỗi khi đánh dấu đã đọc tất cả:', error);
    }
  };

  const handleMarkAsRead = async (id: number) => {
    try {
      await markNotificationRead(id);

      // Cập nhật UI
      setNotifications((prevNotifications) =>
        prevNotifications.map((item) =>
          item.id === id ? { ...item, read: true } : item,
        ),
      );

      // Cập nhật lại số lượng chưa đọc
      fetchUnreadCount();
    } catch (error) {
      console.error('Lỗi khi đánh dấu đã đọc:', error);
    }
  };

  const handleLoadMore = () => {
    fetchNotifications();
  };

  // Define the dropdown content render function
  const renderNotificationContent = () => (
    <div className="notification-dropdown">
      <div className="notification-header">
        <span>Thông báo ({notifications.length})</span>
        <Button type="link" onClick={handleReadAll}>
          Đánh dấu tất cả đã đọc
        </Button>
      </div>
      <List
        className="notification-list"
        itemLayout="horizontal"
        dataSource={notifications}
        loading={loading && notifications.length === 0}
        renderItem={(item) => (
          <List.Item
            className={item.read ? 'read' : 'unread'}
            onClick={() => !item.read && handleMarkAsRead(item.id)}
          >
            <List.Item.Meta
              avatar={<Avatar src={item.avatar} />}
              title={item.title}
              description={item.description}
            />
            <div className="notification-time">{item.time}</div>
          </List.Item>
        )}
      />
      <div className="notification-footer">
        {hasMore ? (
          <Button
            type="link"
            onClick={handleLoadMore}
            loading={loading}
            className="load-more-btn"
          >
            Tải thêm
          </Button>
        ) : (
          <a href="/notifications">Xem tất cả</a>
        )}
      </div>
    </div>
  );

  return (
    <Dropdown
      popupRender={renderNotificationContent}
      trigger={['click']}
      placement="bottomRight"
      arrow
    >
      <div className="notification-icon">
        <Badge count={unreadCount} size="small">
          <BellOutlined style={{ fontSize: '20px' }} />
        </Badge>
      </div>
    </Dropdown>
  );
};

export default NotificationComponent;
