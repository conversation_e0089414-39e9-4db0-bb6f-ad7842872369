import type { Meta, StoryObj } from '@storybook/react-webpack5';
import { action } from '@storybook/addon-actions';
import CursorPagination from './cursor-pagination';

const meta = {
  title: 'Components/Pagination/CursorPagination',
  component: CursorPagination,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    total: {
      control: 'number',
      description: 'Total number of items',
    },
    showTotal: {
      control: 'boolean',
      description: 'Whether to show total count',
    },
    isBack: {
      control: 'boolean',
      description: 'Enable/disable back button',
    },
    isNext: {
      control: 'boolean',
      description: 'Enable/disable next button',
    },
  },
} satisfies Meta<typeof CursorPagination>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    total: 150,
    isBack: true,
    isNext: true,
    showTotal: true,
    goBack: action('goBack'),
    goNext: action('goNext'),
  },
};

export const FirstPage: Story = {
  args: {
    total: 150,
    isBack: false,
    isNext: true,
    showTotal: true,
    goBack: action('goBack'),
    goNext: action('goNext'),
  },
};

export const LastPage: Story = {
  args: {
    total: 150,
    isBack: true,
    isNext: false,
    showTotal: true,
    goBack: action('goBack'),
    goNext: action('goNext'),
  },
};

export const BothDisabled: Story = {
  args: {
    total: 0,
    isBack: false,
    isNext: false,
    showTotal: true,
    goBack: action('goBack'),
    goNext: action('goNext'),
  },
};

export const WithoutTotal: Story = {
  args: {
    isBack: true,
    isNext: true,
    showTotal: false,
    goBack: action('goBack'),
    goNext: action('goNext'),
  },
};

export const LargeTotal: Story = {
  args: {
    total: 999999,
    isBack: true,
    isNext: true,
    showTotal: true,
    goBack: action('goBack'),
    goNext: action('goNext'),
  },
};

export const NoTotal: Story = {
  args: {
    isBack: true,
    isNext: true,
    showTotal: true,
    goBack: action('goBack'),
    goNext: action('goNext'),
  },
};
