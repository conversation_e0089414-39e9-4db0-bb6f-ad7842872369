import type { Meta, StoryObj } from '@storybook/react-webpack5';
import { Input } from 'antd';
import { useState } from 'react';
import PasswordTooltip from './PasswordTooltip';

const meta = {
  title: 'Components/PasswordTooltip',
  component: PasswordTooltip,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    password: {
      control: 'text',
      description: 'Current password value',
    },
    requirements: {
      control: 'object',
      description: 'Custom password requirements',
    },
  },
} satisfies Meta<typeof PasswordTooltip>;

export default meta;
type Story = StoryObj<typeof meta>;

const InteractivePasswordInput = ({
  password: initialPassword = '',
  requirements,
}: any) => {
  const [password, setPassword] = useState(initialPassword);

  return (
    <div style={{ width: '300px' }}>
      <PasswordTooltip password={password} requirements={requirements}>
        <Input.Password
          placeholder="Nhập mật khẩu"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
        />
      </PasswordTooltip>
    </div>
  );
};

export const Default: Story = {
  render: (args) => <InteractivePasswordInput {...args} />,
  args: {
    password: '',
  },
};

export const WeakPassword: Story = {
  render: (args) => <InteractivePasswordInput {...args} />,
  args: {
    password: 'abc',
  },
};

export const MediumPassword: Story = {
  render: (args) => <InteractivePasswordInput {...args} />,
  args: {
    password: 'Password123',
  },
};

export const StrongPassword: Story = {
  render: (args) => <InteractivePasswordInput {...args} />,
  args: {
    password: 'MyStr0ng#Pass',
  },
};

export const CustomRequirements: Story = {
  render: (args) => <InteractivePasswordInput {...args} />,
  args: {
    password: 'test123',
    requirements: [
      {
        text: 'At least 6 characters',
        test: (password: string) => password.length >= 6,
      },
      {
        text: 'Contains numbers',
        test: (password: string) => /\d/.test(password),
      },
      {
        text: 'Contains letters',
        test: (password: string) => /[a-zA-Z]/.test(password),
      },
    ],
  },
};

export const EnglishRequirements: Story = {
  render: (args) => <InteractivePasswordInput {...args} />,
  args: {
    password: 'MyPassword123!',
    requirements: [
      {
        text: 'Contains uppercase and lowercase letters',
        test: (password: string) =>
          /[a-z]/.test(password) && /[A-Z]/.test(password),
      },
      {
        text: 'Contains special characters (!@#$%^&*)',
        test: (password: string) => /[!@#$%^&*]/.test(password),
      },
      {
        text: 'At least 8 characters long',
        test: (password: string) => password.length >= 8,
      },
      {
        text: 'Contains numbers',
        test: (password: string) => /\d/.test(password),
      },
    ],
  },
};
