import type { Meta, StoryObj } from '@storybook/react-webpack5';
import { Col, Input, Select } from 'antd';
import FormGroup from './form-group';

const meta = {
  title: 'Components/Form/FormGroup',
  component: FormGroup,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    title: {
      control: 'text',
      description: 'The section title (will be translated)',
    },
  },
} satisfies Meta<typeof FormGroup>;

export default meta;
type Story = StoryObj<typeof meta>;

export const WithSingleInput: Story = {
  args: {
    title: 'Basic Information',
    children: (
      <Col span={24}>
        <Input placeholder="Enter your name" />
      </Col>
    ),
  },
};

export const WithMultipleInputs: Story = {
  args: {
    title: 'Contact Details',
    children: (
      <>
        <Col span={12}>
          <Input placeholder="First Name" />
        </Col>
        <Col span={12}>
          <Input placeholder="Last Name" />
        </Col>
        <Col span={24}>
          <Input placeholder="Email Address" />
        </Col>
        <Col span={12}>
          <Input placeholder="Phone Number" />
        </Col>
        <Col span={12}>
          <Select placeholder="Country" style={{ width: '100%' }}>
            <Select.Option value="us">United States</Select.Option>
            <Select.Option value="ca">Canada</Select.Option>
            <Select.Option value="uk">United Kingdom</Select.Option>
          </Select>
        </Col>
      </>
    ),
  },
};

export const WithComplexContent: Story = {
  args: {
    title: 'Advanced Settings',
    children: (
      <>
        <Col span={8}>
          <Select placeholder="Status" style={{ width: '100%' }}>
            <Select.Option value="active">Active</Select.Option>
            <Select.Option value="inactive">Inactive</Select.Option>
          </Select>
        </Col>
        <Col span={8}>
          <Select placeholder="Priority" style={{ width: '100%' }}>
            <Select.Option value="high">High</Select.Option>
            <Select.Option value="medium">Medium</Select.Option>
            <Select.Option value="low">Low</Select.Option>
          </Select>
        </Col>
        <Col span={8}>
          <Input type="number" placeholder="Order" />
        </Col>
        <Col span={24}>
          <Input.TextArea placeholder="Description" rows={3} />
        </Col>
      </>
    ),
  },
};

export const EmptyGroup: Story = {
  args: {
    title: 'Empty Section',
    children: null,
  },
};
