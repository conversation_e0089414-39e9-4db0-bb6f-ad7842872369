import {
  Announcements,
  DndContext,
  DragEndEvent,
  DragMoveEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  DropAnimation,
  KeyboardSensor,
  MeasuringStrategy,
  Modifier,
  PointerSensor,
  UniqueIdentifier,
  closestCenter,
  defaultDropAnimation,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  arrayMove,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { useEffect, useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

import { CSS } from '@dnd-kit/utilities';
import { Modal, message } from 'antd';
import { useNavigateTenant } from '../../hooks';
import { deleteItem } from '../../pages/blog-category/api';
import ConsoleService from '../../services/console.service';
import { SortableTreeItem } from './components';
import { sortableTreeKeyboardCoordinates } from './keyboardCoordinates';
import type { FlattenedItem, SensorContext, TreeItems } from './types';
import {
  buildTree,
  flattenTree,
  getChildCount,
  getProjection,
  getSiblings,
  removeChildrenOf,
  removeItem,
  setProperty,
} from './utilities';

const measuring = {
  droppable: {
    strategy: MeasuringStrategy.Always,
  },
};

const dropAnimationConfig: DropAnimation = {
  keyframes({ transform }) {
    return [
      { opacity: 1, transform: CSS.Transform.toString(transform.initial) },
      {
        opacity: 0,
        transform: CSS.Transform.toString({
          ...transform.final,
          x: transform.final.x + 5,
          y: transform.final.y + 5,
        }),
      },
    ];
  },
  easing: 'ease-out',
  sideEffects({ active }) {
    active.node.animate([{ opacity: 0 }, { opacity: 1 }], {
      duration: defaultDropAnimation.duration,
      easing: defaultDropAnimation.easing,
    });
  },
};

interface Props {
  collapsible?: boolean;
  items: TreeItems;
  indentationWidth?: number;
  indicator?: boolean;
  removable?: boolean;

  onMoveNode: (
    activeId: UniqueIdentifier,
    overId: UniqueIdentifier,
    position: string,
  ) => void;
  onMoveNodeRoot: (activeId: UniqueIdentifier, position: number) => void;
  onUpdatePosition: (categoryId: number, targetId: number) => void;
}

export function SortableTree({
  collapsible,
  items,
  indicator = false,
  indentationWidth = 50,
  removable,
  onMoveNode,
  onUpdatePosition,
  onMoveNodeRoot,
}: Props) {
  const logger = ConsoleService.register('SortableTree');
  // Đảm bảo items luôn là mảng hợp lệ
  const safeItems = Array.isArray(items) ? items : [];
  const navigate = useNavigateTenant();

  const [treeItems, setTreeItems] = useState(() => safeItems);
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
  const [overId, setOverId] = useState<UniqueIdentifier | null>(null);
  const [offsetLeft, setOffsetLeft] = useState(0);
  const [currentPosition, setCurrentPosition] = useState<{
    parentId: UniqueIdentifier | null;
    overId: UniqueIdentifier;
  } | null>(null);

  // Update treeItems when items prop changes
  useEffect(() => {
    setTreeItems(Array.isArray(items) ? items : []);
  }, [items]);

  const flattenedItems = useMemo(() => {
    if (!treeItems.length) return [];

    const flattenedTree = flattenTree(treeItems);
    const collapsedItems = flattenedTree.reduce<UniqueIdentifier[]>(
      (acc, { children, collapsed, id }) =>
        collapsed && children.length ? [...acc, id] : acc,
      [],
    );

    return removeChildrenOf(
      flattenedTree,
      activeId != null ? [activeId, ...collapsedItems] : collapsedItems,
    );
  }, [activeId, treeItems]);
  const projected =
    activeId && overId
      ? getProjection(
          flattenedItems,
          activeId,
          overId,
          offsetLeft,
          indentationWidth,
        )
      : null;
  const sensorContext: SensorContext = useRef({
    items: flattenedItems,
    offset: offsetLeft,
  });
  const [coordinateGetter] = useState(() =>
    sortableTreeKeyboardCoordinates(sensorContext, indicator, indentationWidth),
  );
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter,
    }),
  );

  const sortedIds = useMemo(
    () => flattenedItems.map(({ id }) => id),
    [flattenedItems],
  );
  const activeItem = activeId
    ? flattenedItems.find(({ id }) => id === activeId)
    : null;

  useEffect(() => {
    sensorContext.current = {
      items: flattenedItems,
      offset: offsetLeft,
    };
  }, [flattenedItems, offsetLeft]);

  const announcements: Announcements = {
    onDragStart({ active }) {
      console.log('onDragStart', active.id);
      return `Picked up ${active.id}.`;
    },
    onDragMove({ active, over }) {
      //console.log('onDragMove', active.id, over?.id);
      return getMovementAnnouncement('onDragMove', active.id, over?.id);
    },
    onDragOver({ active, over }) {
      //console.log('onDragOver', active.id, over?.id);
      return getMovementAnnouncement('onDragOver', active.id, over?.id);
    },
    onDragEnd({ active, over }) {
      //console.log('onDragEnd', active.id, over?.id);
      return getMovementAnnouncement('onDragEnd', active.id, over?.id);
    },
    onDragCancel({ active }) {
      //console.log('onDragCancel', active.id);
      return `Moving was cancelled. ${active.id} was dropped in its original position.`;
    },
  };

  // Nếu không có dữ liệu, không render gì cả
  if (!flattenedItems.length) {
    return null;
  }

  return (
    <DndContext
      accessibility={{ announcements }}
      sensors={sensors}
      collisionDetection={closestCenter}
      measuring={measuring}
      onDragStart={handleDragStart}
      onDragMove={handleDragMove}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
      onDragCancel={handleDragCancel}
    >
      <SortableContext items={sortedIds} strategy={verticalListSortingStrategy}>
        {flattenedItems.map(
          ({ id, children, collapsed, depth, name, parentId, position }) => (
            <SortableTreeItem
              key={id}
              id={id}
              value={
                typeof name === 'string' ? name : name?.name || String(name)
              }
              depth={id === activeId && projected ? projected.depth : depth}
              indentationWidth={indentationWidth}
              indicator={indicator}
              collapsed={Boolean(collapsed && children.length)}
              position={position}
              onCollapse={
                collapsible && children.length
                  ? () => handleCollapse(id)
                  : undefined
              }
              onRemove={removable ? () => handleRemove(id) : undefined}
              onEdit={() => {
                // Xử lý sự kiện Edit - mở trang chỉnh sửa cho item
                navigate(`blog-category/${id}`);
              }}
            />
          ),
        )}
        {createPortal(
          <DragOverlay
            dropAnimation={dropAnimationConfig}
            modifiers={indicator ? [adjustTranslate] : undefined}
          >
            {activeId && activeItem ? (
              <SortableTreeItem
                id={activeId}
                depth={activeItem.depth}
                clone
                childCount={getChildCount(treeItems, activeId) + 1}
                value={
                  typeof activeItem.name === 'string'
                    ? activeItem.name
                    : activeItem.name?.name || String(activeItem.name)
                }
                indentationWidth={indentationWidth}
              />
            ) : null}
          </DragOverlay>,
          document.body,
        )}
      </SortableContext>
    </DndContext>
  );

  function handleDragStart({ active: { id: activeId } }: DragStartEvent) {
    setActiveId(activeId);
    setOverId(activeId);

    const activeItem = flattenedItems.find(({ id }) => id === activeId);

    if (activeItem) {
      setCurrentPosition({
        parentId: activeItem.parentId,
        overId: activeId,
      });
    }

    document.body.style.setProperty('cursor', 'grabbing');
  }

  function handleDragMove({ delta }: DragMoveEvent) {
    setOffsetLeft(delta.x);
  }

  function handleDragOver({ over }: DragOverEvent) {
    setOverId(over?.id ?? null);
  }
  function getNestedSetPosition(parentId, overId) {
    // Lấy tất cả node con trực tiếp của parent được chỉ định
    const siblings = treeItems.filter((item) => item.id === parentId);

    // Sắp xếp siblings theo thứ tự lft
    //const sortedSiblings = siblings.sort((a, b) => a.lft - b.lft);
    const sortedSiblings = siblings.length
      ? siblings[0].children.sort((a, b) => a.position - b.position)
      : [];

    // Tìm vị trí của node mục tiêu trong số các siblings
    const overPosition = sortedSiblings.findIndex((item) => item.id === overId);

    // Trả về position - nếu không tìm thấy thì đặt vào cuối
    const position = overPosition !== -1 ? overPosition : siblings.length;
    logger('[getNestedSetPosition]', { parentId, overId, position, overId });
    return position;
  }
  /**
   * Tìm tất cả các con cháu (descendants) của một parentId cho trước
   * trong một mảng phẳng các item bằng phương pháp đệ quy.
   *
   * @param {Array<Object>} items - Mảng phẳng các item. Mỗi item phải có 'id' và 'parent_id'.
   * @param {number|string|null} parentId - ID của node cha cần tìm con cháu.
   *                                         Sử dụng null nếu parent_id của item gốc là null.
   * @returns {Array<Object>} Mảng chứa tất cả các item con cháu.
   */
  function findAllDescendantsRecursive(items, parentId) {
    // 1. Tìm các con TRỰC TIẾP của parentId hiện tại
    const directChildren = items.filter((item) => item.parent_id === parentId); // <<< SỬA LỖI LỌC

    // 2. Khởi tạo danh sách kết quả cho cấp này VỚI các con trực tiếp
    let allDescendants = [...directChildren]; // Bắt đầu với các con trực tiếp tìm được

    // 3. Với MỖI con trực tiếp, tìm tiếp các con cháu CỦA NÓ (đệ quy)
    directChildren.forEach((child) => {
      // <<< SỬA LỖI: Lặp qua directChildren
      // Gọi đệ quy hàm cho ID của con hiện tại, sử dụng cùng danh sách phẳng 'items' ban đầu
      const grandchildrenAndBeyond = findAllDescendantsRecursive(
        items,
        child.id,
      ); // <<< SỬA LỖI GỌI ĐỆ QUY

      // Thêm các con cháu (và xa hơn) tìm được của 'child' này vào danh sách kết quả tổng
      if (grandchildrenAndBeyond.length > 0) {
        allDescendants = allDescendants.concat(grandchildrenAndBeyond);
        // Hoặc: allDescendants.push(...grandchildrenAndBeyond); // Có thể hiệu quả hơn cho mảng lớn
      }
    });

    // Trả về danh sách tổng hợp các con cháu tìm được ở tất cả các cấp dưới parentId
    return allDescendants;
  }
  function getPosition(newItems: any, parentId: any, overId: any) {
    // Lấy tất cả node con trực tiếp của parent được chỉ định
    const siblings = getSiblings(newItems, parentId);

    const sortedSiblings = siblings.length
      ? siblings[0].children.sort((a, b) => a.position - b.position)
      : [];

    // Tìm vị trí của node mục tiêu trong số các siblings
    const overPosition = sortedSiblings.findIndex((item) => item.id === overId);

    // Trả về position - nếu không tìm thấy thì đặt vào cuối
    const position = overPosition !== -1 ? overPosition : siblings.length;
    logger('[getNestedSetPosition]', { parentId, overId, position, overId });
    return position;
  }

  /**
   * Tính toán position mới khi di chuyển một node lên trên node khác
   * @param {Object} treeData - Cấu trúc cây
   * @param {number} moveNodeId - ID của node cần di chuyển
   * @param {number} targetNodeId - ID của node mục tiêu (di chuyển lên trên node này)
   * @return {Object} Thông tin về vị trí mới {parentId, position}
   */
  function calculateNewPosition(treeData, moveNodeId, targetNodeId) {
    // Tìm node cha chứa cả 2 node
    const findParentAndNodes = (tree, moveId, targetId, parent = null) => {
      // Kiểm tra xem node hiện tại có chứa cả 2 node cần tìm không
      const moveNodeIndex =
        tree.children?.findIndex((child) => child.id === moveId) ?? -1;
      const targetNodeIndex =
        tree.children?.findIndex((child) => child.id === targetId) ?? -1;

      // Nếu tìm thấy cả 2 node trong danh sách con trực tiếp
      if (moveNodeIndex !== -1 && targetNodeIndex !== -1) {
        return {
          parent: tree,
          moveNodeIndex,
          targetNodeIndex,
        };
      }

      // Tìm kiếm đệ quy trong các node con
      if (tree.children) {
        for (const child of tree.children) {
          const result = findParentAndNodes(child, moveId, targetId, tree);
          if (result) return result;
        }
      }

      return null;
    };

    // Duyệt cây từ node gốc
    const traverseTree = (node) => {
      const result = findParentAndNodes(node, moveNodeId, targetNodeId);
      if (result) return result;

      if (node.children) {
        for (const child of node.children) {
          const found = traverseTree(child);
          if (found) return found;
        }
      }

      return null;
    };

    // Tìm thông tin về vị trí
    const result = traverseTree(treeData);

    if (!result) {
      console.error('Không tìm thấy các node trong cây');
      return null;
    }

    const { parent, targetNodeIndex } = result;

    // Trả về thông tin vị trí mới
    return {
      parentId: parent.id,
      position: targetNodeIndex, // Vị trí của node mục tiêu
    };
  }

  function handleDragEnd({ active, over, collisions, delta }: DragEndEvent) {
    resetState();

    logger('[handleDragEnd]', { active, over, collisions, delta });

    if (!projected || !over) return;

    const { depth, parentId } = projected;

    // Clone lại danh sách các item đã được làm phẳng
    const flattenedItems: FlattenedItem[] = JSON.parse(
      JSON.stringify(flattenTree(treeItems)),
    );

    const activeIndex = flattenedItems.findIndex(({ id }) => id === active.id);
    const overIndex = flattenedItems.findIndex(({ id }) => id === over.id);

    logger('[handleDragEnd]', { activeIndex, overIndex });

    if (activeIndex === -1) return;

    const activeItem = flattenedItems[activeIndex];
    const prevDepth = activeItem.depth;
    const prevParentId = activeItem.parentId;

    // Cập nhật thông tin depth và parentId cho item đang kéo
    flattenedItems[activeIndex] = { ...activeItem, depth, parentId };

    // Sắp xếp lại mảng sau khi đã cập nhật thông tin
    const sortedItems = arrayMove(flattenedItems, activeIndex, overIndex);
    // Xây dựng lại cấu trúc cây từ mảng đã làm phẳng
    const newItems = buildTree(sortedItems);
    setTreeItems(newItems);
    // Xác định vị trí chính xác

    // Kiểm tra xem có thay đổi parentId hay không
    const relationshipChanged = prevParentId !== parentId;

    if (relationshipChanged) {
      console.log(
        `Quan hệ cha-con đã thay đổi: ${active.id} từ parent ${prevParentId} sang ${parentId}, depth: ${depth}`,
      );
      const nestedPosition = getPosition(newItems, parentId, over.id);
      logger('[handleDragEnd]', {
        activeIndex,
        overIndex,
        relationshipChanged,
        parentId,
        nestedPosition,
      });
      if (parentId === null) {
        // tìm position của active.id
        // overIndex là position
        onMoveNodeRoot(active.id, overIndex);
      } else {
        onMoveNode(active.id, parentId, nestedPosition);
      }
    } else {
      // Trường hợp di chuyển cùng độ sâu (trước/sau)
      if (overIndex < activeIndex) {
        logger('[Trường hợp di chuyển cùng độ sâu]', {
          activeIndex,
          overIndex,
          relationshipChanged,
          parentId,
        });
        // const nestedPosition = calculateNewPosition(
        //   treeItems,
        //   active.id,
        //   over.id,
        // );
        onUpdatePosition(active.id, over.id);
      }
    }
  }
  function handleDragCancel() {
    resetState();
  }

  function resetState() {
    setOverId(null);
    setActiveId(null);
    setOffsetLeft(0);
    setCurrentPosition(null);

    document.body.style.setProperty('cursor', '');
  }

  function handleRemove(id: UniqueIdentifier) {
    Modal.confirm({
      title: 'Xác nhận xóa danh mục',
      content: 'Bạn có chắc chắn muốn xóa danh mục này không?',
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      onOk: async () => {
        try {
          // Gọi API xóa danh mục từ api.ts
          const response = await deleteItem(id.toString());
          if (response.status.success) {
            // Xóa item khỏi state local
            setTreeItems((items) => removeItem(items, id));
            message.success('Xóa danh mục thành công');
          } else {
            message.error(
              response.status.message || 'Có lỗi xảy ra khi xóa danh mục',
            );
          }
        } catch (error) {
          console.error('Error:', error);
          message.error('Có lỗi xảy ra khi xóa danh mục');
        }
      },
    });
  }

  function handleCollapse(id: UniqueIdentifier) {
    setTreeItems((items) =>
      setProperty(items, id, 'collapsed', (value) => {
        return !value;
      }),
    );
  }

  function getMovementAnnouncement(
    eventName: string,
    activeId: UniqueIdentifier,
    overId?: UniqueIdentifier,
  ) {
    if (overId && projected) {
      if (eventName !== 'onDragEnd') {
        if (
          currentPosition &&
          projected.parentId === currentPosition.parentId &&
          overId === currentPosition.overId
        ) {
          return;
        } else {
          setCurrentPosition({
            parentId: projected.parentId,
            overId,
          });
        }
      }

      const clonedItems: FlattenedItem[] = JSON.parse(
        JSON.stringify(flattenTree(treeItems)),
      );
      const overIndex = clonedItems.findIndex(({ id }) => id === overId);
      const activeIndex = clonedItems.findIndex(({ id }) => id === activeId);
      const sortedItems = arrayMove(clonedItems, activeIndex, overIndex);

      const previousItem = sortedItems[overIndex - 1];

      let announcement;
      const movedVerb = eventName === 'onDragEnd' ? 'dropped' : 'moved';
      const nestedVerb = eventName === 'onDragEnd' ? 'dropped' : 'nested';

      if (!previousItem) {
        const nextItem = sortedItems[overIndex + 1];
        announcement = `${activeId} was ${movedVerb} before ${nextItem.id}.`;
      } else {
        if (projected.depth > previousItem.depth) {
          announcement = `${activeId} was ${nestedVerb} under ${previousItem.id}.`;
        } else {
          let previousSibling: FlattenedItem | undefined = previousItem;
          while (previousSibling && projected.depth < previousSibling.depth) {
            const parentId: UniqueIdentifier | null = previousSibling.parentId;
            previousSibling = sortedItems.find(({ id }) => id === parentId);
          }

          if (previousSibling) {
            announcement = `${activeId} was ${movedVerb} after ${previousSibling.id}.`;
          }
        }
      }

      return announcement;
    }

    return;
  }
}

const adjustTranslate: Modifier = ({ transform }) => {
  return {
    ...transform,
    y: transform.y - 25,
  };
};
