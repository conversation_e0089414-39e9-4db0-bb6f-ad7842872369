import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { WebsiteResponse, WebsiteSelectorItem } from './type';
import { websiteApi } from './api';
import { message } from 'antd';

export interface WebsiteStoreState {
  // State
  websites: WebsiteSelectorItem[];
  selectedWebsiteId: number | null;
  selectedWebsite: WebsiteResponse | null;
  loading: boolean;

  // Actions
  setLoading: (loading: boolean) => void;
  setWebsites: (websites: WebsiteSelectorItem[]) => void;
  setSelectedWebsiteId: (id: number | null) => void;
  selectWebsite: (id: number) => void;
  loadWebsites: () => Promise<void>;

  // Computed
  getSelectedWebsite: () => WebsiteSelectorItem | null;
}

const useWebsiteStore = create<WebsiteStoreState>()(
  persist(
    (set, get) => ({
      // Initial state
      websites: [],
      selectedWebsiteId: null,
      selectedWebsite: null,
      loading: false,

      // Actions
      setLoading: (loading: boolean) => {
        set({ loading });
      },

      setWebsites: (websites: WebsiteSelectorItem[]) => {
        set({ websites });
      },

      setSelectedWebsiteId: (id: number | null) => {
        const state = get();
        const selectedWebsite =
          state.websites.find((w) => w.id === id)?.website || null;

        set({
          selectedWebsiteId: id,
          selectedWebsite,
          websites: state.websites.map((site) => ({
            ...site,
            isSelected: site.id === id,
          })),
        });
      },

      selectWebsite: (id: number) => {
        const state = get();
        const website = state.websites.find((w) => w.id === id);

        if (website) {
          state.setSelectedWebsiteId(id);

          // Trigger axios interceptor update by setting a flag
          if (typeof window !== 'undefined') {
            window.dispatchEvent(
              new CustomEvent('websiteChanged', {
                detail: { websiteId: id, website: website.website },
              }),
            );
          }
        }
      },

      loadWebsites: async () => {
        const state = get();
        state.setLoading(true);

        try {
          const response = await websiteApi.getWebsites({
            limit: 50,
            sort_by: 'updated_at',
            sort_order: 'desc',
          });

          const selectorItems: WebsiteSelectorItem[] = response.websites.map(
            (website) => ({
              id: website.id,
              name: website.name,
              role: 'Chủ sở hữu',
              thumbnail:
                website.site_logo ||
                website.favicon ||
                '/default-website-icon.png',
              isSelected: state.selectedWebsiteId === website.id,
              website,
            }),
          );

          state.setWebsites(selectorItems);

          // Auto-select first website if none selected
          if (!state.selectedWebsiteId && selectorItems.length > 0) {
            state.selectWebsite(selectorItems[0].id);
          }
        } catch (error) {
          console.error('Failed to load websites:', error);
          message.error('Không thể tải danh sách website');
        } finally {
          state.setLoading(false);
        }
      },

      // Computed
      getSelectedWebsite: () => {
        const state = get();
        return state.websites.find((site) => site.isSelected) || null;
      },
    }),
    {
      name: 'website-selector-storage',
      partialize: (state) => ({
        selectedWebsiteId: state.selectedWebsiteId,
        selectedWebsite: state.selectedWebsite,
      }),
    },
  ),
);

export default useWebsiteStore;
