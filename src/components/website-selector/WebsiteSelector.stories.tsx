import type { Meta, StoryObj } from '@storybook/react-webpack5';
import WebsiteSelector from './index';
import { websiteApi } from './api';
import { WebsiteListResponse, WebsiteResponse, WebsiteStatus } from './type';

const meta = {
  title: 'Components/WebsiteSelector',
  component: WebsiteSelector,
  parameters: {
    layout: 'centered',
    mockData: {
      websiteApi: websiteApi,
    },
  },
  tags: ['autodocs'],
} satisfies Meta<typeof WebsiteSelector>;

export default meta;
type Story = StoryObj<typeof meta>;

// Mock data
const mockWebsites: WebsiteResponse[] = [
  {
    id: 1,
    name: 'My Blog Website',
    domain: 'myblog.com',
    status: WebsiteStatus.ACTIVE,
    tenant_id: 1,
    site_logo: 'https://via.placeholder.com/48/4CAF50/FFFFFF?text=MB',
    favicon: 'https://via.placeholder.com/16/4CAF50',
    description: 'Personal blog about technology',
    active_theme: 'modern',
    custom_css: '',
    custom_js: '',
    google_analytics_id: 'GA-*********',
    google_tag_manager_id: '',
    facebook_pixel_id: '',
    social_media: {},
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z',
  },
  {
    id: 2,
    name: 'E-commerce Store',
    domain: 'store.example.com',
    status: WebsiteStatus.ACTIVE,
    tenant_id: 1,
    site_logo: 'https://via.placeholder.com/48/2196F3/FFFFFF?text=ES',
    favicon: 'https://via.placeholder.com/16/2196F3',
    description: 'Online shopping platform',
    active_theme: 'shop',
    custom_css: '',
    custom_js: '',
    google_analytics_id: 'GA-*********',
    google_tag_manager_id: 'GTM-ABCDEF',
    facebook_pixel_id: 'FB-*********',
    social_media: {
      facebook: 'https://facebook.com/mystore',
      twitter: 'https://twitter.com/mystore',
    },
    created_at: '2024-01-05T00:00:00Z',
    updated_at: '2024-01-20T00:00:00Z',
  },
  {
    id: 3,
    name: 'Corporate Website',
    domain: 'company.com',
    status: WebsiteStatus.INACTIVE,
    tenant_id: 1,
    site_logo: 'https://via.placeholder.com/48/FF9800/FFFFFF?text=CW',
    favicon: 'https://via.placeholder.com/16/FF9800',
    description: 'Company corporate website',
    active_theme: 'corporate',
    custom_css: '.header { background: #333; }',
    custom_js: 'console.log(&quot;Corporate site loaded&quot;);',
    google_analytics_id: '',
    google_tag_manager_id: '',
    facebook_pixel_id: '',
    social_media: {},
    created_at: '2024-01-10T00:00:00Z',
    updated_at: '2024-01-10T00:00:00Z',
  },
];

const mockListResponse: WebsiteListResponse = {
  websites: mockWebsites,
  cursor: {
    next_cursor: null,
    prev_cursor: null,
    has_next: false,
    has_prev: false,
  },
  total: mockWebsites.length,
};

// Mock the API for Storybook
const mockWebsiteApi = {
  ...websiteApi,
  getWebsites: async () => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return mockListResponse;
  },
};

export const Default: Story = {
  parameters: {
    mockData: {
      websiteApi: mockWebsiteApi,
    },
  },
};

export const Loading: Story = {
  parameters: {
    mockData: {
      websiteApi: {
        ...websiteApi,
        getWebsites: async () => {
          // Never resolve to show loading state
          return new Promise(() => {});
        },
      },
    },
  },
};

export const Empty: Story = {
  parameters: {
    mockData: {
      websiteApi: {
        ...websiteApi,
        getWebsites: async () => ({
          websites: [],
          cursor: {
            next_cursor: null,
            prev_cursor: null,
            has_next: false,
            has_prev: false,
          },
          total: 0,
        }),
      },
    },
  },
};

export const SingleWebsite: Story = {
  parameters: {
    mockData: {
      websiteApi: {
        ...websiteApi,
        getWebsites: async () => ({
          websites: [mockWebsites[0]],
          cursor: {
            next_cursor: null,
            prev_cursor: null,
            has_next: false,
            has_prev: false,
          },
          total: 1,
        }),
      },
    },
  },
};

export const WithError: Story = {
  parameters: {
    mockData: {
      websiteApi: {
        ...websiteApi,
        getWebsites: async () => {
          throw new Error('Failed to load websites');
        },
      },
    },
  },
};
