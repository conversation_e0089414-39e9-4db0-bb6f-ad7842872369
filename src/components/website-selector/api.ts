import { ApiResponse, apiService } from '../../services/api.service';
import {
  WebsiteCreateRequest,
  WebsiteUpdateRequest,
  WebsiteResponse,
  WebsiteListResponse,
  WebsiteFilter,
} from './type';

const baseUrl = '/api/cms/v1/websites';

export const websiteApi = {
  // Get all websites for current user/tenant
  getWebsites: async (params?: WebsiteFilter): Promise<WebsiteListResponse> => {
    const response = await apiService.get<ApiResponse<WebsiteListResponse>>(
      `${baseUrl}/me/list`,
      { params },
    );
    return response.data.data;
  },

  // Get single website by ID
  getWebsite: async (id: number): Promise<WebsiteResponse> => {
    const response = await apiService.get<ApiResponse<WebsiteResponse>>(
      `${baseUrl}/${id}`,
    );
    return response.data.data;
  },

  // Create new website
  createWebsite: async (
    data: WebsiteCreateRequest,
  ): Promise<WebsiteResponse> => {
    const response = await apiService.post<ApiResponse<WebsiteResponse>>(
      baseUrl,
      data,
    );
    return response.data.data;
  },

  // Update website
  updateWebsite: async (
    id: number,
    data: WebsiteUpdateRequest,
  ): Promise<WebsiteResponse> => {
    const response = await apiService.put<ApiResponse<WebsiteResponse>>(
      `${baseUrl}/${id}`,
      data,
    );
    return response.data.data;
  },

  // Delete website
  deleteWebsite: async (id: number): Promise<void> => {
    await apiService.delete(`${baseUrl}/${id}`);
  },
};
