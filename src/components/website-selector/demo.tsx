import React, { useEffect } from 'react';
import { Button, Space, Typography } from 'antd';
import useWebsiteStore from './store';
import websiteService from '../../services/website.service';
import { websiteApi } from './api';

const { Title, Text } = Typography;

const WebsiteSelectorDemo: React.FC = () => {
  const {
    websites,
    loading,
    selectedWebsiteId,
    loadWebsites,
    selectWebsite,
    getSelectedWebsite,
  } = useWebsiteStore();

  const selectedWebsite = getSelectedWebsite();

  useEffect(() => {
    loadWebsites();
  }, [loadWebsites]);

  const handleTestApiCall = async () => {
    try {
      console.log('Current website ID:', websiteService.getCurrentWebsiteId());
      const response = await websiteApi.getWebsites({ limit: 10 });
      console.log('API Response:', response);
    } catch (error) {
      console.error('API Error:', error);
    }
  };

  return (
    <div style={{ padding: 20 }}>
      <Title level={3}>Website Selector Demo</Title>

      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        <div>
          <Text strong>Loading: </Text>
          <Text>{loading ? 'Yes' : 'No'}</Text>
        </div>

        <div>
          <Text strong>Selected Website ID: </Text>
          <Text>{selectedWebsiteId || 'None'}</Text>
        </div>

        <div>
          <Text strong>Selected Website: </Text>
          <Text>{selectedWebsite?.name || 'None'}</Text>
        </div>

        <div>
          <Text strong>Total Websites: </Text>
          <Text>{websites.length}</Text>
        </div>

        <div>
          <Text strong>Website Service Current ID: </Text>
          <Text>{websiteService.getCurrentWebsiteId() || 'None'}</Text>
        </div>

        <Space>
          <Button onClick={() => loadWebsites()}>Reload Websites</Button>

          <Button onClick={handleTestApiCall}>
            Test API Call (Check Console)
          </Button>

          {websites.length > 0 && (
            <Button
              onClick={() => selectWebsite(websites[0].id)}
              type="primary"
            >
              Select First Website
            </Button>
          )}
        </Space>

        <div>
          <Title level={4}>Websites List:</Title>
          {websites.map((website) => (
            <div key={website.id} style={{ marginBottom: 8 }}>
              <Button
                type={website.isSelected ? 'primary' : 'default'}
                onClick={() => selectWebsite(website.id)}
                style={{ marginRight: 8 }}
              >
                {website.name} (ID: {website.id})
              </Button>
            </div>
          ))}
        </div>
      </Space>
    </div>
  );
};

export default WebsiteSelectorDemo;
