import {
  CheckOutlined,
  DownOutlined,
  PlusOutlined,
  SearchOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Button,
  Dropdown,
  Input,
  List,
  Typography,
  message,
  Spin,
} from 'antd';
import React, { useState, useEffect } from 'react';
import { WebsiteSelectorItem } from './type';
import useWebsiteStore from './store';
import websiteService from '../../services/website.service';
import './style.scss';

const { Text } = Typography;

const WebsiteSelector: React.FC = () => {
  const [searchValue, setSearchValue] = useState('');
  const [dropdownVisible, setDropdownVisible] = useState(false);

  // Use store
  const {
    websites,
    loading,
    selectedWebsiteId,
    loadWebsites,
    selectWebsite,
    getSelectedWebsite,
  } = useWebsiteStore();

  const selectedWebsite = getSelectedWebsite();

  useEffect(() => {
    loadWebsites();
  }, [loadWebsites]);

  // Update websiteService when selectedWebsiteId changes
  useEffect(() => {
    websiteService.setCurrentWebsiteId(selectedWebsiteId);
  }, [selectedWebsiteId]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const handleSelectWebsite = (id: number) => {
    selectWebsite(id);
    setDropdownVisible(false);
  };

  const handleCreateNew = () => {
    // TODO: Navigate to website creation page
    console.log('Tạo trang web mới');
    setDropdownVisible(false);
  };

  const handleViewAll = () => {
    // TODO: Navigate to website management page
    console.log('Xem tất cả trang web');
    setDropdownVisible(false);
  };

  const filteredWebsites = websites.filter((site) =>
    site.name.toLowerCase().includes(searchValue.toLowerCase()),
  );

  // Define the dropdown content render function
  const renderDropdownContent = () => (
    <div className="website-dropdown">
      <div className="search-section">
        <Input
          prefix={<SearchOutlined />}
          placeholder="Tìm kiếm tên trang web..."
          value={searchValue}
          onChange={handleSearchChange}
          className="search-input"
          disabled={loading}
        />
        <Button
          type="primary"
          icon={<PlusOutlined />}
          className="create-button"
          onClick={handleCreateNew}
          disabled={loading}
        >
          Tạo trang web mới
        </Button>
      </div>

      {loading ? (
        <div
          className="loading-container"
          style={{ textAlign: 'center', padding: '20px' }}
        >
          <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
          <div style={{ marginTop: '8px' }}>Đang tải...</div>
        </div>
      ) : filteredWebsites.length === 0 ? (
        <div
          className="empty-state"
          style={{ textAlign: 'center', padding: '20px' }}
        >
          <Text type="secondary">
            {searchValue
              ? 'Không tìm thấy website phù hợp'
              : 'Chưa có website nào'}
          </Text>
        </div>
      ) : (
        <List
          className="website-list"
          itemLayout="horizontal"
          dataSource={filteredWebsites}
          renderItem={(site) => (
            <List.Item
              className={site.isSelected ? 'selected' : ''}
              onClick={() => handleSelectWebsite(site.id)}
            >
              <List.Item.Meta
                avatar={
                  <Avatar shape="square" size={48} src={site.thumbnail} />
                }
                title={site.name}
                description={`Vai trò: ${site.role}`}
              />
              {site.isSelected && <CheckOutlined className="check-icon" />}
            </List.Item>
          )}
        />
      )}

      <div className="view-all">
        <Button type="link" onClick={handleViewAll} disabled={loading}>
          Đến Tất cả trang web
        </Button>
      </div>
    </div>
  );

  return (
    <Dropdown
      popupRender={renderDropdownContent}
      open={dropdownVisible}
      onOpenChange={setDropdownVisible}
      trigger={['click']}
      placement="bottomLeft"
      arrow
      disabled={loading && websites.length === 0}
    >
      <div className="website-selector">
        {loading && websites.length === 0 ? (
          <>
            <Spin
              indicator={<LoadingOutlined style={{ fontSize: 14 }} spin />}
            />
            <span className="website-name">Đang tải...</span>
          </>
        ) : selectedWebsite ? (
          <>
            <Avatar shape="square" size={32} src={selectedWebsite.thumbnail} />
            <span className="website-name">{selectedWebsite.name}</span>
            <DownOutlined className="dropdown-icon" />
          </>
        ) : (
          <>
            <Avatar shape="square" size={32} icon={<PlusOutlined />} />
            <span className="website-name">Chọn website</span>
            <DownOutlined className="dropdown-icon" />
          </>
        )}
      </div>
    </Dropdown>
  );
};

export default WebsiteSelector;
