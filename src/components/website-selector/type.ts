import { CursorPagination, CursorResponse } from '../../pages/blog/type';

// API DTOs matching backend
export interface WebsiteCreateRequest {
  name: string;
  domain?: string;
  subdomain?: string;
  description?: string;
  active_theme?: string;
  custom_css?: string;
  custom_js?: string;
  site_logo?: string;
  favicon?: string;
  timezone?: string;
  language?: string;
  social_media?: Record<string, any>;
}

export interface WebsiteUpdateRequest {
  name?: string;
  domain?: string;
  subdomain?: string;
  description?: string;
  active_theme?: string;
  custom_css?: string;
  custom_js?: string;
  site_logo?: string;
  favicon?: string;
  timezone?: string;
  language?: string;
  google_analytics_id?: string;
  google_tag_manager_id?: string;
  facebook_pixel_id?: string;
  social_media?: Record<string, any>;
  status?: WebsiteStatus;
}

export enum WebsiteStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

export interface WebsiteResponse {
  id: number;
  tenant_id: number;
  name: string;
  domain?: string;
  subdomain?: string;
  description?: string;
  active_theme?: string;
  custom_css?: string;
  custom_js?: string;
  site_logo?: string;
  favicon?: string;
  timezone: string;
  language: string;
  google_analytics_id?: string;
  google_tag_manager_id?: string;
  facebook_pixel_id?: string;
  social_media?: Record<string, any>;
  status: WebsiteStatus;
  created_at: string;
  updated_at: string;
}

export interface WebsiteListResponse {
  websites: WebsiteResponse[];
  pagination?: CursorResponse;
}

export interface WebsiteFilter extends CursorPagination {
  name?: string;
  domain?: string;
  status?: WebsiteStatus;
  language?: string;
  sort_by?: 'id' | 'name' | 'domain' | 'created_at' | 'updated_at';
  sort_order?: 'asc' | 'desc';
}

// Component-specific types
export interface WebsiteSelectorItem {
  id: number;
  name: string;
  role?: string;
  thumbnail?: string;
  isSelected: boolean;
  website?: WebsiteResponse;
}
