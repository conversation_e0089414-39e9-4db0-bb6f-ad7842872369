import { Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuthStore } from '../pages/auth/auth';
import OnboardingService from '../pages/onboarding/components/onboarding-service';
import { OnboardingStep } from '../pages/onboarding/type';
import { tenantOnboardingApi } from '../pages/tenant-onboarding/api';
import { OnboardingStatus } from '../pages/tenant-onboarding/type';
import ConsoleService from '../services/console.service';
import tenantService from '../services/tenant.service';

const logger = ConsoleService.register('ProtectedRoute');

const ProtectedRoute: React.FC = () => {
  const { isLoggedIn } = useAuthStore();
  const location = useLocation();
  const [onboardingStatus, setOnboardingStatus] =
    useState<OnboardingStatus | null>(null);
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(true);
  const [currentStep, setCurrentStep] = useState<string | null>(null);

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      if (!isLoggedIn) {
        setIsCheckingOnboarding(false);
        return;
      }

      try {
        // Check onboarding status using new API
        const response = await tenantOnboardingApi.getOnboardingStatus();

        logger('Onboarding status response:', response);

        // Update OnboardingService localStorage with the API response
        if (response.data) {
          const { step, onboarding_step, current_step, is_completed } =
            response.data;

          // Determine the step value from different possible fields
          const stepValue = step || onboarding_step || current_step;

          // Map API step values to OnboardingStep enum
          if (stepValue) {
            let onboardingStepEnum: OnboardingStep;
            switch (stepValue.toLowerCase()) {
              case 'organization_setup':
              case 'organization':
              case 'tenant':
              case 'tenant_setup':
              case 'create_tenant':
                onboardingStepEnum = OnboardingStep.ORGANIZATION_SETUP;
                break;
              case 'website_creation':
              case 'website':
              case 'website_setup':
              case 'create_website':
                onboardingStepEnum = OnboardingStep.WEBSITE_CREATION;
                break;
              case 'completed':
              case 'complete':
                onboardingStepEnum = OnboardingStep.COMPLETED;
                break;
              default:
                onboardingStepEnum = OnboardingStep.ORGANIZATION_SETUP;
            }

            OnboardingService.setCurrentStep(onboardingStepEnum);
          }

          // Update completion status
          if (is_completed !== undefined && is_completed) {
            OnboardingService.markAsCompleted();
          }
        }

        if (response.status.success && response.data) {
          const data = response.data as any;

          // Set current step if available (from step field)
          const step = data.step || data.current_step;
          if (step) {
            setCurrentStep(step);
          }

          // Map response to onboarding status
          let status: OnboardingStatus;
          if (data.status) {
            // Use status field if available
            status = data.status as OnboardingStatus;
          } else if (data.onboarding_status) {
            // Fallback to onboarding_status field
            status = data.onboarding_status as OnboardingStatus;
          } else if (data.step) {
            // Map step to status as fallback
            switch (data.step) {
              case 'create_tenant':
              case 'create_website':
              case 'complete_profile':
                status = OnboardingStatus.IN_PROGRESS;
                break;
              case 'completed':
                status = OnboardingStatus.COMPLETED;
                break;
              default:
                status = OnboardingStatus.NOT_STARTED;
            }
          } else {
            status = OnboardingStatus.NOT_STARTED;
          }

          setOnboardingStatus(status);

          logger('Onboarding status loaded:', {
            status: status,
            currentStep: step,
            originalStep: data.step,
            isCompleted: data.is_completed,
            completionPercentage: data.completion_percentage,
          });
        } else {
          logger('Failed to get onboarding status:', response.status.message);
          // Default to not started if we can't get status
          setOnboardingStatus(OnboardingStatus.NOT_STARTED);
        }
      } catch (error) {
        logger('Error checking onboarding status:', error);
        // Default to not started on error
        setOnboardingStatus(OnboardingStatus.NOT_STARTED);
      } finally {
        setIsCheckingOnboarding(false);
      }
    };

    checkOnboardingStatus();
  }, [isLoggedIn]);

  if (!isLoggedIn) {
    return <Navigate to="/auth/login" replace />;
  }

  // Show loading while checking onboarding status or onboarding status is not loaded yet
  if (isCheckingOnboarding || onboardingStatus === null) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spin size="large" />
      </div>
    );
  }

  // Check onboarding status and redirect if needed
  const isOnOnboardingPage =
    location.pathname === '/tenant-onboarding' ||
    location.pathname === '/onboarding';

  const isOnTenantSelectionPage =
    location.pathname === '/auth/tenant-selection';

  const needsOnboarding =
    onboardingStatus === OnboardingStatus.NOT_STARTED ||
    onboardingStatus === OnboardingStatus.PENDING ||
    onboardingStatus === OnboardingStatus.PROCESSING ||
    onboardingStatus === OnboardingStatus.IN_PROGRESS;

  // Debug logging
  logger('Route check:', {
    pathname: location.pathname,
    isOnOnboardingPage,
    isOnTenantSelectionPage,
    onboardingStatus,
    needsOnboarding,
    isLoggedIn,
    hasTenantSelected: tenantService.hasTenantSelected(),
  });

  // If user needs onboarding and not on onboarding page, redirect to onboarding
  if (needsOnboarding && !isOnOnboardingPage) {
    logger('Redirecting to onboarding:', {
      status: onboardingStatus,
      currentStep,
    });
    return <Navigate to="/onboarding" replace />;
  }

  // If onboarding is completed but user is on onboarding page, redirect to tenant selection
  if (onboardingStatus === OnboardingStatus.COMPLETED && isOnOnboardingPage) {
    logger('Onboarding completed, redirecting to tenant selection');
    return <Navigate to="/auth/tenant-selection" replace />;
  }

  // Check if user has selected a tenant (only for non-onboarding and non-tenant-selection pages)
  if (
    !isOnOnboardingPage &&
    !isOnTenantSelectionPage &&
    isLoggedIn &&
    !tenantService.hasTenantSelected()
  ) {
    logger('No tenant selected, redirecting to tenant selection');
    return <Navigate to="/auth/tenant-selection" replace />;
  }
  // All checks passed, render the protected content
  return <Outlet />;
};

export default ProtectedRoute;
