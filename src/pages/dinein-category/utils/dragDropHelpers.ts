import {
  DragDropContextProps,
  DropResult,
  ResponderProvided,
} from 'react-beautiful-dnd';

export type SafeDropResult = DropResult & {
  destination: NonNullable<DropResult['destination']>;
};

export type SafeOnDragCallback = (
  result: SafeDropResult,
  provided: ResponderProvided,
) => void;
export const createOnDragEnd =
  (callback: SafeOnDragCallback): DragDropContextProps['onDragEnd'] =>
  (result, provided): void => {
    const { destination, source } = result;
    // if drops out of droppable area
    // or drops into the same position, do nothing
    if (
      !destination ||
      (destination.droppableId === source.droppableId &&
        destination.index === source.index)
    ) {
      return undefined;
    }

    callback(result as SafeDropResult, provided);
  };
