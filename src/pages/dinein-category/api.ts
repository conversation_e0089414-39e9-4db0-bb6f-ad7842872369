import {
  ApiResponse,
  ApiResponsePagination,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { DineinCategory } from './type';

const url = `/api/cms/v1/dinein/categories`;

export async function getItems(
  params: any,
): Promise<ApiResponsePagination<DineinCategory[]>> {
  const response = await apiService.get<
    ApiResponsePagination<DineinCategory[]>
  >(url, { params });
  return response.data;
}

export async function getTree(
  params?: any,
): Promise<ApiResponsePagination<DineinCategory[]>> {
  const response = await apiService.get<
    ApiResponsePagination<DineinCategory[]>
  >(`${url}/tree`, { params });
  return response.data;
}

export async function getItem(
  id: string,
): Promise<ApiResponse<DineinCategory>> {
  const response = await apiService.get<ApiResponse<DineinCategory>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function createItem(
  payload: any,
): Promise<ApiResponse<DineinCategory>> {
  const response = await apiService.post<ApiResponse<DineinCategory>>(
    url,
    payload,
  );
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<DineinCategory>> {
  const response = await apiService.put<ApiResponse<DineinCategory>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(
  id: string,
): Promise<ApiResponse<DineinCategory>> {
  const response = await apiService.delete<ApiResponse<DineinCategory>>(
    `${url}/${id}`,
  );
  return response.data;
}

// Định nghĩa kiểu dữ liệu cho request move-node
export interface MoveNodeRequest {
  category_id: number;
  new_parent_id: number;
  position: number;
}

// Định nghĩa kiểu dữ liệu cho request update-position
export interface UpdatePositionRequest {
  category_id: number;
  target_id: number; // ID của category làm mốc, node sẽ đứng TRƯỚC node này
}

// Định nghĩa kiểu dữ liệu cho request move-node-root
export interface MoveNodeRootRequest {
  category_id: number;
  position: number;
}

// Di chuyển danh mục vào danh mục cha khác và đặt ở vị trí cụ thể
export async function moveCategoryNode(
  payload: MoveNodeRequest,
): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${url}/move-node`,
    payload,
  );
  return response.data;
}

// Di chuyển danh mục thành node gốc (root) và đặt ở vị trí cụ thể
export async function moveCategoryNodeRoot(
  payload: MoveNodeRootRequest,
): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${url}/move-node-root`,
    payload,
  );
  return response.data;
}

// Cập nhật vị trí của danh mục trong cùng một danh mục cha
export async function updateCategoryPosition(
  payload: UpdatePositionRequest,
): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${url}/update-position`,
    payload,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getSearch(
  params: any,
): Promise<ApiResponse<DineinCategory[]>> {
  const response = await apiService.get<ApiResponse<DineinCategory[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<DineinCategory[]>> {
  const response = await apiService.get<ApiResponse<DineinCategory[]>>(
    `${url}/all`,
  );
  return response.data;
}
