export const STATUS = [
  {
    label: 'Chờ duyệt',
    value: 'pending',
    color: 'orange',
  },
  {
    label: 'Bật',
    value: 'active',
    color: 'green',
  },
  {
    label: 'Tắt',
    value: 'inactive',
    color: 'red',
  },
];

export interface DineinCategory {
  id: number;
  tenant_id: number;
  parent_id: number | null;
  name: string;
  slug: string;
  description: string;
  image: string;
  lft: number;
  rgt: number;
  depth: number;
  position: number;
  is_active: boolean;
  is_featured: boolean;
  meta_title: string;
  meta_description: string;
  created_at: string;
  updated_at: string;
  created_by: number;
  updated_by: number;
  children?: DineinCategory[];
  post_count?: number;
}

export interface DineinCategoryCreate {
  parent_id?: number | null;
  name: string;
  slug?: string;
  description?: string;
  image?: string;
  position?: number;
  is_active?: boolean;
  is_featured?: boolean;
  meta_title?: string;
  meta_description?: string;
}

export interface DineinCategoryUpdate {
  parent_id?: number | null;
  name?: string;
  slug?: string;
  description?: string;
  image?: string;
  position?: number;
  is_active?: boolean;
  is_featured?: boolean;
  meta_title?: string;
  meta_description?: string;
}

// @deprecated - Sử dụng MoveNodeRequest hoặc UpdatePositionRequest thay thế
export interface MoveCategoryRequest {
  category_id: number;
  target_id: number;
  position: 'before' | 'after' | 'child';
}

// Yêu cầu di chuyển danh mục vào danh mục cha khác
export interface MoveNodeRequest {
  category_id: number;
  new_parent_id: number;
  position: number;
}

// Yêu cầu cập nhật vị trí của danh mục
export interface UpdatePositionRequest {
  category_id: number;
  target_id: number; // ID của category làm mốc, node sẽ đứng TRƯỚC node này
}

// Định nghĩa kiểu dữ liệu cho cây danh mục sử dụng trong react-beautiful-dnd
export interface TreeItem {
  id: string;
  content: string;
  children: TreeItem[];
}
