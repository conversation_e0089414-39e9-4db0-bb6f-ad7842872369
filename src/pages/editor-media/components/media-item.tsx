import React from 'react';
import { MediaType } from '../type';
import {
  FaCheck,
  FaFile,
  FaFileAudio,
  FaFileVideo,
  FaFileImage,
} from 'react-icons/fa';

// Define props interface
interface MediaItemProps {
  item: MediaType;
  onClick: (item: MediaType) => void;
  active: boolean;
}

export default function MediaItem({ item, onClick, active }: MediaItemProps) {
  // Function to determine what icon to display based on media type
  const getMediaTypeIcon = () => {
    switch (item.file_type) {
      case 'image':
        return <FaFileImage />;
      case 'video':
        return <FaFileVideo />;
      case 'audio':
        return <FaFileAudio />;
      default:
        return <FaFile />;
    }
  };

  // Function to format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else if (bytes < 1073741824) return (bytes / 1048576).toFixed(1) + ' MB';
    else return (bytes / 1073741824).toFixed(1) + ' GB';
  };

  // Determine if preview is possible and what content to show
  const renderPreview = () => {
    console.log('item', item);
    if (item.file_type === 'image') {
      return <img src={item.public_url} alt={item.alt_text || item.filename} />;
    } else if (item.file_type === 'video') {
      return (
        <div className="VideoPreview">
          {getMediaTypeIcon()}
          <span>{item.original_filename}</span>
        </div>
      );
    } else if (item.file_type === 'audio') {
      return (
        <div className="AudioPreview">
          {getMediaTypeIcon()}
          <span>{item.original_filename}</span>
        </div>
      );
    } else {
      return (
        <div className="DocumentPreview">
          {getMediaTypeIcon()}
          <span>{item.original_filename}</span>
        </div>
      );
    }
  };

  return (
    <div
      className={`MediaItem ${active ? 'active' : ''}`}
      onClick={() => onClick(item)}
    >
      <div className="MediaItemContent">
        <div className="MediaType">{item.extension}</div>

        <div className="MediaImg">{renderPreview()}</div>
      </div>
      <div className="MediaName">
        <span className="MediaFileName" title={item.filename}>
          {item.filename}
        </span>

        <span className="MediaSelect">{active && <FaCheck />}</span>
      </div>
      {item.file_size && (
        <div className="MediaSize">{formatFileSize(item.file_size)}</div>
      )}
    </div>
  );
}
