import { AxiosRequestConfig } from 'axios';
import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import {
  MediaFileResponse,
  MediaFileUploadRequest,
  MediaFileUpdateRequest,
  MediaQueryParams,
  MediaFolderResponse,
  MediaFolderCreateRequest,
  MediaFolderUpdateRequest,
  MediaFolderMoveRequest,
  MediaFolderTreeResponse,
  MediaTagResponse,
  MediaTagCreateRequest,
  MediaTagUpdateRequest,
  ChunkedUploadInitRequest,
  ChunkedUploadRequest,
  UploadSessionResponse,
  StorageStatsResponse,
  FileTypeStatsResponse,
  FolderStatsResponse,
  BulkUpdateMetadataRequest,
  BulkVisibilityRequest,
  BulkDeleteRequest,
  MoveFilesRequest,
  TagOperationRequest,
  // Legacy compatibility
  MediaType,
  Folder,
  MediaUpdateData,
} from './type';

export const MODULE = 'media';
export const MODULE_NAME = 'Media Manager';
export const MODULE_POPUP = true;

const baseUrl = `/api/cms/v1/media`;
const filesUrl = `${baseUrl}/files`;
const foldersUrl = `${baseUrl}/folders`;
const tagsUrl = `${baseUrl}/tags`;
const publicUrl = `${baseUrl}/public`;

// File operations
export async function listFiles(
  params: MediaQueryParams,
): Promise<ApiResponsePaginationCursor<MediaFileResponse[]>> {
  const config: AxiosRequestConfig = { params };
  const response = await apiService.get<
    ApiResponsePaginationCursor<MediaFileResponse[]>
  >(filesUrl, config);
  return response.data;
}

export async function getFile(
  id: string,
): Promise<ApiResponse<MediaFileResponse>> {
  const response = await apiService.get<ApiResponse<MediaFileResponse>>(
    `${filesUrl}/${id}`,
  );
  return response.data;
}

export async function searchFiles(params: {
  search?: string;
  limit?: number;
  cursor?: string;
}): Promise<ApiResponsePaginationCursor<MediaFileResponse[]>> {
  const config: AxiosRequestConfig = { params };
  const response = await apiService.get<
    ApiResponsePaginationCursor<MediaFileResponse[]>
  >(`${filesUrl}/search`, config);
  return response.data;
}

// Legacy compatibility functions
export async function getItems(
  params: MediaQueryParams,
): Promise<ApiResponsePaginationCursor<MediaType[]>> {
  return listFiles(params) as Promise<ApiResponsePaginationCursor<MediaType[]>>;
}

export async function getItem(id: string): Promise<ApiResponse<MediaType>> {
  return getFile(id) as Promise<ApiResponse<MediaType>>;
}

// Upload operations
export async function uploadFile(
  request: MediaFileUploadRequest,
): Promise<ApiResponse<MediaFileResponse>> {
  const formData = new FormData();
  formData.append('file', request.file);

  if (request.folder_id)
    formData.append('folder_id', String(request.folder_id));
  if (request.title) formData.append('title', request.title);
  if (request.alt_text) formData.append('alt_text', request.alt_text);
  if (request.description) formData.append('description', request.description);
  if (request.category) formData.append('category', request.category);
  if (request.visibility) formData.append('visibility', request.visibility);
  if (request.storage_type)
    formData.append('storage_type', request.storage_type);
  if (request.tags && request.tags.length > 0) {
    request.tags.forEach((tag) => formData.append('tags', tag));
  }

  const response = await apiService.post<ApiResponse<MediaFileResponse>>(
    `${baseUrl}/upload`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );

  return response.data;
}

// Chunked upload operations
export async function initChunkedUpload(
  request: ChunkedUploadInitRequest,
): Promise<ApiResponse<UploadSessionResponse>> {
  const response = await apiService.post<ApiResponse<UploadSessionResponse>>(
    `${baseUrl}/upload/chunked/init`,
    request,
  );
  return response.data;
}

export async function uploadChunk(
  request: ChunkedUploadRequest,
): Promise<ApiResponse<UploadSessionResponse>> {
  const formData = new FormData();
  formData.append('session_id', request.session_id);
  formData.append('chunk_number', String(request.chunk_number));
  formData.append('chunk', request.chunk);

  const response = await apiService.post<ApiResponse<UploadSessionResponse>>(
    `${baseUrl}/upload/chunked/chunk`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
  return response.data;
}

export async function completeChunkedUpload(
  sessionId: string,
): Promise<ApiResponse<MediaFileResponse>> {
  const response = await apiService.post<ApiResponse<MediaFileResponse>>(
    `${baseUrl}/upload/chunked/complete`,
    { session_id: sessionId },
  );
  return response.data;
}

// Legacy upload function for backward compatibility
export async function uploadMedia(
  file: File,
  alt_text?: string,
  description?: string,
  tags?: string[],
  is_public?: boolean,
  folder_id?: number | null,
): Promise<ApiResponse<MediaType>> {
  const request: MediaFileUploadRequest = {
    file,
    alt_text,
    description,
    tags,
    folder_id: folder_id || undefined,
    visibility: is_public ? 'public' : 'private',
  };

  return uploadFile(request) as Promise<ApiResponse<MediaType>>;
}

// File update and delete operations
export async function updateFile(
  id: string,
  data: MediaFileUpdateRequest,
): Promise<ApiResponse<MediaFileResponse>> {
  const response = await apiService.put<ApiResponse<MediaFileResponse>>(
    `${filesUrl}/${id}`,
    data,
  );
  return response.data;
}

export async function deleteFile(id: string): Promise<ApiResponse<void>> {
  const response = await apiService.delete<ApiResponse<void>>(
    `${filesUrl}/${id}`,
  );
  return response.data;
}

// File operations
export async function moveFiles(
  request: MoveFilesRequest,
): Promise<ApiResponse<void>> {
  const response = await apiService.post<ApiResponse<void>>(
    `${filesUrl}/move`,
    request,
  );
  return response.data;
}

export async function copyFile(
  id: string,
): Promise<ApiResponse<MediaFileResponse>> {
  const response = await apiService.post<ApiResponse<MediaFileResponse>>(
    `${filesUrl}/${id}/copy`,
  );
  return response.data;
}

export async function renameFile(
  id: string,
  name: string,
): Promise<ApiResponse<MediaFileResponse>> {
  const response = await apiService.post<ApiResponse<MediaFileResponse>>(
    `${filesUrl}/${id}/rename`,
    { name },
  );
  return response.data;
}

// Tag operations for files
export async function addTagsToFile(
  id: string,
  request: TagOperationRequest,
): Promise<ApiResponse<void>> {
  const response = await apiService.post<ApiResponse<void>>(
    `${filesUrl}/${id}/tags`,
    request,
  );
  return response.data;
}

export async function removeTagsFromFile(
  id: string,
  request: TagOperationRequest,
): Promise<ApiResponse<void>> {
  const response = await apiService.delete<ApiResponse<void>>(
    `${filesUrl}/${id}/tags`,
    { data: request },
  );
  return response.data;
}

export async function setFileTags(
  id: string,
  request: TagOperationRequest,
): Promise<ApiResponse<void>> {
  const response = await apiService.put<ApiResponse<void>>(
    `${filesUrl}/${id}/tags`,
    request,
  );
  return response.data;
}

// Bulk operations
export async function bulkUpdateMetadata(
  request: BulkUpdateMetadataRequest,
): Promise<ApiResponse<void>> {
  const response = await apiService.post<ApiResponse<void>>(
    `${filesUrl}/bulk/update`,
    request,
  );
  return response.data;
}

export async function bulkChangeVisibility(
  request: BulkVisibilityRequest,
): Promise<ApiResponse<void>> {
  const response = await apiService.post<ApiResponse<void>>(
    `${filesUrl}/bulk/visibility`,
    request,
  );
  return response.data;
}

export async function bulkDeleteFiles(
  request: BulkDeleteRequest,
): Promise<ApiResponse<void>> {
  const response = await apiService.delete<ApiResponse<void>>(
    `${filesUrl}/bulk`,
    { data: request },
  );
  return response.data;
}

// Statistics
export async function getStorageStats(): Promise<
  ApiResponse<StorageStatsResponse>
> {
  const response = await apiService.get<ApiResponse<StorageStatsResponse>>(
    `${baseUrl}/stats/storage`,
  );
  return response.data;
}

export async function getFileTypeStats(): Promise<
  ApiResponse<FileTypeStatsResponse>
> {
  const response = await apiService.get<ApiResponse<FileTypeStatsResponse>>(
    `${baseUrl}/stats/types`,
  );
  return response.data;
}

// Download and stream
export async function downloadFile(id: string): Promise<ApiResponse<Blob>> {
  const response = await apiService.get<ApiResponse<Blob>>(
    `${filesUrl}/${id}/download`,
    { responseType: 'blob' },
  );
  return response.data;
}

export async function streamFile(id: string): Promise<ApiResponse<Blob>> {
  const response = await apiService.get<ApiResponse<Blob>>(
    `${filesUrl}/${id}/stream`,
    { responseType: 'blob' },
  );
  return response.data;
}

// Legacy compatibility functions
export async function updateItem(
  id: string,
  data: MediaUpdateData,
): Promise<ApiResponse<MediaType>> {
  return updateFile(id, data) as Promise<ApiResponse<MediaType>>;
}

export async function deleteItem(id: string): Promise<ApiResponse<void>> {
  return deleteFile(id);
}

// Folder operations
export async function listFolders(params?: {
  parent_id?: number;
  include_files?: boolean;
  include_tree?: boolean;
}): Promise<ApiResponse<MediaFolderResponse[]>> {
  const config: AxiosRequestConfig = { params };
  const response = await apiService.get<ApiResponse<MediaFolderResponse[]>>(
    foldersUrl,
    config,
  );
  return response.data;
}

export async function createFolder(
  request: MediaFolderCreateRequest,
): Promise<ApiResponse<MediaFolderResponse>> {
  const response = await apiService.post<ApiResponse<MediaFolderResponse>>(
    foldersUrl,
    request,
  );
  return response.data;
}

export async function getFolderTree(): Promise<
  ApiResponse<MediaFolderTreeResponse[]>
> {
  const response = await apiService.get<ApiResponse<MediaFolderTreeResponse[]>>(
    `${foldersUrl}/tree`,
  );
  return response.data;
}

export async function getFolder(
  id: number,
): Promise<ApiResponse<MediaFolderResponse>> {
  const response = await apiService.get<ApiResponse<MediaFolderResponse>>(
    `${foldersUrl}/${id}`,
  );
  return response.data;
}

export async function updateFolder(
  id: number,
  request: MediaFolderUpdateRequest,
): Promise<ApiResponse<MediaFolderResponse>> {
  const response = await apiService.put<ApiResponse<MediaFolderResponse>>(
    `${foldersUrl}/${id}`,
    request,
  );
  return response.data;
}

export async function deleteFolder(id: number): Promise<ApiResponse<void>> {
  const response = await apiService.delete<ApiResponse<void>>(
    `${foldersUrl}/${id}`,
  );
  return response.data;
}

export async function moveFolder(
  id: number,
  request: MediaFolderMoveRequest,
): Promise<ApiResponse<MediaFolderResponse>> {
  const response = await apiService.post<ApiResponse<MediaFolderResponse>>(
    `${foldersUrl}/${id}/move`,
    request,
  );
  return response.data;
}

export async function getFolderStats(
  id: number,
): Promise<ApiResponse<FolderStatsResponse>> {
  const response = await apiService.get<ApiResponse<FolderStatsResponse>>(
    `${foldersUrl}/${id}/stats`,
  );
  return response.data;
}

// System folders
export async function createSystemFolders(): Promise<ApiResponse<void>> {
  const response = await apiService.post<ApiResponse<void>>(
    `${foldersUrl}/system/init`,
  );
  return response.data;
}

export async function getSystemFolders(): Promise<
  ApiResponse<MediaFolderResponse[]>
> {
  const response = await apiService.get<ApiResponse<MediaFolderResponse[]>>(
    `${foldersUrl}/system`,
  );
  return response.data;
}

// Legacy folder functions for backward compatibility
export async function getFolders(
  parentId: number | null = null,
): Promise<ApiResponse<Folder[]>> {
  const params = parentId !== null ? { parent_id: parentId } : {};
  return listFolders(params) as Promise<ApiResponse<Folder[]>>;
}

// Tag operations
export async function listTags(params?: {
  search?: string;
  sort_by?: string;
  sort_order?: string;
}): Promise<ApiResponse<MediaTagResponse[]>> {
  const config: AxiosRequestConfig = { params };
  const response = await apiService.get<ApiResponse<MediaTagResponse[]>>(
    tagsUrl,
    config,
  );
  return response.data;
}

export async function createTag(
  request: MediaTagCreateRequest,
): Promise<ApiResponse<MediaTagResponse>> {
  const response = await apiService.post<ApiResponse<MediaTagResponse>>(
    tagsUrl,
    request,
  );
  return response.data;
}

export async function getPopularTags(
  limit?: number,
): Promise<ApiResponse<MediaTagResponse[]>> {
  const config: AxiosRequestConfig = { params: { limit } };
  const response = await apiService.get<ApiResponse<MediaTagResponse[]>>(
    `${tagsUrl}/popular`,
    config,
  );
  return response.data;
}

export async function searchTags(
  query: string,
): Promise<ApiResponse<MediaTagResponse[]>> {
  const config: AxiosRequestConfig = { params: { search: query } };
  const response = await apiService.get<ApiResponse<MediaTagResponse[]>>(
    `${tagsUrl}/search`,
    config,
  );
  return response.data;
}

export async function getTag(
  id: number,
): Promise<ApiResponse<MediaTagResponse>> {
  const response = await apiService.get<ApiResponse<MediaTagResponse>>(
    `${tagsUrl}/${id}`,
  );
  return response.data;
}

export async function updateTag(
  id: number,
  request: MediaTagUpdateRequest,
): Promise<ApiResponse<MediaTagResponse>> {
  const response = await apiService.put<ApiResponse<MediaTagResponse>>(
    `${tagsUrl}/${id}`,
    request,
  );
  return response.data;
}

export async function deleteTag(id: number): Promise<ApiResponse<void>> {
  const response = await apiService.delete<ApiResponse<void>>(
    `${tagsUrl}/${id}`,
  );
  return response.data;
}

export async function getFilesByTag(
  id: number,
  params?: MediaQueryParams,
): Promise<ApiResponsePaginationCursor<MediaFileResponse[]>> {
  const config: AxiosRequestConfig = { params };
  const response = await apiService.get<
    ApiResponsePaginationCursor<MediaFileResponse[]>
  >(`${tagsUrl}/${id}/files`, config);
  return response.data;
}

export async function cleanupUnusedTags(): Promise<
  ApiResponse<{ deleted_count: number }>
> {
  const response = await apiService.post<
    ApiResponse<{ deleted_count: number }>
  >(`${tagsUrl}/cleanup`);
  return response.data;
}

// Public file access (for CDN/direct access)
export async function getFileBySlug(
  slug: string,
): Promise<ApiResponse<MediaFileResponse>> {
  const response = await apiService.get<ApiResponse<MediaFileResponse>>(
    `${publicUrl}/files/${slug}`,
  );
  return response.data;
}

export async function downloadFileBySlug(
  slug: string,
): Promise<ApiResponse<Blob>> {
  const response = await apiService.get<ApiResponse<Blob>>(
    `${publicUrl}/download/${slug}`,
    { responseType: 'blob' },
  );
  return response.data;
}

// Legacy content update function
export async function updateMediaContent(
  id: string,
  editedImageBlob: Blob,
  filename?: string,
): Promise<ApiResponse<MediaType>> {
  const formData = new FormData();
  formData.append('file', editedImageBlob, filename || `edited_${id}.png`);

  const response = await apiService.patch<ApiResponse<MediaType>>(
    `${filesUrl}/${id}/content`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
  return response.data;
}
