import { apiService } from '../../services/api.service';

export async function getAuthors(params: any) {
  return apiService.get('/api/cms/v1/blog/authors', { params });
}

export async function deleteAuthor(id: string) {
  return apiService.delete(`/api/cms/v1/blog/authors/${id}`);
}

export async function createAuthor(data: any) {
  return apiService.post('/api/cms/v1/blog/authors', data);
}

export async function updateAuthor(id: string, data: any) {
  return apiService.put(`/api/cms/v1/blog/authors/${id}`, data);
}

export async function getAuthorOptions(search?: string) {
  const params: { limit: number; search?: string } = { limit: 20 };
  if (search) {
    params.search = search;
  }
  const response = await apiService.get('/api/cms/v1/blog/authors', {
    params,
  });
  return response.data;
}
