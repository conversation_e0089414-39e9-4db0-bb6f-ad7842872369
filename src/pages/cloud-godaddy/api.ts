import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MODULE } from './config';
import { CloudGodaddy } from './type';

const url = `/api/cms/v1/${MODULE}`;

export async function getItems(
  params: any,
): Promise<ApiResponsePaginationCursor<CloudGodaddy[]>> {
  const response = await apiService.get<
    ApiResponsePaginationCursor<CloudGodaddy[]>
  >(url, { params });
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<CloudGodaddy>> {
  const response = await apiService.get<ApiResponse<CloudGodaddy>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function createItem(
  payload: any,
): Promise<ApiResponse<CloudGodaddy>> {
  const response = await apiService.post<ApiResponse<CloudGodaddy>>(
    url,
    payload,
  );
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<CloudGodaddy>> {
  const response = await apiService.put<ApiResponse<CloudGodaddy>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(
  id: string,
): Promise<ApiResponse<CloudGodaddy>> {
  const response = await apiService.delete<ApiResponse<CloudGodaddy>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getSearch(
  params: any,
): Promise<ApiResponse<CloudGodaddy[]>> {
  const response = await apiService.get<ApiResponse<CloudGodaddy[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<CloudGodaddy[]>> {
  const response = await apiService.get<ApiResponse<CloudGodaddy[]>>(
    `${url}/all`,
  );
  return response.data;
}

export async function checkDomainsAvailability(
  payload: any,
): Promise<ApiResponse<CloudGodaddy>> {
  const response = await apiService.post<ApiResponse<CloudGodaddy>>(
    `${url}/check-availability`,
    payload,
  );
  return response.data;
}
