{"module": "CloudGodaddy", "list.btnAdd": "<PERSON><PERSON><PERSON><PERSON>", "list.btnBuy": "Mua domain", "list.btnEdit": "Chỉnh sửa", "list.btnUpdate": "<PERSON><PERSON><PERSON>", "list.btnDelete": "Xóa", "list.btnImport": "Import config", "list.btnCancel": "Bỏ qua", "list.btnSaveAndContinue": "<PERSON><PERSON><PERSON> và tiếp tục", "list.btnSave": "<PERSON><PERSON><PERSON>", "list.addSuccess": "<PERSON><PERSON><PERSON> thành công", "list.updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công", "list.deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "list.submitError": "<PERSON><PERSON><PERSON> thất bại. Thử lại sau", "list.pleaseEnterData": "<PERSON><PERSON> lòng nhập dữ liệu", "list.actions": "<PERSON><PERSON><PERSON>", "list.deleteConfirm": "Bạn có chắc chắn muốn xóa?", "list.categories": "<PERSON><PERSON>", "status.INACTIVE": "<PERSON><PERSON><PERSON> k<PERSON>ch ho<PERSON>", "status.ACTIVE": "<PERSON><PERSON><PERSON>", "search.btnSearch": "<PERSON><PERSON><PERSON>", "search.btnReset": "<PERSON><PERSON><PERSON>", "form.id": "ID", "form.inputData": "<PERSON><PERSON> lòng nhập dữ liệu", "form.selectData": "<PERSON><PERSON> lòng chọn dữ liệu", "form.createdAt": "<PERSON><PERSON><PERSON>", "form.domain": "<PERSON><PERSON><PERSON>", "form.domainId": "<PERSON> <PERSON><PERSON>n", "form.expirationProtected": "<PERSON><PERSON><PERSON> v<PERSON> hết hạn", "form.expires": "<PERSON><PERSON><PERSON> h<PERSON> hạn", "form.exposeWhois": "<PERSON><PERSON><PERSON>", "form.holdRegistrar": "<PERSON><PERSON><PERSON> đ<PERSON>ng ký", "form.locked": "Đã khóa", "form.nameServers": "<PERSON><PERSON><PERSON> chủ tên", "form.privacy": "<PERSON><PERSON><PERSON><PERSON> tư", "form.registrarCreatedAt": "<PERSON><PERSON><PERSON> ký", "form.renewAuto": "<PERSON><PERSON> động gia hạn", "form.renewDeadline": "<PERSON>ạn gia hạn", "form.renewable": "<PERSON><PERSON> thể gia hạn", "form.status": "<PERSON><PERSON><PERSON><PERSON> thái", "form.transferAwayEligibleAt": "<PERSON><PERSON><PERSON> đủ điều kiện chuyển đi", "form.transferProtected": "<PERSON><PERSON><PERSON> v<PERSON> chuy<PERSON>n", "form.yes": "<PERSON><PERSON>", "form.no": "K<PERSON>ô<PERSON>", "formConfig.config": "Config json"}