import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Select, SelectProps, Spin, Avatar } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import debounce from 'lodash/debounce';
import { getItems } from '../api';
import { Customer } from '../type';

interface SearchCustomerProps extends Omit<SelectProps, 'options'> {
  onSelect?: (customer: Customer) => void;
}

const SearchCustomer: React.FC<SearchCustomerProps> = ({
  onSelect,
  ...props
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState<boolean>(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [searchText, setSearchText] = useState<string>('');

  const fetchCustomers = async (search: string) => {
    if (!search || search.length < 2) return;

    setLoading(true);
    try {
      const response = await getItems({
        search,
        limit: 10,
      });
      if (response.success) {
        setCustomers(response.data);
      }
    } catch (error) {
      console.error('Error fetching customers:', error);
    } finally {
      setLoading(false);
    }
  };

  const debouncedFetchCustomers = debounce(fetchCustomers, 500);

  useEffect(() => {
    debouncedFetchCustomers(searchText);
    return () => {
      debouncedFetchCustomers.cancel();
    };
  }, [searchText]);

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  const handleSelect = (value: string, option: any) => {
    const selectedCustomer = customers.find(
      (customer) => customer.customer_id === parseInt(value, 10),
    );
    if (selectedCustomer && onSelect) {
      onSelect(selectedCustomer);
    }
  };

  const options = customers.map((customer) => ({
    label: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Avatar
          size="small"
          src={customer.avatar_url}
          icon={<UserOutlined />}
          style={{ marginRight: 8 }}
        />
        <div>
          <div>{customer.full_name}</div>
          <div style={{ fontSize: '12px', color: '#999' }}>
            {customer.email}
          </div>
        </div>
      </div>
    ),
    value: String(customer.customer_id),
  }));

  return (
    <Select
      showSearch
      placeholder={t('customer.filter.search')}
      loading={loading}
      filterOption={false}
      onSearch={handleSearch}
      onSelect={handleSelect}
      notFoundContent={loading ? <Spin size="small" /> : null}
      options={options}
      {...props}
    />
  );
};

export default SearchCustomer;
