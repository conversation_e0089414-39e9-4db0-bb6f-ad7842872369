import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Select, SelectProps, Spin } from 'antd';
import { getCustomerGroups } from '../api';
import { SelectOption } from '../../../types.global';

type SelectCustomerGroupProps = SelectProps;

const SelectCustomerGroup: React.FC<SelectCustomerGroupProps> = (props) => {
  const { t } = useTranslation();
  const [options, setOptions] = useState<SelectOption[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    const fetchOptions = async () => {
      setLoading(true);
      try {
        const response = await getCustomerGroups();
        if (response.success) {
          setOptions(response.data);
        }
      } catch (error) {
        console.error('Error fetching customer groups:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOptions();
  }, []);

  return (
    <Select
      placeholder={t('customer.filter.by_group')}
      allowClear
      showSearch
      optionFilterProp="label"
      notFoundContent={loading ? <Spin size="small" /> : null}
      options={options}
      {...props}
    />
  );
};

export default SelectCustomerGroup;
