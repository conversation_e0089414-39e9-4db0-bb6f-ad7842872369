import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Col, Form, Input, Row, Select, Space, Switch } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import { CustomerStatus } from '../type';
import { getCustomerGroups } from '../api';
import { SelectOption } from '../../../types.global';

interface SearchProps {
  onSearch: (values: any) => void;
}

const Search: React.FC<SearchProps> = ({ onSearch }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [customerGroups, setCustomerGroups] = useState<SelectOption[]>([]);

  useEffect(() => {
    const fetchGroups = async () => {
      try {
        const response = await getCustomerGroups();
        if (response.success) {
          setCustomerGroups(response.data);
        }
      } catch (error) {
        console.error('Error fetching customer groups:', error);
      }
    };
    fetchGroups();
  }, []);

  const handleSearch = (values: any) => {
    // Clean up empty values
    const cleanValues = Object.entries(values).reduce(
      (acc: any, [key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          acc[key] = value;
        }
        return acc;
      },
      {},
    );
    onSearch(cleanValues);
  };

  const handleReset = () => {
    form.resetFields();
    onSearch({});
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSearch}
      style={{ marginBottom: 16 }}
    >
      <Row gutter={16}>
        <Col xs={24} sm={12} md={6} lg={6}>
          <Form.Item name="search" label={t('common.search')}>
            <Input placeholder={t('customer.filter.search')} allowClear />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={5}>
          <Form.Item name="status" label={t('customer.fields.status')}>
            <Select
              placeholder={t('customer.filter.by_status')}
              allowClear
              options={[
                { value: 'active', label: t('customer.status.active') },
                { value: 'inactive', label: t('customer.status.inactive') },
                { value: 'blocked', label: t('customer.status.blocked') },
                { value: 'pending', label: t('customer.status.pending') },
              ]}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={5}>
          <Form.Item name="group_id" label={t('customer.fields.group_id')}>
            <Select
              placeholder={t('customer.filter.by_group')}
              allowClear
              options={customerGroups}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={4}>
          <Form.Item
            name="is_verified"
            label={t('customer.fields.is_verified')}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
        <Col
          xs={24}
          sm={24}
          md={24}
          lg={4}
          style={{ display: 'flex', alignItems: 'flex-end' }}
        >
          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SearchOutlined />}
              >
                {t('common.search')}
              </Button>
              <Button onClick={handleReset} icon={<ReloadOutlined />}>
                {t('common.reset')}
              </Button>
            </Space>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default Search;
