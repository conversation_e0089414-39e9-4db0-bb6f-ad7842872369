import {
  ApiResponse,
  ApiResponsePagination,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MODULE, RESOURCE } from './config';
import { Customer } from './type';

const url = `/api/cms/v1/${MODULE}/${RESOURCE}`;

export async function getItems(
  params: any,
): Promise<ApiResponsePagination<Customer[]>> {
  const response = await apiService.get<ApiResponsePagination<Customer[]>>(
    url,
    {
      params,
    },
  );
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<Customer>> {
  const response = await apiService.get<ApiResponse<Customer>>(`${url}/${id}`);
  return response.data;
}

export async function createItem(payload: any): Promise<ApiResponse<Customer>> {
  const response = await apiService.post<ApiResponse<Customer>>(url, payload);
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<Customer>> {
  const response = await apiService.put<ApiResponse<Customer>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(id: string): Promise<ApiResponse<Customer>> {
  const response = await apiService.delete<ApiResponse<Customer>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getCustomerGroups(): Promise<
  ApiResponse<SelectOption[]>
> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `/api/cms/v1/${MODULE}/customer-groups/options`,
  );
  return response.data;
}

export async function verifyCustomer(
  id: string,
): Promise<ApiResponse<Customer>> {
  const response = await apiService.post<ApiResponse<Customer>>(
    `${url}/${id}/verify`,
  );
  return response.data;
}

export async function updateStatus(
  id: string,
  status: string,
): Promise<ApiResponse<Customer>> {
  const response = await apiService.patch<ApiResponse<Customer>>(
    `${url}/${id}/status`,
    { status },
  );
  return response.data;
}
