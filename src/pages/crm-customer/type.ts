export interface Customer {
  customer_id: number;
  tenant_id: number;
  user_id?: number;
  group_id?: number;
  full_name: string;
  email: string;
  phone?: string;
  status: 'active' | 'inactive' | 'blocked' | 'pending';
  is_verified: boolean;
  avatar_url?: string;
  created_at?: string;
  updated_at?: string;
  last_login_at?: string;
  created_by?: number;
  updated_by?: number;

  // Additional properties for UI display
  group_name?: string;
}

export interface CustomerGroup {
  group_id: number;
  name: string;
  description?: string;
}

export type CustomerStatus = 'active' | 'inactive' | 'blocked' | 'pending';
