import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, message } from 'antd';
import CustomerForm from './form';
import { useCustomerStore } from '../store';
import { createItem, updateItem } from '../api';

const CustomerFormModal: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState<boolean>(false);

  const {
    customer,
    isEdit,
    isModalOpen,
    setIsModalOpen,
    setCustomer,
    setRefreshList,
  } = useCustomerStore();

  const handleCancel = () => {
    setIsModalOpen(false);
    setCustomer(null);
  };

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      if (isEdit && customer) {
        await updateItem(String(customer.customer_id), values);
        message.success(t('customer.messages.update_success'));
      } else {
        await createItem(values);
        message.success(t('customer.messages.create_success'));
      }
      handleCancel();
      setRefreshList((prev) => !prev);
    } catch (error) {
      console.error('Error saving customer:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={
        isEdit ? t('customer.actions.update') : t('customer.actions.create')
      }
      open={isModalOpen}
      onCancel={handleCancel}
      footer={null}
      width={800}
      destroyOnClose
    >
      <CustomerForm
        initialValues={customer || undefined}
        onFinish={handleSubmit}
        onCancel={handleCancel}
        loading={loading}
      />
    </Modal>
  );
};

export default CustomerFormModal;
