import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  Switch,
  Upload,
  message,
  Spin,
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { Customer, CustomerStatus } from '../type';
import { getCustomerGroups } from '../api';
import { SelectOption } from '../../../types.global';

interface CustomerFormProps {
  initialValues?: Customer;
  onFinish: (values: any) => void;
  onCancel?: () => void;
  loading?: boolean;
}

const CustomerForm: React.FC<CustomerFormProps> = ({
  initialValues,
  onFinish,
  onCancel,
  loading = false,
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [customerGroups, setCustomerGroups] = useState<SelectOption[]>([]);
  const [groupsLoading, setGroupsLoading] = useState<boolean>(false);

  const isEdit = !!initialValues;

  useEffect(() => {
    const fetchGroups = async () => {
      setGroupsLoading(true);
      try {
        const response = await getCustomerGroups();
        if (response.success) {
          setCustomerGroups(response.data);
        }
      } catch (error) {
        console.error('Error fetching customer groups:', error);
      } finally {
        setGroupsLoading(false);
      }
    };
    fetchGroups();

    // Set form values when editing
    if (initialValues) {
      form.setFieldsValue({
        ...initialValues,
      });
    }
  }, [initialValues, form]);

  const handleFinish = (values: any) => {
    onFinish(values);
  };

  return (
    <Spin spinning={loading || groupsLoading}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleFinish}
        initialValues={{
          status: 'pending',
          is_verified: false,
          ...initialValues,
        }}
      >
        <Row gutter={16}>
          <Col xs={24} sm={24} md={12}>
            <Form.Item
              name="full_name"
              label={t('customer.fields.full_name')}
              rules={[
                {
                  required: true,
                  message: t('common.validation.required', {
                    field: t('customer.fields.full_name'),
                  }),
                },
              ]}
            >
              <Input />
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={12}>
            <Form.Item
              name="email"
              label={t('customer.fields.email')}
              rules={[
                {
                  required: true,
                  message: t('common.validation.required', {
                    field: t('customer.fields.email'),
                  }),
                },
                {
                  type: 'email',
                  message: t('common.validation.email'),
                },
              ]}
            >
              <Input />
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={12}>
            <Form.Item name="phone" label={t('customer.fields.phone')}>
              <Input />
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={12}>
            <Form.Item name="group_id" label={t('customer.fields.group_id')}>
              <Select
                placeholder={t('customer.filter.by_group')}
                allowClear
                options={customerGroups}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={12}>
            <Form.Item
              name="status"
              label={t('customer.fields.status')}
              rules={[
                {
                  required: true,
                  message: t('common.validation.required', {
                    field: t('customer.fields.status'),
                  }),
                },
              ]}
            >
              <Select
                options={[
                  { value: 'active', label: t('customer.status.active') },
                  { value: 'inactive', label: t('customer.status.inactive') },
                  { value: 'blocked', label: t('customer.status.blocked') },
                  { value: 'pending', label: t('customer.status.pending') },
                ]}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={12}>
            <Form.Item
              name="avatar_url"
              label={t('customer.fields.avatar_url')}
            >
              <Input placeholder="https://example.com/avatar.jpg" />
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={12}>
            <Form.Item
              name="is_verified"
              label={t('customer.fields.is_verified')}
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Row gutter={8} justify="end">
            {onCancel && (
              <Col>
                <Button onClick={onCancel}>{t('common.cancel')}</Button>
              </Col>
            )}
            <Col>
              <Button type="primary" htmlType="submit" loading={loading}>
                {isEdit ? t('common.update') : t('common.create')}
              </Button>
            </Col>
          </Row>
        </Form.Item>
      </Form>
    </Spin>
  );
};

export default CustomerForm;
