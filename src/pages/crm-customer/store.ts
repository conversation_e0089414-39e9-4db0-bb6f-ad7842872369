import { create } from 'zustand';
import { Customer } from './type';

interface CustomerStore {
  customer: Customer | null;
  setCustomer: (customer: Customer | null) => void;
  isEdit: boolean;
  setIsEdit: (isEdit: boolean) => void;
  isModalOpen: boolean;
  setIsModalOpen: (isModalOpen: boolean) => void;
  refreshList: boolean;
  setRefreshList: (refreshList: boolean) => void;
}

export const useCustomerStore = create<CustomerStore>((set) => ({
  customer: null,
  setCustomer: (customer) => set({ customer }),
  isEdit: false,
  setIsEdit: (isEdit) => set({ isEdit }),
  isModalOpen: false,
  setIsModalOpen: (isModalOpen) => set({ isModalOpen }),
  refreshList: false,
  setRefreshList: (refreshList) => set({ refreshList }),
}));
