import dayjs, { Dayjs } from 'dayjs';
export enum CrawlUserStatus {
  PENDING = 'PENDING',
  PUBLISHED = 'PUBLISHED',
  DISABLED = 'DISABLED',
}

export interface CrawlUser {
  _id: string;
  domain: string;
  ip?: string;
  expireDate?: Dayjs;
  apiKey?: string;
  webhookUrl?: string;
  webhookMethod?: string;
  webhookApiKey?: string;
  webhookHeaderKey?: string;
  webhookHeaderField?: string;
  status: CrawlUserStatus;
  categories?: [];
}

export const transformValues = (values: any) => {
  const item: CrawlUser = { ...values };
  if (values.expireDate) {
    item.expireDate = dayjs(values.expireDate);
  }

  return item;
};

export interface CrawlUserAnalytics {
  userId: string;
  blogTotal: number;
}
