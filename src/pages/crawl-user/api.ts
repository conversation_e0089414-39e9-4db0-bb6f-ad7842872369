import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MODULE } from './config';
import { CrawlUser, CrawlUserAnalytics } from './type';

const url = `/api/cms/v1/${MODULE}`;

export async function getItems(
  params: any,
): Promise<ApiResponsePaginationCursor<CrawlUser[]>> {
  const response = await apiService.get<
    ApiResponsePaginationCursor<CrawlUser[]>
  >(url, { params });
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<CrawlUser>> {
  const response = await apiService.get<ApiResponse<CrawlUser>>(`${url}/${id}`);
  return response.data;
}

export async function createItem(
  payload: any,
): Promise<ApiResponse<CrawlUser>> {
  const response = await apiService.post<ApiResponse<CrawlUser>>(url, payload);
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<CrawlUser>> {
  const response = await apiService.put<ApiResponse<CrawlUser>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(id: string): Promise<ApiResponse<CrawlUser>> {
  const response = await apiService.delete<ApiResponse<CrawlUser>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getSearch(
  params: any,
): Promise<ApiResponse<CrawlUser[]>> {
  const response = await apiService.get<ApiResponse<CrawlUser[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<CrawlUser[]>> {
  const response = await apiService.get<ApiResponse<CrawlUser[]>>(`${url}/all`);
  return response.data;
}

export async function getAnalytics(
  payload: any,
): Promise<ApiResponse<CrawlUserAnalytics>> {
  const response = await apiService.post(`${url}/analytics`, payload);
  return response.data;
}
