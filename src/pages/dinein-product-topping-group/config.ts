// filepath: /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/web/cms/src/pages/dinein-product-topping-group/config.ts
export const MODULE = 'dinein-product-topping-group';
export const ROUTES = {
  LIST: '/dinein-product-topping-group',
  DETAIL: '/dinein-product-topping-group/:id',
  CREATE: '/dinein-product-topping-group/create',
  EDIT: '/dinein-product-topping-group/edit/:id',
};

export const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

export const tailFormItemLayout = {
  wrapperCol: {
    xs: {
      span: 24,
      offset: 0,
    },
    sm: {
      span: 18,
      offset: 6,
    },
  },
};
