import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card } from 'antd';

import ToppingGroupForm from './index';

const ToppingGroupStandalone: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div>
      <h2>{t('dinein-product-topping-group:title')}</h2>
      <Card bordered={false}>
        <ToppingGroupForm />
      </Card>
    </div>
  );
};

export default ToppingGroupStandalone;
