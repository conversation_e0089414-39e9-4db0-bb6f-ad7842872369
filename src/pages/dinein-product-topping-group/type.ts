// filepath: /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/web/cms/src/pages/dinein-product-topping-group/type.ts
import { DineinTopping } from '../dinein-product-topping/type';

export interface User {
  id?: number;
  username?: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
}

export interface DineinProduct {
  product_id?: number;
  name?: string;
  image_url?: string;
}

export interface DineinToppingGroup {
  group_id?: number;
  tenant_id?: number;
  name?: string;
  description?: string;
  min_selections?: number;
  max_selections?: number;
  display_order?: number;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
  product?: DineinProduct;
  toppings?: DineinToppingGroupItem[];
}

export interface DineinToppingGroupItem {
  item_id?: number;
  tenant_id?: number;
  group_id?: number;
  topping_id?: number;
  display_order?: number;
  price_override?: number;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
  topping?: DineinTopping;
}

export function transformToppingGroup(group: any): DineinToppingGroup {
  return {
    ...group,
    min_selections: group.min_selections || 0,
    max_selections: group.max_selections || 0,
    display_order: group.display_order || 0,
  };
}

export function transformToppingGroupItem(item: any): DineinToppingGroupItem {
  return {
    ...item,
    display_order: item.display_order || 0,
    price_override: item.price_override || 0,
    topping: item.topping
      ? {
          ...item.topping,
          is_active:
            item.topping.is_active === 1 || item.topping.is_active === true,
        }
      : undefined,
  };
}
