{"title": "<PERSON><PERSON><PERSON><PERSON>", "create": {"title": "<PERSON><PERSON><PERSON> mớ<PERSON> Nhó<PERSON> Top<PERSON>"}, "edit": {"title": "Chỉnh s<PERSON><PERSON>"}, "form": {"product": "<PERSON><PERSON><PERSON> p<PERSON>m", "productPlaceholder": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "name": "<PERSON><PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên n<PERSON>", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả nhóm", "minSelections": "<PERSON><PERSON> l<PERSON> chọn tối thiểu", "minSelectionsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số l<PERSON> chọn tối thiểu", "maxSelections": "Số lư<PERSON><PERSON> chọn tối đa", "maxSelectionsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số lư<PERSON><PERSON> chọn tối đa", "displayOrder": "<PERSON><PERSON><PERSON> tự hiển thị", "displayOrderPlaceholder": "<PERSON><PERSON><PERSON><PERSON> thứ tự hiển thị"}, "list": {"name": "<PERSON><PERSON><PERSON>", "productName": "<PERSON><PERSON><PERSON> p<PERSON>m", "minSelections": "<PERSON><PERSON><PERSON> tối thi<PERSON>u", "maxSelections": "<PERSON><PERSON><PERSON> tối đa", "displayOrder": "<PERSON><PERSON><PERSON> tự hiển thị", "viewToppings": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> trong nhóm", "action": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "search": {"name": "<PERSON><PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON> kiếm theo tên", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "productPlaceholder": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "searchButton": "<PERSON><PERSON><PERSON>", "resetButton": "Đặt lại"}, "validation": {"nameRequired": "<PERSON><PERSON> lòng nhập tên nhóm", "productRequired": "<PERSON><PERSON> lòng chọn sản phẩm", "minSelectionsRequired": "<PERSON><PERSON> lòng nhập số lượng chọn tối thiểu", "maxSelectionsRequired": "<PERSON><PERSON> lòng nhập số lượng chọn tối đa", "toppingRequired": "<PERSON><PERSON> lòng ch<PERSON>n topping"}, "detail": {"title": "<PERSON><PERSON> trong Nhóm", "addTopping": "<PERSON><PERSON><PERSON><PERSON>", "editTopping": "Chỉnh s<PERSON>a <PERSON>", "topping": "Topping", "selectTopping": "<PERSON><PERSON><PERSON> topping", "toppingName": "<PERSON><PERSON><PERSON> topping", "originalPrice": "<PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "priceOverride": "<PERSON><PERSON><PERSON> thay thế", "priceOverridePlaceholder": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> thay thế (k<PERSON><PERSON><PERSON> b<PERSON><PERSON> buộc)", "displayOrder": "<PERSON><PERSON><PERSON> tự hiển thị", "displayOrderPlaceholder": "<PERSON><PERSON><PERSON><PERSON> thứ tự hiển thị"}, "messages": {"fetchFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải dữ liệu", "createSuccess": "Tạo nhóm topping thành công", "createFailed": "Tạo nhóm topping thất bại", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> nh<PERSON>m topping thành công", "updateFailed": "<PERSON><PERSON><PERSON> nh<PERSON>t nh<PERSON> topping thất bại", "deleteSuccess": "Xóa nhóm topping thành công", "deleteFailed": "Xóa nhóm topping thất bại", "deleteConfirm": "Bạn có chắc chắn muốn xóa nhóm topping này không?", "addToppingSuccess": "Thêm topping vào nhóm thành công", "addToppingFailed": "Thêm topping vào nhóm thất bại", "updateToppingSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t topping trong nhóm thành công", "updateToppingFailed": "<PERSON><PERSON><PERSON> nhật topping trong nhóm thất bại", "deleteToppingSuccess": "<PERSON>ó<PERSON> topping khỏi nhóm thành công", "deleteToppingFailed": "<PERSON>óa topping khỏi nhóm thất bại", "deleteToppingConfirm": "Bạn có chắc chắn muốn xóa topping này khỏi nhóm không?"}, "buttons": {"create": "<PERSON><PERSON><PERSON> mới", "update": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "edit": "<PERSON><PERSON><PERSON>", "back": "Quay lại", "save": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "addTopping": "<PERSON><PERSON><PERSON><PERSON>", "viewToppings": "<PERSON><PERSON>", "yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>"}, "table": {"actions": "<PERSON><PERSON>"}}