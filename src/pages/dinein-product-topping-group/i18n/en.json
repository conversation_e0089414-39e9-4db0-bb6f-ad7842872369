{"title": "Topping Group Management", "create": {"title": "Create Topping Group"}, "edit": {"title": "Edit Topping Group"}, "form": {"product": "Product", "productPlaceholder": "Select product", "name": "Group Name", "namePlaceholder": "Enter group name", "description": "Description", "descriptionPlaceholder": "Enter group description", "minSelections": "Minimum Selections", "minSelectionsPlaceholder": "Enter minimum selections", "maxSelections": "Maximum Selections", "maxSelectionsPlaceholder": "Enter maximum selections", "displayOrder": "Display Order", "displayOrderPlaceholder": "Enter display order"}, "list": {"name": "Group Name", "productName": "Product", "minSelections": "Min Selections", "maxSelections": "Max Selections", "displayOrder": "Display Order", "viewToppings": "Topping management in groups", "action": "Action"}, "search": {"name": "Group Name", "namePlaceholder": "Search by name", "product": "Product", "productPlaceholder": "Select product", "searchButton": "Search", "resetButton": "Reset"}, "validation": {"nameRequired": "Please enter group name", "productRequired": "Please select a product", "minSelectionsRequired": "Please enter minimum selections", "maxSelectionsRequired": "Please enter maximum selections", "toppingRequired": "Please select a topping"}, "detail": {"title": "Toppings in Group", "addTopping": "Add Topping", "editTopping": "Edit Topping", "topping": "Topping", "selectTopping": "Select topping", "toppingName": "Topping Name", "originalPrice": "Original Price", "price": "Original Price", "priceOverride": "Price Override", "priceOverridePlaceholder": "Enter price override (optional)", "displayOrder": "Display Order", "displayOrderPlaceholder": "Enter display order"}, "messages": {"fetchFailed": "Failed to fetch data", "createSuccess": "Topping group created successfully", "createFailed": "Failed to create topping group", "updateSuccess": "Topping group updated successfully", "updateFailed": "Failed to update topping group", "deleteSuccess": "Topping group deleted successfully", "deleteFailed": "Failed to delete topping group", "deleteConfirm": "Are you sure you want to delete this topping group?", "addToppingSuccess": "Topping added to group successfully", "addToppingFailed": "Failed to add topping to group", "updateToppingSuccess": "Topping in group updated successfully", "updateToppingFailed": "Failed to update topping in group", "deleteToppingSuccess": "Topping removed from group successfully", "deleteToppingFailed": "Failed to remove topping from group", "deleteToppingConfirm": "Are you sure you want to remove this topping from the group?"}, "buttons": {"create": "Create", "update": "Update", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "back": "Back", "save": "Save", "add": "Add", "addTopping": "Add Topping", "viewToppings": "View Toppings", "yes": "Yes", "no": "No"}, "table": {"actions": "Actions"}}