// filepath: /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/web/cms/src/pages/dinein-product-topping-group/list/list.tsx
import { DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import { But<PERSON>, Popconfirm, Space, Table } from 'antd';
import { TableProps } from 'antd/lib/table';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { DineinToppingGroup } from '../type';

interface ToppingGroupListProps extends TableProps<DineinToppingGroup> {
  toppingGroups: DineinToppingGroup[];
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
  onViewToppings: (group: DineinToppingGroup) => void;
}

const ToppingGroupList: React.FC<ToppingGroupListProps> = ({
  toppingGroups,
  onEdit,
  onDelete,
  onViewToppings,
  pagination,
  onChange,
}) => {
  const { t } = useTranslation(['dinein-product-topping-group', 'common']);

  const columns = [
    {
      title: t('dinein-product-topping-group:list.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('dinein-product-topping-group:list.productName'),
      dataIndex: ['product', 'name'],
      key: 'product_name',
      render: (text: string, record: DineinToppingGroup) =>
        record.product?.name || '-',
    },
    {
      title: t('dinein-product-topping-group:list.minSelections'),
      dataIndex: 'min_selections',
      key: 'min_selections',
      width: 120,
    },
    {
      title: t('dinein-product-topping-group:list.maxSelections'),
      dataIndex: 'max_selections',
      key: 'max_selections',
      width: 120,
    },
    {
      title: t('dinein-product-topping-group:list.displayOrder'),
      dataIndex: 'display_order',
      key: 'display_order',
      width: 120,
    },
    {
      title: t('common:table.actions'),
      key: 'action',
      width: 350,
      render: (_: any, record: DineinToppingGroup) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => onViewToppings(record)}
            title={t('dinein-product-topping-group:list.viewToppings')}
          >
            {t('dinein-product-topping-group:list.viewToppings')}
          </Button>
          <Button
            type="default"
            size="small"
            icon={<EditOutlined />}
            onClick={() => onEdit(record.group_id!)}
            title={t('common:buttons.edit')}
          />
          <Popconfirm
            title={t('common:messages.deleteConfirm')}
            onConfirm={() => onDelete(record.group_id!)}
            okText={t('common:buttons.yes')}
            cancelText={t('common:buttons.no')}
          >
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
              title={t('common:buttons.delete')}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={toppingGroups}
      rowKey="group_id"
      pagination={pagination}
      onChange={onChange}
    />
  );
};

export default ToppingGroupList;
