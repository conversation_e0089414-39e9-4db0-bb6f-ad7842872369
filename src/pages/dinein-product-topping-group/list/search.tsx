// filepath: /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/web/cms/src/pages/dinein-product-topping-group/list/search.tsx
import React, { useEffect, useState } from 'react';
import { Form, Input, Button, Row, Col, Select } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { getProductOptions } from '../api';

interface ToppingGroupSearchProps {
  onSearch: (values: any) => void;
}

const ToppingGroupSearch: React.FC<ToppingGroupSearchProps> = ({
  onSearch,
}) => {
  const { t } = useTranslation(['dinein-product-topping-group', 'common']);
  const [form] = Form.useForm();
  const [products, setProducts] = useState<any[]>([]);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      const response = await getProductOptions();
      if (response.data) {
        const options = response.data.map((product: any) => ({
          label: product.name,
          value: product.product_id,
        }));
        setProducts(options);
      }
    } catch (error) {
      console.error('Failed to fetch products:', error);
    }
  };

  const handleSearch = () => {
    const values = form.getFieldsValue();
    onSearch(values);
  };

  const handleReset = () => {
    form.resetFields();
    onSearch({});
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSearch}
      className="mb-4"
    >
      <Row gutter={16}>
        <Col lg={8} md={12} sm={24} xs={24}>
          <Form.Item
            name="name"
            label={t('dinein-product-topping-group:search.name')}
          >
            <Input
              placeholder={t(
                'dinein-product-topping-group:search.namePlaceholder',
              )}
            />
          </Form.Item>
        </Col>
        <Col lg={8} md={12} sm={24} xs={24}>
          <Form.Item
            name="product_id"
            label={t('dinein-product-topping-group:search.product')}
          >
            <Select
              placeholder={t(
                'dinein-product-topping-group:search.productPlaceholder',
              )}
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.label as string)
                  .toLowerCase()
                  .indexOf(input.toLowerCase()) >= 0
              }
              options={products}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24} style={{ textAlign: 'right' }}>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleReset}
            style={{ marginRight: 8 }}
          >
            {t('common:buttons.reset')}
          </Button>
          <Button type="primary" icon={<SearchOutlined />} htmlType="submit">
            {t('common:buttons.search')}
          </Button>
        </Col>
      </Row>
    </Form>
  );
};

export default ToppingGroupSearch;
