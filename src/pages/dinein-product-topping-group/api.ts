// filepath: /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/web/cms/src/pages/dinein-product-topping-group/api.ts
import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import { DineinToppingGroup, DineinToppingGroupItem } from './type';

export const MODULE = 'dinein-product-topping-group';
export const MODULE_NAME = 'Nhóm topping dùng tại chỗ';
export const MODULE_POPUP = false;

const toppingGroupUrl = `/api/cms/v1/dinein/topping-groups`;
const productUrl = `/api/cms/v1/dinein/products`;
const toppingUrl = `/api/cms/v1/dinein/toppings`;

// Topping Group APIs
export async function getToppingGroups(
  params: any,
): Promise<ApiResponsePaginationCursor<DineinToppingGroup[]>> {
  const response = await apiService.get<
    ApiResponsePaginationCursor<DineinToppingGroup[]>
  >(toppingGroupUrl, params);
  return response.data;
}

export async function getToppingGroup(
  id: string,
): Promise<ApiResponse<DineinToppingGroup>> {
  const response = await apiService.get<ApiResponse<DineinToppingGroup>>(
    `${toppingGroupUrl}/${id}`,
  );
  return response.data;
}

export async function createToppingGroup(
  payload: any,
): Promise<ApiResponse<DineinToppingGroup>> {
  const response = await apiService.post<ApiResponse<DineinToppingGroup>>(
    toppingGroupUrl,
    payload,
  );
  return response.data;
}

export async function updateToppingGroup(
  id: string,
  payload: any,
): Promise<ApiResponse<DineinToppingGroup>> {
  const response = await apiService.put<ApiResponse<DineinToppingGroup>>(
    `${toppingGroupUrl}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteToppingGroup(
  id: string,
): Promise<ApiResponse<DineinToppingGroup>> {
  const response = await apiService.delete<ApiResponse<DineinToppingGroup>>(
    `${toppingGroupUrl}/${id}`,
  );
  return response.data;
}

export async function getToppingGroupsByProduct(
  productId: string,
): Promise<ApiResponse<DineinToppingGroup[]>> {
  const response = await apiService.get<ApiResponse<DineinToppingGroup[]>>(
    `${productUrl}/${productId}/topping-groups`,
  );
  return response.data;
}

// Topping Group Items APIs
export async function getToppingGroupItems(
  groupId: string,
): Promise<ApiResponse<DineinToppingGroupItem[]>> {
  const response = await apiService.get<ApiResponse<DineinToppingGroupItem[]>>(
    `${toppingGroupUrl}/${groupId}/toppings`,
  );
  return response.data;
}

export async function addToppingToGroup(
  groupId: string,
  payload: any,
): Promise<ApiResponse<DineinToppingGroupItem>> {
  const response = await apiService.post<ApiResponse<DineinToppingGroupItem>>(
    `${toppingGroupUrl}/${groupId}/toppings`,
    payload,
  );
  return response.data;
}

export async function removeToppingFromGroup(
  groupId: string,
  toppingId: string,
): Promise<ApiResponse<any>> {
  const response = await apiService.delete<ApiResponse<any>>(
    `${toppingGroupUrl}/${groupId}/toppings/${toppingId}`,
  );
  return response.data;
}

export async function updateToppingInGroup(
  groupId: string,
  toppingId: string,
  payload: any,
): Promise<ApiResponse<DineinToppingGroupItem>> {
  const response = await apiService.put<ApiResponse<DineinToppingGroupItem>>(
    `${toppingGroupUrl}/${groupId}/toppings/${toppingId}`,
    payload,
  );
  return response.data;
}

// Get available toppings for selection
export async function getAvailableToppings(
  params: any,
): Promise<ApiResponse<any[]>> {
  const response = await apiService.get<ApiResponse<any[]>>(toppingUrl, params);
  return response.data;
}

// Get product options for dropdown
export async function getProductOptions(): Promise<ApiResponse<any[]>> {
  const response = await apiService.get<ApiResponse<any[]>>(`${productUrl}`);
  return response.data;
}
