import { makeAutoObservable } from 'mobx';
import { DineinToppingGroup, DineinToppingGroupItem } from './type';

export class DineinProductToppingGroupStore {
  currentGroup?: DineinToppingGroup = undefined;
  currentToppings: DineinToppingGroupItem[] = [];
  isFormOpen: boolean = false;
  isItemFormOpen: boolean = false;
  isLoading: boolean = false;
  selectedProductId?: number = undefined;

  constructor() {
    makeAutoObservable(this);
  }

  setCurrentGroup = (group: DineinToppingGroup) => {
    this.currentGroup = group;
  };

  clearCurrentGroup = () => {
    this.currentGroup = undefined;
  };

  setCurrentToppings = (toppings: DineinToppingGroupItem[]) => {
    this.currentToppings = toppings;
  };

  addTopping = (topping: DineinToppingGroupItem) => {
    this.currentToppings.push(topping);
  };

  removeTopping = (toppingId: number) => {
    this.currentToppings = this.currentToppings.filter(
      (item) => item.topping_id !== toppingId,
    );
  };

  clearCurrentToppings = () => {
    this.currentToppings = [];
  };

  openForm = () => {
    this.isFormOpen = true;
  };

  closeForm = () => {
    this.isFormOpen = false;
  };

  openItemForm = () => {
    this.isItemFormOpen = true;
  };

  closeItemForm = () => {
    this.isItemFormOpen = false;
  };

  setLoading = (loading: boolean) => {
    this.isLoading = loading;
  };

  setSelectedProductId = (productId?: number) => {
    this.selectedProductId = productId;
  };
}

export default new DineinProductToppingGroupStore();
