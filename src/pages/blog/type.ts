import dayjs, { Dayjs } from 'dayjs';

// Pagination types
export interface CursorPagination {
  cursor?: string;
  limit?: number;
}

export interface CursorResponse {
  has_next: boolean;
  has_previous: boolean;
  next_cursor?: string;
  previous_cursor?: string;
  total_count?: number;
}

export enum BlogPostStatus {
  DRAFT = 'draft',
  REVIEW = 'review',
  PUBLISHED = 'published',
  SCHEDULED = 'scheduled',
  ARCHIVED = 'archived',
  REJECTED = 'rejected',
}

export enum BlogPostType {
  POST = 'post',
  PAGE = 'page',
  ANNOUNCEMENT = 'announcement',
}

export const BLOG_POST_STATUS_LABELS = {
  [BlogPostStatus.DRAFT]: 'Bản nháp',
  [BlogPostStatus.REVIEW]: 'Chờ duyệt',
  [BlogPostStatus.PUBLISHED]: 'Đã xuất bản',
  [BlogPostStatus.SCHEDULED]: 'Đã lên lịch',
  [BlogPostStatus.ARCHIVED]: 'Lưu trữ',
  [BlogPostStatus.REJECTED]: 'Từ chối',
};

export const BLOG_POST_TYPE_LABELS = {
  [BlogPostType.POST]: 'Bài viết',
  [BlogPostType.PAGE]: 'Trang',
  [BlogPostType.ANNOUNCEMENT]: 'Thông báo',
};

// SEO metadata interface
export interface SEOMetadata {
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string[];
  og_title?: string;
  og_description?: string;
  og_image?: string;
  twitter_title?: string;
  twitter_description?: string;
  twitter_card?: string;
  focus_keyword?: string;
  canonical_url?: string;
}

// API DTOs matching backend
export interface BlogPostCreateRequest {
  title: string;
  slug?: string;
  content: string;
  excerpt?: string;
  status: BlogPostStatus;
  category_id?: number;
  tag_ids?: number[];
  type: BlogPostType;
  is_featured: boolean;
  allow_comments: boolean;
  is_sticky?: boolean;
  password?: string;
  featured_image?: string;
  scheduled_at?: string;
  seo?: SEOMetadata;
}

export interface BlogPostUpdateRequest {
  title?: string;
  slug?: string;
  content?: string;
  excerpt?: string;
  status?: BlogPostStatus;
  category_id?: number;
  tag_ids?: number[];
  type?: BlogPostType;
  is_featured?: boolean;
  allow_comments?: boolean;
  is_sticky?: boolean;
  password?: string;
  featured_image?: string;
  scheduled_at?: string;
  seo?: SEOMetadata;
}

export interface BlogPostResponse {
  id: number;
  tenant_id: number;
  website_id: number;
  slug: string;
  title: string;
  content: string;
  excerpt?: string;
  author_id: number;
  category_id?: number;
  tag_ids: number[];
  type: BlogPostType;
  is_featured: boolean;
  allow_comments: boolean;
  is_sticky: boolean;
  featured_image?: string;
  view_count: number;
  comment_count: number;
  scheduled_at?: string;
  published_at?: string;
  status: BlogPostStatus;
  created_at: string;
  updated_at: string;
  seo?: SEOMetadata;
  category?: BlogCategoryResponse;
  tags?: BlogTagResponse[];
}

export interface BlogCategoryResponse {
  id: number;
  tenant_id: number;
  website_id: number;
  name: string;
  slug: string;
  description?: string;
  parent_id?: number;
  sort_order: number;
  post_count: number;
  is_active: boolean;
  meta_title?: string;
  meta_keywords?: string;
  created_at: string;
  updated_at: string;
}

export interface BlogTagResponse {
  id: number;
  tenant_id: number;
  website_id: number;
  name: string;
  slug: string;
  description?: string;
  color?: string;
  post_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface BlogCategoryCreateRequest {
  tenant_id: number;
  website_id: number;
  name: string;
  slug: string;
  description?: string;
  parent_id?: number;
  sort_order: number;
  is_active: boolean;
  meta_title?: string;
  meta_keywords?: string;
}

export interface BlogCategoryUpdateRequest {
  name?: string;
  slug?: string;
  description?: string;
  parent_id?: number;
  sort_order?: number;
  is_active?: boolean;
  meta_title?: string;
  meta_keywords?: string;
}

export interface BlogTagCreateRequest {
  tenant_id: number;
  website_id: number;
  name: string;
  slug: string;
  description?: string;
  color?: string;
  is_active: boolean;
}

export interface BlogTagUpdateRequest {
  name?: string;
  slug?: string;
  description?: string;
  color?: string;
  is_active?: boolean;
}

export interface BlogPostFilter extends CursorPagination {
  title?: string;
  status?: BlogPostStatus;
  type?: BlogPostType;
  category_id?: number;
  author_id?: number;
  is_featured?: boolean;
  tag_id?: number;
  date_from?: string;
  date_to?: string;
  sort_by?:
    | 'id'
    | 'title'
    | 'created_at'
    | 'updated_at'
    | 'published_at'
    | 'view_count';
  sort_order?: 'asc' | 'desc';
}

export interface BlogPostListResponse {
  posts: BlogPostResponse[];
  meta?: {
    next_cursor?: string;
    has_more: boolean;
  };
}

export interface BlogCategoryListResponse {
  categories: BlogCategoryResponse[];
  pagination?: CursorResponse;
}

export interface BlogTagListResponse {
  tags: BlogTagResponse[];
  pagination?: CursorResponse;
}

export interface BlogCategoryFilter extends CursorPagination {
  website_id?: number;
  search?: string;
  is_active?: boolean;
  parent_id?: number;
  sort_by?:
    | 'id'
    | 'name'
    | 'created_at'
    | 'updated_at'
    | 'sort_order'
    | 'post_count';
  sort_order?: 'asc' | 'desc';
}

export interface BlogTagFilter extends CursorPagination {
  website_id?: number;
  search?: string;
  is_active?: boolean;
  sort_by?: 'id' | 'name' | 'created_at' | 'updated_at' | 'usage_count';
  sort_order?: 'asc' | 'desc';
}

// Main Blog interface - use BlogPostResponse for API responses
export interface Blog extends BlogPostResponse {
  // Additional UI-specific fields if needed
  category_ids?: number[];
  tag_ids?: number[];
  related_post_ids?: number[];
  scheduleAt?: Dayjs;
}

export const CommentStatusOptions = [
  {
    label: 'Cho phép bình luận',
    value: true,
  },
  {
    label: 'Không cho phép bình luận',
    value: false,
  },
];

// Define specific fields that can be managed in the drawer
export interface BlogDrawerData {
  // Tags and Categories
  tag_ids?: number[];
  category_id?: number;

  // Publishing settings
  scheduled_at?: string;

  // Content metadata
  excerpt?: string;
  is_featured?: boolean;
  is_sticky?: boolean;

  // Media
  featured_image?: string;

  // Related posts
  related_post_ids?: number[];

  // Status and comments
  status?: BlogPostStatus;
  allow_comments?: boolean;
  password?: string;

  // SEO metadata
  seo?: SEOMetadata;

  author_id?: number;
}

// Related Posts Types
export interface RelatedPost {
  id: number;
  post_id: number;
  related_post_id: number;
  priority: number;
  is_bidirectional: boolean;
  created_at: string;
  updated_at: string;
  post?: BlogPostResponse;
  related_post?: BlogPostResponse;
}

export interface RelatedPostCreate {
  post_id: number;
  related_post_id: number;
  priority?: number;
  is_bidirectional?: boolean;
}

export interface RelatedPostBulkCreate {
  post_id: number;
  is_bidirectional?: boolean;
  related_posts: Array<{
    related_post_id: number;
    priority?: number;
  }>;
}

export interface RelatedPostUpdate {
  priority?: number;
  is_bidirectional?: boolean;
}

// Type-safe function to extract only allowed drawer fields
export const extractDrawerFields = (data: Partial<Blog>): BlogDrawerData => {
  const allowedFields: (keyof BlogDrawerData)[] = [
    'tag_ids',
    'category_id',
    'scheduled_at',
    'excerpt',
    'is_featured',
    'is_sticky',
    'featured_image',
    'related_post_ids',
    'status',
    'allow_comments',
    'password',
    'seo',
    'author_id',
  ];

  const drawerData: BlogDrawerData = {};

  allowedFields.forEach((field) => {
    if (data[field] !== undefined) {
      (drawerData as any)[field] = data[field];
    }
  });

  return drawerData;
};

export const transformBlog = (values: BlogPostResponse): Blog => {
  const item: Blog = { ...values };

  // Extract category and tag IDs for UI
  if (values.category) {
    item.category_ids = [values.category.id];
  }

  if (values.tags && values.tags.length > 0) {
    item.tag_ids = values.tags.map((tag) => tag.id);
  }

  // Convert scheduled_at to Dayjs for UI components
  if (values.scheduled_at) {
    item.scheduleAt = dayjs(values.scheduled_at);
  }

  return item;
};
