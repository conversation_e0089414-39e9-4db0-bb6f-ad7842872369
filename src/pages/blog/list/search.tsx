import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Col, DatePicker, Form, Input, Row, Select } from 'antd';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';

import { MODULE } from '../config';
import { CommentStatusOptions } from '../type';

// Define status options for the filter
const STATUS_OPTIONS = [
  { value: 'draft', label: 'Bài viết nháp' },
  { value: 'pending', label: 'Bài chờ biên tập' },
  { value: 'approved', label: 'Bài viết chờ xuất bản' },
  { value: 'schedule', label: 'Bài viết hẹn giờ xuất bản' },
  { value: 'published', label: 'Bài viết đã xuất bản' },
  { value: 'return', label: '<PERSON>ài viết trả lại' },
  { value: 'trash', label: 'Bài viết đã gỡ' },
  { value: 'delete', label: 'Bài viết đã xóa' },
];

const { Option } = Select;
const { RangePicker } = DatePicker;

function SearchForm(props: any) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();

  // Update the form with status filter when it changes from outside
  useEffect(() => {
    if (props.statusFilter) {
      // If statusFilter is 'mine', we should clear the status field as it's a special filter
      if (props.statusFilter === 'mine') {
        form.setFieldsValue({ status: undefined, is_mine: true });
      } else {
        form.setFieldsValue({ status: props.statusFilter, is_mine: undefined });
      }
    } else {
      // Clear status when statusFilter is null
      form.setFieldsValue({ status: undefined, is_mine: undefined });
    }
  }, [props.statusFilter, form]);

  const onFinish = (values: any) => {
    // Transform date range to API format
    if (values.dateRange && values.dateRange.length === 2) {
      values.created_from = values.dateRange[0].toISOString();
      values.created_to = values.dateRange[1].toISOString();
      delete values.dateRange;
    }

    // Handle special case for 'mine' filter
    if (props.statusFilter === 'mine' && !values.status) {
      values.is_mine = true;
    }

    logger('[search values]', values);
    props.onChange(values);
  };

  function handlerRefresh() {
    form.resetFields();

    // When resetting, preserve the status filter from URL if it exists
    const resetValues = {};
    if (props.statusFilter) {
      if (props.statusFilter === 'mine') {
        resetValues.is_mine = true;
      } else {
        resetValues.status = props.statusFilter;
      }
    }

    props.onChange(resetValues);
  }

  return (
    <Form
      layout="vertical"
      form={form}
      name="frmSearch"
      onFinish={onFinish}
      className="p-4 bg-white rounded-lg mb-4"
    >
      <Row>
        <Col span={24}>
          <Row gutter={16}>
            {/* Search text */}
            <Col xs={24} md={6} lg={4}>
              <Form.Item
                name="search"
                label="Tìm kiếm"
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Input placeholder="Tìm theo tiêu đề, nội dung..." />
              </Form.Item>
            </Col>

            {/* Date range filter */}
            <Col xs={24} md={6} lg={4}>
              <Form.Item
                name="dateRange"
                label="Khoảng thời gian"
                rules={[{ required: false, message: t('inputData') }]}
              >
                <RangePicker placeholder={['Từ ngày', 'Đến ngày']} />
              </Form.Item>
            </Col>

            {/* Sort by */}
            <Col xs={24} md={6} lg={4}>
              <Form.Item
                name="sort_by"
                label="Sắp xếp theo"
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Select placeholder="Chọn cách sắp xếp" allowClear>
                  <Option value="created_at">Ngày tạo</Option>
                  <Option value="updated_at">Ngày cập nhật</Option>
                  <Option value="published_at">Ngày xuất bản</Option>
                  <Option value="title">Tiêu đề</Option>
                </Select>
              </Form.Item>
            </Col>

            {/* Sort order */}
            <Col xs={24} md={6} lg={4}>
              <Form.Item
                name="sort_order"
                label="Thứ tự"
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Select placeholder="Chọn thứ tự" allowClear>
                  <Option value="desc">Mới nhất</Option>
                  <Option value="asc">Cũ nhất</Option>
                </Select>
              </Form.Item>
            </Col>

            {/* Status filter */}
            {/* <Col xs={24} md={6} lg={4}>
              <Form.Item
                name="status"
                label="Trạng thái"
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Select placeholder="Trạng thái bài viết" allowClear>
                  {STATUS_OPTIONS.map(option => (
                    <Option key={option.value} value={option.value}>{option.label}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col> */}

            {/* Is mine filter */}
            <Col xs={24} md={6} lg={4}>
              <Form.Item
                name="is_mine"
                label="Bài viết của tôi"
                valuePropName="checked"
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Select placeholder="Bài viết của tôi" allowClear>
                  <Option value={true}>Chỉ của tôi</Option>
                  <Option value={false}>Tất cả</Option>
                </Select>
              </Form.Item>
            </Col>

            {/* Comment status */}
            <Col xs={24} md={6} lg={4}>
              <Form.Item
                name="comment_status"
                label="Bình luận"
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Select placeholder="Trạng thái bình luận" allowClear>
                  <Option value={true}>Cho phép</Option>
                  <Option value={false}>Không cho phép</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Col>
      </Row>
      <Row>
        <Col span={24} style={{ textAlign: 'right' }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: '15px' }}
            loading={props.loading}
            icon={<SearchOutlined />}
          >
            {t('search')}
          </Button>

          <Button
            type="default"
            htmlType="button"
            loading={props.loading}
            onClick={handlerRefresh}
            icon={<ReloadOutlined />}
          >
            {t('reset')}
          </Button>
        </Col>
      </Row>
    </Form>
  );
}

export default SearchForm;
