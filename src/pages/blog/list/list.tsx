import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import { Col, Dropdown, message, Popconfirm, Row, Space, Table } from 'antd';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { BackButton, ButtonLink } from '../../../components/button';
import { useCursorPagination } from '../../../components/pagination';
import CursorPagination from '../../../components/pagination/cursor-pagination';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import ModalForm from '../form/modal';
import useBlogStore from '../store';
import { Blog } from '../type';
import Search from './search';

export default function List({
  statusFilter,
}: {
  statusFilter: string | null;
}) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useBlogStore() as any; // Type cast to avoid TS error

  const {
    afterKey,
    isNext,
    isBack,
    setNextCursor,
    goNext,
    goBack,
    resetPagination,
  } = useCursorPagination({
    defaultLimit: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<Blog[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();

  const fetchData = useCallback(
    async (payload?: any, cursor?: string) => {
      const params = {
        ...query,
        ...filters,
        ...payload,
        limit: 10,
      };

      // Apply status filter from query params if available
      if (statusFilter && !params.status) {
        if (statusFilter === 'mine') {
          // Special case: filter by current user's posts
          params.is_mine = true;
          delete params.status; // Ensure status is not set when filtering by mine
        } else {
          params.status = statusFilter;
        }
      }

      // Add cursor if we have one
      if (cursor) {
        params.cursor = cursor;
      }

      delete params.total;
      logger('[params]', params);

      try {
        const response = await getItems(params);
        if (response.status.success) {
          // Ensure data is always an array to prevent Table errors
          const data = Array.isArray(response.data) ? response.data : [];
          setItems(data);
          //setTotal(response.meta.total || 0);
          setNextCursor(response.meta.next_cursor);

          // Debug logging
          logger('[fetchData] response data type:', typeof response.data);
          logger(
            '[fetchData] response data is array:',
            Array.isArray(response.data),
          );
          logger('[fetchData] response data length:', data.length);
        } else {
          message.error(response.status.message);
          // Set empty array on error to prevent Table issues
          setItems([]);
          setTotal(0);
          setNextCursor(undefined);
        }
      } catch (error) {
        logger('[fetchData] error:', error);
        message.error('Đã có lỗi xảy ra khi tải dữ liệu');
        // Set empty array on error to prevent Table issues
        setItems([]);
        setTotal(0);
        setNextCursor(undefined);
      }
    },
    [query, filters, statusFilter, setNextCursor, logger],
  );

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });

    // Preserve the status filter from URL when applying other filters
    const newFilters = { ...values };

    // Don't override status from URL with form values if statusFilter is set
    if (statusFilter && newFilters.status === undefined) {
      if (statusFilter === 'mine') {
        newFilters.is_mine = true;
        delete newFilters.status;
      } else {
        newFilters.status = statusFilter;
      }
    }

    // Reset pagination when filters change
    resetPagination();
    setFilters(newFilters);
    fetchData(newFilters, undefined);
  };

  // Fetch data when afterKey changes (includes initial load when afterKey is undefined)
  useEffect(() => {
    fetchData(undefined, afterKey);
  }, [afterKey, fetchData]);

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData(undefined, afterKey);
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    const recordId = record.id;

    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(recordId);
        setShowModal(true);
      } else {
        //console.log('[BLOG][EDIT]', getPath(`/${MODULE}/${recordId}`))
        navigate(`/${MODULE}/${recordId}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`${MODULE}/create`);
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData(undefined, afterKey);
  };

  const columns = [
    // {
    //   title: t('id'),
    //   dataIndex: 'id',
    //   key: 'id',
    //   render: (dom: any, record: any) => (
    //     <span
    //       className="text-blue-600 cursor-pointer"
    //       onClick={() => navigate(`/${MODULE}/${record.id}`)}
    //     >
    //       {dom}
    //     </span>
    //   ),
    // },
    {
      title: t('Tiêu đề bài viết'),
      dataIndex: 'title',
      key: 'title',
      render: (dom: any, record: any) => {
        const recordId = record?.id;
        return (
          <div
            className="flex justify-start max-w-[400px]"
            onClick={() => navigate(`/${MODULE}/${recordId}`)}
          >
            <div className="max-w-[100px] mr-2">
              <img
                src={
                  record.featured_image ??
                  'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSrJgwdOAjqaZGS7kn35IVm_ZN6E4XFuJ7V_g&s'
                }
                alt="img"
                className="w-full h-auto rounded"
              />
            </div>
            <div className="max-w-[300px]">
              <div className="text-blue-600 cursor-pointer font-medium mb-1">
                {record.title ?? 'Không có tiêu đề'}
              </div>
              <div className="text-gray-500 text-sm mb-1">
                {record.excerpt
                  ? record.excerpt.length > 100
                    ? record.excerpt.substring(0, 100) + '...'
                    : record.excerpt
                  : 'Chưa có mô tả'}
              </div>
              <div className="text-xs text-gray-400">
                {record.categories?.length > 0
                  ? `${record.categories.length} danh mục`
                  : 'Chưa có danh mục'}
                {record.tags?.length > 0 && ` • ${record.tags.length} thẻ`}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (dom: any, record: any) => {
        const getStatusColor = (status: string) => {
          switch (status) {
            case 'published':
              return 'text-green-600 bg-green-100';
            case 'draft':
              return 'text-gray-600 bg-gray-100';
            case 'review':
              return 'text-yellow-600 bg-yellow-100';
            case 'scheduled':
              return 'text-purple-600 bg-purple-100';
            case 'archived':
              return 'text-orange-600 bg-orange-100';
            case 'rejected':
              return 'text-red-600 bg-red-100';
            default:
              return 'text-gray-600 bg-gray-100';
          }
        };

        return (
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-2">
              <span
                className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(record.status)}`}
              >
                {record.status === 'published'
                  ? 'Đã xuất bản'
                  : record.status === 'draft'
                    ? 'Bản nháp'
                    : record.status === 'review'
                      ? 'Đang xem xét'
                      : record.status === 'scheduled'
                        ? 'Đã lên lịch'
                        : record.status === 'archived'
                          ? 'Đã lưu trữ'
                          : record.status === 'rejected'
                            ? 'Đã từ chối'
                            : record.status || 'Không xác định'}
              </span>
            </div>
            {record.allow_comments !== undefined && (
              <div className="text-xs text-gray-500">
                Bình luận:{' '}
                {record.allow_comments ? 'Cho phép' : 'Không cho phép'}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: 'Thông tin thời gian',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (dom: any, record: any) => {
        const formatDate = (dateStr: string | undefined) => {
          if (!dateStr) return 'Chưa có';
          const date = new Date(dateStr);
          return date.toLocaleString('vi-VN');
        };

        return (
          <div className="flex flex-col text-sm">
            <div className="mb-1 flex flex-col">
              <div className="text-gray-500 text-xs">Ngày tạo:</div>
              <div className="font-medium">{formatDate(record.created_at)}</div>
            </div>
            <div className="mb-1 flex flex-col">
              <div className="text-gray-500 text-xs">Ngày cập nhật:</div>
              <div className="">{formatDate(record.updated_at)}</div>
            </div>
            {record.published_at && (
              <div className="mb-1 flex flex-col">
                <div className="text-gray-500 text-xs">Ngày xuất bản:</div>
                <div className="text-green-600">
                  {formatDate(record.published_at)}
                </div>
              </div>
            )}
            {record.scheduled_at && (
              <div className="mb-1 flex flex-col">
                <div className="text-gray-500 text-xs">Lên lịch:</div>
                <div className="text-orange-600 font-medium">
                  {formatDate(record.scheduled_at)}
                </div>
              </div>
            )}
            {record.author_id && (
              <div className="mt-2 pt-2 border-t border-gray-200">
                <div className="text-gray-500 text-xs">ID Tác giả:</div>
                <div className="text-blue-600 text-xs">{record.author_id}</div>
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: 'Thao tác',
      dataIndex: '',
      key: 'action',
      render: (dom: any, record: any) => {
        const recordId = record?.id;
        return (
          <Dropdown.Button
            menu={{
              items: [
                {
                  key: 'delete',
                  label: (
                    <Popconfirm
                      placement="top"
                      title="Bạn có chắc chắn muốn xóa bài viết này?"
                      onConfirm={() => handleDelete(recordId)}
                      okText="Có"
                      cancelText="Không"
                    >
                      <DeleteOutlined /> Xóa
                    </Popconfirm>
                  ),
                },
              ],
            }}
            onClick={() => handleActions('edit', record)}
          >
            <EditOutlined /> Chỉnh sửa
          </Dropdown.Button>
        );
      },
    },
  ];

  return (
    <div>
      <div className="bg-gray flex justify-between p-4">
        <div className="flex items-center space-x-2">
          <BackButton destination="dashboard" />
          <div className="text-xl font-bold">
            Quản lý Blog
            {statusFilter && (
              <span className="ml-2 text-base text-blue-600">
                {statusFilter === 'draft'
                  ? '- Bài viết nháp'
                  : statusFilter === 'pending'
                    ? '- Bài chờ biên tập'
                    : statusFilter === 'approved'
                      ? '- Bài viết chờ xuất bản'
                      : statusFilter === 'schedule'
                        ? '- Bài viết hẹn giờ xuất bản'
                        : statusFilter === 'published'
                          ? '- Bài viết đã xuất bản'
                          : statusFilter === 'return'
                            ? '- Bài viết trả lại'
                            : statusFilter === 'trash'
                              ? '- Bài viết đã gỡ'
                              : statusFilter === 'delete'
                                ? '- Bài viết đã xóa'
                                : statusFilter === 'mine'
                                  ? '- Bài viết của tôi'
                                  : ''}
              </span>
            )}
          </div>
        </div>
        <div className="gap-4">
          <ButtonLink
            type="primary"
            icon={<PlusCircleOutlined />}
            to={`${MODULE}/create`}
          >
            Thêm bài viết
          </ButtonLink>
        </div>
      </div>
      <Search
        query={query}
        loading={loading}
        onChange={handleFilters}
        statusFilter={statusFilter}
      />
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey={(record) => String(record.id || Math.random())}
          loading={loading}
          columns={columns}
          dataSource={Array.isArray(items) ? items : []}
          pagination={false}
        ></Table>
        <Row justify="end" className="p-4">
          <Col>
            <CursorPagination
              total={total}
              isNext={isNext}
              isBack={isBack}
              goNext={goNext}
              goBack={goBack}
            />
          </Col>
        </Row>
        {MODULE_POPUP && (
          <ModalForm
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></ModalForm>
        )}
      </Space>
    </div>
  );
}
