import ConsoleService from 'services/console.service';
import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import {
  BlogPostResponse,
  BlogPostCreateRequest,
  BlogPostUpdateRequest,
  BlogPostFilter,
  BlogPostListResponse,
} from './type';
// Status Statistics Interface
export interface StatusCounts {
  draft: number;
  pending: number;
  approved: number;
  schedule: number;
  published: number;
  return: number;
  trash: number;
  storage: number;
  request: number;
  auto: number;
  delete: number;
  total: number;
}

export interface StatusStatistics {
  status_counts: StatusCounts;
}

export const MODULE = 'blog';
export const MODULE_NAME = 'Blog Management';
export const MODULE_POPUP = false;
const logger = ConsoleService.register(MODULE);

const postsUrl = `/api/cms/v1/blog/posts`;
const websitePostsUrl = `/api/cms/v1/websites`;
const relatedPostsUrl = `/api/cms/v1/blog/related-posts`;
const seoUrl = `/blog/posts`;

// Posts API Functions
export async function getItems(
  params: BlogPostFilter,
): Promise<ApiResponse<BlogPostListResponse>> {
  logger('getItems', params);

  const response = await apiService.get<ApiResponse<BlogPostListResponse>>(
    postsUrl,
    { params },
  );
  return response.data;
}

export async function getItem(
  id: string,
): Promise<ApiResponse<BlogPostResponse>> {
  const response = await apiService.get<ApiResponse<BlogPostResponse>>(
    `${postsUrl}/${id}`,
  );
  return response.data;
}

export async function getItemBySlug(
  slug: string,
): Promise<ApiResponse<BlogPostResponse>> {
  const response = await apiService.get<ApiResponse<BlogPostResponse>>(
    `${postsUrl}/slug/${slug}`,
  );
  return response.data;
}

export async function createItem(
  payload: BlogPostCreateRequest,
): Promise<ApiResponse<BlogPostResponse>> {
  const response = await apiService.post<ApiResponse<BlogPostResponse>>(
    postsUrl,
    payload,
  );
  return response.data;
}

export async function updateItem(
  id: string,
  payload: BlogPostUpdateRequest,
): Promise<ApiResponse<BlogPostResponse>> {
  const response = await apiService.put<ApiResponse<BlogPostResponse>>(
    `${postsUrl}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(
  id: string,
): Promise<ApiResponse<BlogPostResponse>> {
  const response = await apiService.delete<ApiResponse<BlogPostResponse>>(
    `${postsUrl}/${id}`,
  );
  return response.data;
}

export async function bulkDeleteItems(
  ids: string[],
): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${postsUrl}/bulk-delete`,
    { ids },
  );
  return response.data;
}

export async function publishPost(
  id: string,
): Promise<ApiResponse<BlogPostResponse>> {
  const response = await apiService.post<ApiResponse<BlogPostResponse>>(
    `${postsUrl}/${id}/publish`,
  );
  return response.data;
}

export async function unpublishPost(
  id: string,
): Promise<ApiResponse<BlogPostResponse>> {
  const response = await apiService.post<ApiResponse<BlogPostResponse>>(
    `${postsUrl}/${id}/unpublish`,
  );
  return response.data;
}

export async function exportPosts(params: any): Promise<ApiResponse<any>> {
  const response = await apiService.get<ApiResponse<any>>(
    `${postsUrl}/export`,
    params,
  );
  return response.data;
}

export async function importPosts(payload: any): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${postsUrl}/import`,
    payload,
  );
  return response.data;
}

export async function searchPosts(
  params: any,
): Promise<ApiResponse<BlogPostResponse[]>> {
  const response = await apiService.get<ApiResponse<BlogPostResponse[]>>(
    `${postsUrl}/search`,
    params,
  );
  return response.data;
}

// Get published posts
export async function getPublishedPosts(
  params: BlogPostFilter,
): Promise<ApiResponse<BlogPostListResponse>> {
  const publishedParams = { ...params, status: 'published' };
  return getItems(publishedParams);
}

// Get featured posts
export async function getFeaturedPosts(
  params: BlogPostFilter,
): Promise<ApiResponse<BlogPostListResponse>> {
  const featuredParams = { ...params, is_featured: true };
  return getItems(featuredParams);
}

// Get related posts for a specific post
export async function getRelatedPostsForPost(
  postId: string,
  params?: { limit?: number },
): Promise<ApiResponse<BlogPostResponse[]>> {
  const response = await apiService.get<ApiResponse<BlogPostResponse[]>>(
    `${postsUrl}/${postId}/related`,
    { params },
  );
  return response.data;
}

// Legacy compatibility functions
export async function getOptions(): Promise<ApiResponse<BlogPostListResponse>> {
  return getItems({ limit: 100 });
}

export async function getSearch(
  params: any,
): Promise<ApiResponse<BlogPostResponse[]>> {
  return searchPosts(params);
}

export async function getAll(): Promise<ApiResponse<BlogPostListResponse>> {
  return getItems({ limit: 1000 });
}

// Related Posts API Functions
export async function getRelatedPosts(
  params: any,
): Promise<ApiResponsePaginationCursor<any[]>> {
  const response = await apiService.get<ApiResponsePaginationCursor<any[]>>(
    relatedPostsUrl,
    { params },
  );
  return response.data;
}

// Get related posts for a specific post (based on API docs)
export async function getPostRelatedPosts(
  postId: string,
  params?: any,
): Promise<ApiResponse<any[]>> {
  const response = await apiService.get<ApiResponse<any[]>>(
    `${postsUrl}/${postId}/related-posts`,
    { params },
  );
  return response.data;
}

// Update related posts for a specific post (based on API docs)
export async function updatePostRelatedPosts(
  postId: string,
  payload: {
    related_post_ids: number[];
    is_bidirectional?: boolean;
  },
): Promise<ApiResponse<any[]>> {
  const response = await apiService.post<ApiResponse<any[]>>(
    `${postsUrl}/${postId}/related`,
    payload,
  );
  return response.data;
}

export async function createRelatedPost(payload: {
  post_id: number;
  related_post_id: number;
  priority?: number;
  is_bidirectional?: boolean;
}): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    relatedPostsUrl,
    payload,
  );
  return response.data;
}

export async function bulkCreateRelatedPosts(payload: {
  post_id: number;
  is_bidirectional?: boolean;
  related_posts: Array<{
    related_post_id: number;
    priority?: number;
  }>;
}): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${relatedPostsUrl}/bulk`,
    payload,
  );
  return response.data;
}

export async function updateRelatedPost(
  relationId: string,
  payload: {
    priority?: number;
    is_bidirectional?: boolean;
  },
): Promise<ApiResponse<any>> {
  const response = await apiService.put<ApiResponse<any>>(
    `${relatedPostsUrl}/${relationId}`,
    payload,
  );
  return response.data;
}

export async function deleteRelatedPost(
  relationId: string,
): Promise<ApiResponse<any>> {
  const response = await apiService.delete<ApiResponse<any>>(
    `${relatedPostsUrl}/${relationId}`,
  );
  return response.data;
}

// Status Statistics API Function
export async function getStatusStatistics(): Promise<
  ApiResponse<StatusStatistics>
> {
  const response = await apiService.get<ApiResponse<StatusStatistics>>(
    '/api/cms/v1/blog/statistics/status',
  );
  return response.data;
}

// Website-based blog posts API (legacy support)
export async function getWebsitePosts(
  websiteId: string,
  params: any = {},
): Promise<ApiResponse<BlogPostListResponse>> {
  const response = await apiService.get<ApiResponse<BlogPostListResponse>>(
    `${websitePostsUrl}/${websiteId}/blog/posts`,
    { params },
  );
  return response.data;
}

// SEO Data API Functions (based on swagger documentation)
export async function fetchSEOData(postId: string): Promise<ApiResponse<any>> {
  const response = await apiService.get<ApiResponse<any>>(
    `${seoUrl}/${postId}/seo`,
  );
  return response.data;
}

// Generate SEO meta tags for blog post
export async function generateSEOTags(
  postId: string,
): Promise<ApiResponse<any>> {
  const response = await apiService.get<ApiResponse<any>>(
    `${seoUrl}/${postId}/seo/tags`,
  );
  return response.data;
}
