import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  EditOutlined,
  EyeOutlined,
  FileTextOutlined,
  MessageOutlined,
} from '@ant-design/icons';
import { Card, Col, DatePicker, Row, Spin, Statistic, message } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';
import { apiService } from '../../../services/api.service';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';

const { RangePicker } = DatePicker;

interface BlogStatistics {
  total_posts: number;
  published_posts: number;
  draft_posts: number;
  pending_posts?: number;
  scheduled_posts?: number;
  total_comments: number;
  total_views: number;
  period_posts?: number;
  period_views?: number;
  period_comments?: number;
}

const BlogStatistics: React.FC = () => {
  const logger = ConsoleService.register(MODULE);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<BlogStatistics | null>(null);
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>([
    dayjs().subtract(30, 'days'),
    dayjs(),
  ]);

  // Fetch blog statistics
  const fetchStatistics = React.useCallback(
    async (dateFrom?: string, dateTo?: string) => {
      try {
        setLoading(true);

        const params: any = {
          period: 30,
        };

        if (dateFrom && dateTo) {
          params.date_from = dateFrom;
          params.date_to = dateTo;
        }

        const response = await apiService.get('/api/cms/v1/blog/admin/stats', {
          params,
        });

        if (response.data.status.success) {
          setStatistics(response.data.data);
          logger('Statistics loaded:', response.data.data);
        } else {
          message.error(
            response.data.status.message || 'Không thể tải thống kê',
          );
        }
      } catch (error) {
        logger('Error fetching statistics:', error);
        message.error('Lỗi khi tải thống kê blog');
      } finally {
        setLoading(false);
      }
    },
    [logger],
  );

  // Handle date range change
  const handleDateRangeChange = (
    dates: [Dayjs | null, Dayjs | null] | null,
    dateStrings: [string, string],
  ) => {
    if (dates && dates[0] && dates[1]) {
      const [start, end] = dates;
      setDateRange([start, end]);
      fetchStatistics(dateStrings[0], dateStrings[1]);
    }
  };

  // Load initial statistics
  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  if (loading && !statistics) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>Đang tải thống kê...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* Date Range Picker */}
      <div style={{ marginBottom: '24px' }}>
        <RangePicker
          value={dateRange}
          onChange={handleDateRangeChange}
          format="DD/MM/YYYY"
          placeholder={['Từ ngày', 'Đến ngày']}
          style={{ marginBottom: '16px' }}
        />
      </div>

      <Spin spinning={loading}>
        {/* Overall Statistics */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Card>
              <Statistic
                title="Tổng bài viết"
                value={statistics?.total_posts || 0}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Card>
              <Statistic
                title="Đã xuất bản"
                value={statistics?.published_posts || 0}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Card>
              <Statistic
                title="Bản nháp"
                value={statistics?.draft_posts || 0}
                prefix={<EditOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Card>
              <Statistic
                title="Đã lên lịch"
                value={statistics?.scheduled_posts || 0}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        {/* Engagement Statistics */}
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8}>
            <Card>
              <Statistic
                title="Tổng lượt xem"
                value={statistics?.total_views || 0}
                prefix={<EyeOutlined />}
                valueStyle={{ color: '#13c2c2' }}
              />
            </Card>
          </Col>

          <Col xs={24} sm={12} md={8}>
            <Card>
              <Statistic
                title="Tổng bình luận"
                value={statistics?.total_comments || 0}
                prefix={<MessageOutlined />}
                valueStyle={{ color: '#eb2f96' }}
              />
            </Card>
          </Col>

          <Col xs={24} sm={12} md={8}>
            <Card>
              <Statistic
                title="Bài viết trong kỳ"
                value={statistics?.period_posts || 0}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        {/* Period Statistics (if available) */}
        {(statistics?.period_views || statistics?.period_comments) && (
          <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
            <Col xs={24} sm={12}>
              <Card>
                <Statistic
                  title="Lượt xem trong kỳ"
                  value={statistics?.period_views || 0}
                  prefix={<EyeOutlined />}
                  valueStyle={{ color: '#13c2c2' }}
                />
              </Card>
            </Col>

            <Col xs={24} sm={12}>
              <Card>
                <Statistic
                  title="Bình luận trong kỳ"
                  value={statistics?.period_comments || 0}
                  prefix={<MessageOutlined />}
                  valueStyle={{ color: '#eb2f96' }}
                />
              </Card>
            </Col>
          </Row>
        )}
      </Spin>
    </div>
  );
};

export default BlogStatistics;
