import {
  CalendarOutlined,
  DeleteOutlined,
  FileTextOutlined,
  HistoryOutlined,
  MoreOutlined,
  SaveOutlined,
  TagsOutlined,
} from '@ant-design/icons';
import { Affix, Button, Dropdown, Space } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { BackButton } from '../../../components/button';
import { useNavigateTenant } from '../../../hooks';
import { MODULE } from '../config';
import { BlogPostStatus } from '../type';

interface BlogTopBarProps {
  isNew?: boolean;
  onSave: () => void;
  loading?: boolean;
  onSchedule: () => void;
  onUpdateStatus: (status: BlogPostStatus) => void;
  /** <PERSON><PERSON><PERSON> đến c<PERSON>a back button */
  backDestination?: 'dashboard' | 'list' | string;
  /** Ẩn back button khi mode popup */
  hideBackButton?: boolean;
  /** <PERSON><PERSON><PERSON> tra xem có phải mode popup không */
  isPopupMode?: boolean;
}

const BlogTopBar: React.FC<BlogTopBarProps> = ({
  isNew = true,
  onSave,
  loading = false,
  onSchedule,
  onUpdateStatus,
  backDestination = 'list',
  hideBackButton = false,
  isPopupMode = false,
}) => {
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const [isAffix, setIsAffix] = useState(false);

  const handleMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'schedule':
        onSchedule();
        break;
      case 'draft':
        onUpdateStatus(BlogPostStatus.DRAFT);
        break;
      case 'trash':
        onUpdateStatus(BlogPostStatus.ARCHIVED);
        break;
      default:
        break;
    }
  };

  // Define menu items with proper typing for Ant Design Dropdown
  const menuItems = [
    {
      key: 'draft',
      icon: <SaveOutlined />,
      label: 'Lưu dưới dạng bản thảo',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'schedule',
      icon: <CalendarOutlined />,
      label: 'Lên lịch bài đăng',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'note',
      icon: <FileTextOutlined />,
      label: 'Ghi chú',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'history',
      icon: <HistoryOutlined />,
      label: 'Lịch sử bài đăng',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'copy',
      icon: <TagsOutlined />,
      label: 'Nhân đôi bài đăng',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'trash',
      icon: <DeleteOutlined />,
      label: 'Chuyển vào thùng rác',
    },
    // {
    //   type: 'divider' as const,
    // },
    // {
    //   key: 'design',
    //   icon: <EditOutlined />,
    //   label: 'Thiết kế bài đăng',
    // },
    // {
    //   type: 'divider' as const,
    // },
    // {
    //   key: 'help',
    //   icon: <QuestionCircleOutlined />,
    //   label: 'Trung tâm trợ giúp',
    // },
  ];

  return (
    <Affix offsetTop={0} onChange={(affixed) => setIsAffix(!!affixed)}>
      <div
        className={`flex items-center justify-between mb-4 ${isAffix ? 'affix-active' : ''}`}
        style={{
          backgroundColor: 'white',
          padding: '10px 20px',
          boxShadow: isAffix ? '0 2px 8px rgba(0, 0, 0, 0.15)' : 'none',
          transition:
            'box-shadow 0.3s ease-in-out, background-color 0.3s ease-in-out',
          width: '100%',
          zIndex: 100,
          borderBottom: isAffix ? '1px solid #f0f0f0' : 'none',
        }}
      >
        <div className="flex items-center space-x-2">
          {!hideBackButton && (
            <BackButton
              destination={backDestination}
              isPopupMode={isPopupMode}
              hideInPopup={true}
            />
          )}
          <div className="text-xl font-bold">
            {isNew ? t('THÊM BÀI VIẾT') : t('CHỈNH SỬA BÀI VIẾT')}
          </div>
        </div>

        <Space>
          <Button type="primary" loading={loading} onClick={() => onSave()}>
            Lưu
          </Button>
          <Button>Xem trước</Button>
          <Button
            type="primary"
            onClick={() => onUpdateStatus(BlogPostStatus.PUBLISHED)}
          >
            Xuất bản
          </Button>
          <Dropdown
            menu={{ items: menuItems, onClick: handleMenuClick }}
            trigger={['click']}
            placement="bottomRight"
          >
            <Button icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      </div>
    </Affix>
  );
};

export default BlogTopBar;
