import { Checkbox, Col, DatePicker, Form, Input, Select } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import config from '../../../../services/config.service';
import { SelectBlogKind } from '../../../blog-kind/components';
import { SelectBlogType } from '../../../blog-type/components';
import { MODULE } from '../../config';
import { Blog } from '../../type';

const FormItem = Form.Item;

interface InfoPanelProps {
  form: any;
  formValues?: Blog;
}

const InfoPanel: React.FC<InfoPanelProps> = ({ form, formValues }) => {
  const { t } = useTranslation(MODULE);

  return (
    <>
      <Col xs={24} lg={24}>
        <FormItem
          label={t('Tắt "Mục lục"')}
          name="tocHide"
          rules={[{ required: false, message: t('pleaseEnterData') }]}
        >
          <Checkbox />
        </FormItem>
      </Col>
      <Col xs={24} lg={24}>
        <FormItem
          label={t(
            'Tắt "Hiển thị ở danh sách bài trên web(Vẫn hiển thị chi tiết bài)"',
          )}
          name="hideList"
          rules={[{ required: false, message: t('pleaseEnterData') }]}
        >
          <Checkbox />
        </FormItem>
      </Col>
      <Col xs={24} lg={24}>
        <FormItem
          label={t('Mật khẩu bảo vệ')}
          name="password"
          rules={[{ required: false, message: t('pleaseEnterData') }]}
        >
          <Input.Password placeholder={t('Để trống nếu bài viết công khai')} />
        </FormItem>
      </Col>
      <Col xs={24} lg={24}>
        <FormItem
          label={t('Lên lịch xuất bản')}
          name="scheduleAt"
          rules={[{ required: false, message: t('pleaseEnterData') }]}
          tooltip="Thiết lập thời gian tự động xuất bản (Bài viết phải nằm trong mục Chờ xuất bản)"
        >
          <DatePicker
            style={{ width: '100%' }}
            format={config.DATETIME_FORMAT}
            showTime
          />
        </FormItem>
      </Col>
      <Col xs={24} lg={24}>
        <FormItem
          label={t('Ngày xuất bản')}
          name="published_at"
          rules={[{ required: false, message: t('pleaseEnterData') }]}
        >
          <DatePicker
            style={{ width: '100%' }}
            format={config.DATETIME_FORMAT}
            showTime
          />
        </FormItem>
      </Col>
      <Col xs={24} lg={24}>
        <FormItem
          label={t('Loại tin')}
          name="kindId"
          rules={[{ required: false, message: t('pleaseEnterData') }]}
        >
          <SelectBlogKind />
        </FormItem>
      </Col>
      <Col xs={24} lg={24}>
        <FormItem
          label={t('Kiểu tin')}
          name="typeId"
          rules={[{ required: false, message: t('pleaseEnterData') }]}
        >
          <SelectBlogType />
        </FormItem>
      </Col>
    </>
  );
};

export default InfoPanel;
