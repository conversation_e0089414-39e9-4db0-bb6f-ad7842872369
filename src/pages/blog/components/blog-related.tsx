import {
  CalendarOutlined,
  CloseOutlined,
  DownOutlined,
  SearchOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Button,
  Checkbox,
  Col,
  Empty,
  Input,
  InputNumber,
  List,
  Modal,
  Row,
  Select,
  Space,
  Spin,
  Tag,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ConsoleService from '../../../services/console.service';
import { getOptions as getCategoryOptions } from '../../blog-category/api';
import { getItems } from '../api';
import { MODULE } from '../config';
import { useNavigateTenant } from '../../../hooks';
import {
  Blog,
  BlogPostStatus,
  RelatedPost,
  RelatedPostBulkCreate,
} from '../type';
import './blog-related.scss';

const { Text, Title } = Typography;
const { Option } = Select;

interface BlogRelatedProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (selectedBlogs: Blog[]) => void;
  selectedBlogIds?: number[];
  excludeBlogIds?: number[];
  title?: string;
  // Related posts management
  currentPostId?: number;
  mode?: 'select' | 'manage'; // 'select' for choosing posts, 'manage' for managing relationships
  onSaveRelations?: (relations: RelatedPostBulkCreate) => void;
  existingRelations?: RelatedPost[];
}

interface FilterState {
  search: string;
  category: string;
  status: BlogPostStatus[];
  categories: string[];
}

const BlogRelated: React.FC<BlogRelatedProps> = ({
  visible,
  onClose,
  onConfirm,
  selectedBlogIds = [],
  excludeBlogIds = [],
  title = 'Chọn bài đăng',
  currentPostId,
  mode = 'select',
  onSaveRelations,
  existingRelations = [],
}) => {
  const logger = ConsoleService.register(MODULE);
  const initialLoadRef = useRef(false);

  // State management
  const [loading, setLoading] = useState(false);
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [selectedBlogs, setSelectedBlogs] = useState<number[]>(selectedBlogIds);
  const [showFilters, setShowFilters] = useState(false);

  // Related posts management state
  const [blogPriorities, setBlogPriorities] = useState<Record<number, number>>(
    {},
  );
  const [isBidirectional, setIsBidirectional] = useState(true);
  const [saving, setSaving] = useState(false);

  // Filter state
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    category: '',
    status: [],
    categories: [],
  });

  // Pagination state
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // Load categories for filter options
  const loadCategories = useCallback(async () => {
    try {
      const response = await getCategoryOptions();
      if (response.status.success) {
        setCategories(response.data);
      }
    } catch (error) {
      logger('Error loading categories:', error);
    }
  }, [logger]);

  // Load blog posts with filters
  const loadBlogs = useCallback(
    async (page = 1) => {
      try {
        setLoading(true);

        const params: any = {
          page,
          limit: pagination.pageSize,
        };

        // Apply filters
        if (filters.search) {
          params.search = filters.search;
        }

        if (filters.category) {
          params.category_id = filters.category;
        }

        if (filters.status.length > 0) {
          params.status = filters.status;
        }

        // Exclude specific blog IDs
        const allExcludeIds = [...excludeBlogIds];

        // In manage mode, exclude the current post
        if (mode === 'manage' && currentPostId) {
          allExcludeIds.push(currentPostId);
        }

        if (allExcludeIds.length > 0) {
          params.exclude_ids = allExcludeIds;
        }

        const response = await getItems(params);

        if (response.status.success) {
          setBlogs(response.data);
          setPagination((prev) => ({
            ...prev,
            current: page,
            total: response.meta?.total || 0,
          }));
        }
      } catch (error) {
        logger('Error loading blogs:', error);
      } finally {
        setLoading(false);
      }
    },
    [
      filters.search,
      filters.category,
      filters.status,
      pagination.pageSize,
      excludeBlogIds,
      logger,
      mode,
      currentPostId,
    ],
  );

  // Initialize data when modal opens
  useEffect(() => {
    if (visible && !initialLoadRef.current) {
      initialLoadRef.current = true;
      loadCategories();
      loadBlogs(1);
      setSelectedBlogs(selectedBlogIds);

      // Initialize priorities from existing relations
      if (mode === 'manage' && existingRelations.length > 0) {
        const priorities: Record<number, number> = {};
        const existingIds: number[] = [];

        existingRelations.forEach((relation) => {
          if (relation.related_post_id) {
            priorities[relation.related_post_id] = relation.priority;
            existingIds.push(relation.related_post_id);
          }
        });

        setBlogPriorities(priorities);
        setSelectedBlogs((prev) => [...new Set([...prev, ...existingIds])]);
      }
    } else if (!visible) {
      // Reset the ref when modal closes
      initialLoadRef.current = false;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, selectedBlogIds, mode, existingRelations]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters((prev) => ({ ...prev, search: e.target.value }));
  };

  // Handle search submit
  const handleSearch = () => {
    loadBlogs(1);
  };

  // Handle category filter change
  const handleCategoryChange = (value: string) => {
    setFilters((prev) => ({ ...prev, category: value }));
    loadBlogs(1);
  };

  // Handle status filter change
  const handleStatusChange = (status: BlogPostStatus, checked: boolean) => {
    setFilters((prev) => ({
      ...prev,
      status: checked
        ? [...prev.status, status]
        : prev.status.filter((s) => s !== status),
    }));
    loadBlogs(1);
  };

  // Handle category checkbox change
  const handleCategoryCheckboxChange = (
    categoryId: string,
    checked: boolean,
  ) => {
    setFilters((prev) => ({
      ...prev,
      categories: checked
        ? [...prev.categories, categoryId]
        : prev.categories.filter((c) => c !== categoryId),
    }));
    loadBlogs(1);
  };

  // Handle blog selection
  const handleBlogSelect = (blogId: number, checked: boolean) => {
    setSelectedBlogs((prev) =>
      checked ? [...prev, blogId] : prev.filter((id) => id !== blogId),
    );
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allBlogIds = blogs
        .map((blog) => blog.post_id)
        .filter(Boolean) as number[];
      setSelectedBlogs((prev) => [...new Set([...prev, ...allBlogIds])]);
    } else {
      const currentBlogIds = blogs.map((blog) => blog.post_id);
      setSelectedBlogs((prev) =>
        prev.filter((id) => !currentBlogIds.includes(id)),
      );
    }
  };

  // Handle priority change
  const handlePriorityChange = (blogId: number, priority: number) => {
    setBlogPriorities((prev) => ({
      ...prev,
      [blogId]: priority,
    }));
  };

  // Handle confirm for select mode
  const handleConfirm = () => {
    if (mode === 'select') {
      const selectedBlogObjects = blogs.filter((blog) =>
        selectedBlogs.includes(blog.post_id || 0),
      );
      onConfirm(selectedBlogObjects);
      onClose();
    } else if (mode === 'manage' && onSaveRelations && currentPostId) {
      handleSaveRelations();
    }
  };

  // Handle save relations for manage mode
  const handleSaveRelations = async () => {
    if (!currentPostId || !onSaveRelations) return;

    try {
      setSaving(true);

      const relatedPosts = selectedBlogs.map((blogId, index) => ({
        related_post_id: blogId,
        priority: blogPriorities[blogId] || index + 1,
      }));

      const payload: RelatedPostBulkCreate = {
        post_id: currentPostId,
        is_bidirectional: isBidirectional,
        related_posts: relatedPosts,
      };

      onSaveRelations(payload);
      onClose();
    } catch (error) {
      logger('Error saving relations:', error);
    } finally {
      setSaving(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    setSelectedBlogs(selectedBlogIds);
    onClose();
  };

  // Clear filters
  const handleClearFilters = () => {
    setFilters({
      search: '',
      category: '',
      status: [],
      categories: [],
    });
    loadBlogs(1);
  };

  // Get status display info
  const getStatusInfo = (status: string) => {
    switch (status) {
      case BlogPostStatus.PUBLISHED:
        return { color: 'green', text: 'Đã xuất bản' };
      case BlogPostStatus.SCHEDULED:
        return { color: 'blue', text: 'Đã lên lịch' };
      case BlogPostStatus.DRAFT:
        return { color: 'orange', text: 'Bản thảo' };
      case BlogPostStatus.REVIEW:
        return { color: 'gold', text: 'Chờ duyệt' };
      default:
        return { color: 'default', text: status };
    }
  };

  // Check if all current page blogs are selected
  const isAllSelected =
    blogs.length > 0 &&
    blogs.every((blog) => selectedBlogs.includes(blog.post_id || 0));

  // Check if some blogs are selected
  const isIndeterminate =
    blogs.some((blog) => selectedBlogs.includes(blog.post_id || 0)) &&
    !isAllSelected;

  return (
    <Modal
      title={
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Title level={4} style={{ margin: 0 }}>
            {title}
          </Title>
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={onClose}
            style={{ border: 'none' }}
          />
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      closable={false}
      className="blog-related-modal"
      styles={{
        body: { padding: '20px' },
      }}
    >
      <div style={{ marginBottom: '16px' }}>
        {/* Search and Filter Section */}
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Input
              placeholder="Tìm kiếm..."
              prefix={<SearchOutlined />}
              value={filters.search}
              onChange={handleSearchChange}
              onPressEnter={handleSearch}
              allowClear
            />
          </Col>
          <Col span={12}>
            <Select
              placeholder="Lọc theo:"
              value={filters.category || undefined}
              onChange={handleCategoryChange}
              style={{ width: '100%' }}
              allowClear
              suffixIcon={<DownOutlined />}
            >
              <Option value="">Tất cả bài</Option>
              {categories.map((category) => (
                <Option key={category.value} value={category.value}>
                  {category.label}
                </Option>
              ))}
            </Select>
          </Col>
        </Row>

        {/* Advanced Filters Toggle */}
        <div style={{ marginTop: '12px' }}>
          <Button
            type="link"
            onClick={() => setShowFilters(!showFilters)}
            style={{ padding: 0 }}
          >
            {showFilters ? 'Ẩn bộ lọc' : 'Hiện bộ lọc'} <DownOutlined />
          </Button>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div
            style={{
              marginTop: '16px',
              padding: '16px',
              background: '#f5f5f5',
              borderRadius: '6px',
            }}
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Text strong style={{ display: 'block', marginBottom: '8px' }}>
                  Trạng thái
                </Text>
                <Space direction="vertical">
                  <Checkbox
                    checked={filters.status.includes(BlogPostStatus.PUBLISHED)}
                    onChange={(e) =>
                      handleStatusChange(
                        BlogPostStatus.PUBLISHED,
                        e.target.checked,
                      )
                    }
                  >
                    Đã xuất bản
                  </Checkbox>
                  <Checkbox
                    checked={filters.status.includes(BlogPostStatus.SCHEDULED)}
                    onChange={(e) =>
                      handleStatusChange(
                        BlogPostStatus.SCHEDULED,
                        e.target.checked,
                      )
                    }
                  >
                    Đã lên lịch
                  </Checkbox>
                  <Checkbox
                    checked={filters.status.includes(BlogPostStatus.DRAFT)}
                    onChange={(e) =>
                      handleStatusChange(BlogPostStatus.DRAFT, e.target.checked)
                    }
                  >
                    Bản thảo
                  </Checkbox>
                </Space>
              </Col>
              <Col span={12}>
                <Text strong style={{ display: 'block', marginBottom: '8px' }}>
                  Danh mục
                </Text>
                <Space direction="vertical">
                  {categories.slice(0, 3).map((category) => (
                    <Checkbox
                      key={category.value}
                      checked={filters.categories.includes(category.value)}
                      onChange={(e) =>
                        handleCategoryCheckboxChange(
                          category.value,
                          e.target.checked,
                        )
                      }
                    >
                      {category.label}
                    </Checkbox>
                  ))}
                </Space>
              </Col>
            </Row>
            <div style={{ marginTop: '12px', textAlign: 'right' }}>
              <Button size="small" onClick={handleClearFilters}>
                Xóa bộ lọc
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Blog List */}
      <div className="blog-related-list">
        {/* Select All Header */}
        <div className="blog-related-header">
          <Checkbox
            checked={isAllSelected}
            indeterminate={isIndeterminate}
            onChange={(e) => handleSelectAll(e.target.checked)}
          >
            Đã chọn {selectedBlogs.length} trong {blogs.length} bài đăng
          </Checkbox>
        </div>

        {/* Blog List */}
        <Spin spinning={loading}>
          {blogs.length === 0 ? (
            <div className="empty-state">
              <Empty description="Không tìm thấy bài đăng nào" />
            </div>
          ) : (
            <List
              dataSource={blogs}
              renderItem={(blog) => {
                const blogId = blog.post_id || 0;
                const isSelected = selectedBlogs.includes(blogId);
                const statusInfo = getStatusInfo(blog.status || '');

                return (
                  <List.Item
                    className={`blog-list-item ${isSelected ? 'selected' : ''}`}
                    onClick={() => handleBlogSelect(blogId, !isSelected)}
                  >
                    <div className="blog-item-content">
                      <Checkbox
                        className="blog-item-checkbox"
                        checked={isSelected}
                        onChange={(e) => {
                          e.stopPropagation();
                          handleBlogSelect(blogId, e.target.checked);
                        }}
                      />

                      <Avatar
                        className="blog-item-avatar"
                        src={blog.image}
                        size={64}
                        shape="square"
                      />

                      <div className="blog-item-info">
                        <div className="blog-item-title">
                          {blog.title || blog.name}
                        </div>

                        <div className="blog-item-meta">
                          <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
                          <span className="meta-item">
                            <CalendarOutlined />
                            {blog.published_at
                              ? dayjs(blog.published_at).format('DD/MM/YYYY')
                              : dayjs(blog.created_at).format('DD/MM/YYYY')}
                          </span>
                          {blog.author && (
                            <span className="meta-item">
                              <UserOutlined />
                              {blog.author.first_name || blog.author.username}
                            </span>
                          )}
                        </div>

                        {mode === 'manage' && isSelected && (
                          <div style={{ marginTop: '8px' }}>
                            <Text
                              style={{ fontSize: '12px', marginRight: '8px' }}
                            >
                              Độ ưu tiên:
                            </Text>
                            <InputNumber
                              size="small"
                              min={1}
                              max={100}
                              value={blogPriorities[blogId] || 1}
                              onChange={(value) =>
                                handlePriorityChange(blogId, value || 1)
                              }
                              style={{ width: '60px' }}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </List.Item>
                );
              }}
            />
          )}
        </Spin>
      </div>

      {/* Footer */}
      <div className="blog-related-footer">
        <div>
          <Text type="secondary" className="selected-count">
            Đã chọn {selectedBlogs.length} trong {blogs.length} bài đăng
          </Text>

          {mode === 'manage' && (
            <div style={{ marginTop: '8px' }}>
              <Checkbox
                checked={isBidirectional}
                onChange={(e) => setIsBidirectional(e.target.checked)}
              >
                Tạo liên kết hai chiều
              </Checkbox>
            </div>
          )}
        </div>

        <Space className="footer-actions">
          <Button onClick={handleCancel}>Hủy</Button>
          <Button
            type="primary"
            onClick={handleConfirm}
            loading={mode === 'manage' ? saving : false}
          >
            {mode === 'manage' ? 'Lưu liên kết' : 'Xong'}
          </Button>
        </Space>
      </div>
    </Modal>
  );
};

export default BlogRelated;
