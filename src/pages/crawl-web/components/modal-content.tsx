import { ReloadOutlined } from '@ant-design/icons';
import { Affix, Button, Modal } from 'antd';
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import { CrawlContent } from './crawl-content';

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  showModal: boolean;
  crawlContent: any;
}

export const ModalContent: React.FC<IndexFormProps> = (props) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const { showModal, onChange, crawlContent } = props;
  const formRef = useRef<any>(null);

  const handleModal = () => {
    onChange(false);
  };

  function handleActions(action: string): void {
    logger('[handleActions]', action);
    if (action === 'save') {
      formRef.current?.submitForm();
    } else if (action === 'cancel') {
      handleModal();
    }
  }

  return (
    <Modal
      title={t('module')}
      open={showModal}
      onCancel={handleModal}
      footer={false}
      className="form_modal"
      width={'100%'}
    >
      <div className="max-w-[765px] mx-auto">
        {crawlContent && <CrawlContent item={crawlContent}></CrawlContent>}
      </div>
      <Affix offsetBottom={0}>
        <div className="form_footer flex gap-4">
          <Button
            type="dashed"
            icon={<ReloadOutlined />}
            onClick={() => handleActions('cancel')}
          >
            {t('form.btnBack')}
          </Button>
        </div>
      </Affix>
    </Modal>
  );
};
