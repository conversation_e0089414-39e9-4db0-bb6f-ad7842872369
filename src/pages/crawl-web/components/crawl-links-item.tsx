import { CrawlLink } from '../type';

interface Props {
  item: CrawlLink;
}
export const CrawlLinksItem: React.FC<Props> = ({ item }) => {
  return (
    <div className="flex justify-start mb-4">
      <div className="max-w-[100px] mr-3">
        <img
          src={
            item.img ??
            'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSrJgwdOAjqaZGS7kn35IVm_ZN6E4XFuJ7V_g&s'
          }
          alt="img"
        />
      </div>
      <div className="max-w-[300px]">
        <a
          className="text-blue-600 cursor-pointer"
          href={item.url}
          target="_blank"
          rel="noreferrer"
        >
          {item.title}
        </a>
      </div>
    </div>
  );
};
