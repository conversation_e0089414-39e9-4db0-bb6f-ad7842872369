import { Col, Form, Input, message, Row } from 'antd';
import _ from 'lodash';
import { forwardRef, useImperativeHandle } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { updateConfig } from '../api';
import { MODULE } from '../config';
import { CrawlWeb } from '../type';

const FormItem = Form.Item;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  config: string;
}

const ModelConfigForm = forwardRef<unknown, IndexFormProps>(
  ({ onChange, config }, ref) => {
    const { t } = useTranslation(MODULE);
    const logger = ConsoleService.register(MODULE);
    const [form] = Form.useForm();

    const initForm = {
      config: config,
    };

    const onFinish = async (values: CrawlWeb) => {
      try {
        const res = await updateConfig(values);
        if (res.status.success) {
          message.success(t('form.addSuccess'));
        }
        if (!res.status.success) {
          message.error(res.status.message);
        }
      } catch (error) {
        logger('Error submitting form', error);
        message.error(
          _.get(error, 'response.data.message.0') || t('form.submitError'),
        );
      }
    };

    const handleValuesChange = (newValue: any, allValues: any) => {
      logger(newValue);
      logger(allValues);
    };

    useImperativeHandle(ref, () => ({
      submitForm: () => form.submit(),
    }));

    return (
      <Form
        form={form}
        name="form"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        initialValues={initForm}
        onValuesChange={handleValuesChange}
      >
        <div className="form_content">
          <Row>
            <Col xs={24} lg={24}>
              <Row gutter={16}>
                <Col xs={24} lg={24}>
                  <FormItem
                    label={t('formConfig.config')}
                    name="config"
                    rules={[
                      { required: true, message: t('form.pleaseEnterData') },
                    ]}
                  >
                    <Input.TextArea rows={40} />
                  </FormItem>
                </Col>
              </Row>
            </Col>
          </Row>
        </div>
      </Form>
    );
  },
);
ModelConfigForm.displayName = 'ModelConfigForm';

export { ModelConfigForm };
