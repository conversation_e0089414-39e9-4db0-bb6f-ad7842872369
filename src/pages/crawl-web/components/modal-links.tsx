import { LeftOutlined } from '@ant-design/icons';
import { <PERSON>ton, Modal } from 'antd';
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import { CrawlLink } from '../type';
import { CrawlLinksItem } from './crawl-links-item';

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  showModal: boolean;
  crawlLinks: any;
}

export const ModalLinks: React.FC<IndexFormProps> = (props) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const { showModal, onChange, crawlLinks } = props;
  const formRef = useRef<any>(null);

  const handleModal = () => {
    onChange(false);
  };

  function handleActions(action: string): void {
    logger('[handleActions]', action);
    if (action === 'save') {
      formRef.current?.submitForm();
    } else if (action === 'cancel') {
      handleModal();
    }
  }

  return (
    <Modal
      title={t('module')}
      open={showModal}
      onCancel={handleModal}
      footer={false}
      className="form_modal"
      width={'100%'}
    >
      <div className="p-4 grid grid-cols-3 gap-4">
        {crawlLinks &&
          crawlLinks.map((item: CrawlLink) => (
            <div key={item.url}>
              <CrawlLinksItem item={item} />
            </div>
          ))}
      </div>

      <div className="form_footer flex gap-4">
        <Button
          type="dashed"
          icon={<LeftOutlined />}
          onClick={() => handleActions('cancel')}
        >
          {t('form.btnBack')}
        </Button>
      </div>
    </Modal>
  );
};
