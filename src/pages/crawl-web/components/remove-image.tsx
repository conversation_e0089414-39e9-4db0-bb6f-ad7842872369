import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Form, Input, Space } from 'antd';
import { forwardRef, useEffect, useImperativeHandle } from 'react';

interface RemoveImageProps {
  replaceImage?: { [key: string]: string };
}

const RemoveImage = forwardRef((props: RemoveImageProps, ref) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (props.replaceImage) {
      const initialValues = Object.entries(props.replaceImage).map(
        ([key, value]) => ({ key, value }),
      );
      form.setFieldsValue({ removeImage: initialValues });
    }
  }, [props.replaceImage, form]);

  useImperativeHandle(ref, () => ({
    validateFields: () => {
      return form.validateFields();
    },
    getValues: () => {
      return form.getFieldsValue();
    },
  }));

  return (
    <Form
      form={form}
      name="dynamic_form_nest_item"
      style={{ maxWidth: 600 }}
      autoComplete="off"
    >
      <Form.List name="removeImage">
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <Space
                key={key}
                style={{ display: 'flex', marginBottom: 8 }}
                align="baseline"
              >
                <Form.Item
                  {...restField}
                  name={[name, 'key']}
                  rules={[{ required: true, message: 'Missing data' }]}
                >
                  <Input placeholder="Key" />
                </Form.Item>
                <Form.Item
                  {...restField}
                  name={[name, 'value']}
                  rules={[{ required: false, message: 'Missing data' }]}
                >
                  <Input placeholder="Value" />
                </Form.Item>
                <MinusCircleOutlined onClick={() => remove(name)} />
              </Space>
            ))}
            <Form.Item>
              <Button
                type="dashed"
                onClick={() => add()}
                block
                icon={<PlusOutlined />}
              >
                Add row
              </Button>
            </Form.Item>
          </>
        )}
      </Form.List>
    </Form>
  );
});

RemoveImage.displayName = 'RemoveImage';

export default RemoveImage;
