import { Divider } from 'antd';
import { CrawlBlog } from '../type';

interface Props {
  item: CrawlBlog;
}
export const CrawlContent: React.FC<Props> = ({ item }) => {
  console.log(item);
  return (
    <div className="mb-4">
      {/* <div className="">
        <img
          src={
            item.images && item.images.length > 0
              ? item.images[0]
              : 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSrJgwdOAjqaZGS7kn35IVm_ZN6E4XFuJ7V_g&s'
          }
          alt="img"
          className="w-full h-auto"
        />
      </div> */}
      <div>
        <Divider>Tiêu đề bài viết</Divider>
        <div className="font-[2em]">{item.title}</div>
        <Divider>Mô tả bài viết</Divider>
        <div className="text-gray-600">{item.desc}</div>

        <Divider>Nội dung bài viết</Divider>
        <div>
          <div
            className="text-gray-600"
            dangerouslySetInnerHTML={{ __html: item.content }}
          ></div>
        </div>

        {/* <p className="text-sm text-gray-500">Tác giả: {item.author}</p>
        <p className="text-sm text-gray-500">Nguồn: {item.source}</p> */}
      </div>
    </div>
  );
};
