import { t } from 'i18next';

export interface CrawlWeb {
  _id: string;
  list: string;
  title: string;
  url: string;
  image: string;
  description: string;
  content: string;
  domain: string;
  titleDetail: string;
  removeElement: string;
  replaceText: string;
  removeContentElement: string;
  replaceImage: string;
  urlListTest: string;
  urlDetailTest: string;
  status: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export enum CrawlWebStatus {
  PENDING = 'PENDING',
  PUBLISHED = 'PUBLISHED',
  DISABLED = 'DISABLED',
}

export function getStatusLabel(status: CrawlWebStatus): string {
  const statusMap = {
    PENDING: t('status.PENDING'),
    PUBLISHED: t('status.PUBLISHED'),
    DISABLED: t('status.DISABLED'),
  };
  return statusMap[status] || '';
}

export interface CrawlLink {
  title: string;
  url: string;
  img: string;
}

export interface CrawlBlog {
  _id: string;
  title: string;
  desc: string;
  images: string[];
  images_cloud: string[];
  content: string;
  author: string;
  source: string;
  url: string;
}
