import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MODULE } from './config';
import { CrawlBlog, CrawlLink, CrawlWeb } from './type';

const url = `/api/cms/v1/${MODULE}`;

export async function getItems(
  params: any,
): Promise<ApiResponsePaginationCursor<CrawlWeb[]>> {
  const response = await apiService.get<
    ApiResponsePaginationCursor<CrawlWeb[]>
  >(url, { params });
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<CrawlWeb>> {
  const response = await apiService.get<ApiResponse<CrawlWeb>>(`${url}/${id}`);
  return response.data;
}

export async function createItem(payload: any): Promise<ApiResponse<CrawlWeb>> {
  const response = await apiService.post<ApiResponse<CrawlWeb>>(url, payload);
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<CrawlWeb>> {
  const response = await apiService.put<ApiResponse<CrawlWeb>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(id: string): Promise<ApiResponse<CrawlWeb>> {
  const response = await apiService.delete<ApiResponse<CrawlWeb>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getSearch(params: any): Promise<ApiResponse<CrawlWeb[]>> {
  const response = await apiService.get<ApiResponse<CrawlWeb[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<CrawlWeb[]>> {
  const response = await apiService.get<ApiResponse<CrawlWeb[]>>(`${url}/all`);
  return response.data;
}

export async function getLinks(payload: any): Promise<ApiResponse<CrawlLink>> {
  const response = await apiService.post<ApiResponse<CrawlLink>>(
    `${url}/get-links`,
    payload,
  );
  return response.data;
}

export async function getContent(
  payload: any,
): Promise<ApiResponse<CrawlBlog>> {
  const response = await apiService.post<ApiResponse<CrawlBlog>>(
    `${url}/get-content`,
    payload,
  );
  return response.data;
}

export async function updateConfig(
  payload: any,
): Promise<ApiResponse<CrawlWeb>> {
  const response = await apiService.post<ApiResponse<CrawlWeb>>(
    `${url}/config`,
    payload,
  );
  return response.data;
}

export async function getHtml(payload: {
  url: string;
  crawlWebId: string;
}): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${url}/get-html`,
    payload,
  );
  return response.data;
}
