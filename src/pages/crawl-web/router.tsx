import React from 'react';
import { Route, Routes } from 'react-router-dom';
import { CrawlWebList } from './list';
import { CrawlWebPage } from './form';

const CrawlWebRouter: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<CrawlWebList />} />
      <Route path="/create" element={<CrawlWebPage />} />
      <Route path="/:id" element={<CrawlWebPage />} />
    </Routes>
  );
};

export { CrawlWebRouter };
