import { Button, Col, Divider, Form, Input, message, Row, Select } from 'antd';
import _ from 'lodash';
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { createItem, getContent, getItem, getLinks, updateItem } from '../api';
import '../assets/styles.scss';
import { ModalCrawl } from '../components';
import { ConfigModal } from '../components/modal-config';
import { ModalContent } from '../components/modal-content';
import { ModalLinks } from '../components/modal-links';
import RemoveImage from '../components/remove-image';
import { MODULE } from '../config';
import { CrawlBlog, CrawlLink, CrawlWeb, CrawlWebStatus } from '../type';
const FormItem = Form.Item;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const CrawlWebForm = forwardRef<unknown, IndexFormProps>(
  ({ onChange, id }, ref) => {
    const { t } = useTranslation(MODULE);
    const logger = ConsoleService.register(MODULE);
    const [form] = Form.useForm();
    const [isNew, setIsNew] = useState<boolean>(false);
    const [item, setItem] = useState<CrawlWeb>();
    const [formValues, setFormValues] = useState<CrawlWeb>();
    const [crawlLinks, setCrawlLinks] = useState<CrawlLink>();
    const [crawlContent, setCrawlContent] = useState<CrawlBlog>();
    const [showModalConfig, setShowModalConfig] = useState(false);
    const [showModalCrawl, setShowModalCrawl] = useState(false);
    const [showModalLinks, setShowModalLinks] = useState(false);
    const [showModalContent, setShowModalContent] = useState(false);

    const removeImageRef = useRef(null);

    const initForm = {};

    const getItemData = async (_id: string) => {
      const res = await getItem(_id);
      if (res.status.success) {
        setItem(res.data);
        form.setFieldsValue(res.data);
      } else {
        message.error(res.status.message);
      }
    };

    useEffect(() => {
      logger(id);
      form.resetFields();
      if (['create', undefined].includes(id)) {
        setIsNew(true);
      } else if (id) {
        setIsNew(false);
        getItemData(id);
      }
    }, [id]);

    const onFinish = async (values: CrawlWeb) => {
      try {
        if (removeImageRef.current) {
          try {
            const removeImageValues =
              await removeImageRef.current.validateFields();
            if (removeImageValues) {
              const replaceImage = removeImageValues.removeImage.reduce(
                (acc, item) => {
                  acc[item.key] = item.value === undefined ? '' : item.value;
                  return acc;
                },
                {},
              );
              console.log('replaceImage :', replaceImage);
              values.replaceImage = replaceImage;
            }
          } catch (errorInfo) {
            message.error(t('form.pleaseEnterData'));
            console.log('Validation failed:', errorInfo);
            return;
          }
        }
        let res;
        if (isNew) {
          res = await createItem(values);
          if (res.status.success) {
            message.success(t('form.addSuccess'));
            getItemData(id);
          }
        } else {
          res = await updateItem(id!, values);
          if (res.status.success) {
            message.success(t('form.updateSuccess'));
            getItemData(id);
          }
        }
        if (!res.status.success) {
          message.error(res.status.message);
        } else {
          setItem(res.data);
          //form.resetFields();
          //onChange(true);
        }
      } catch (error) {
        logger('Error submitting form', error);
        message.error(
          _.get(error, 'response.data.message.0') || t('form.submitError'),
        );
      }
    };

    const handleValuesChange = (newValue: any, allValues: any) => {
      logger(newValue);
      logger(allValues);
      setFormValues(allValues);
    };

    useImperativeHandle(ref, () => ({
      submitForm: () => form.submit(),
      showModalConfig: () => setShowModalConfig(true),
      showModalCrawl: () => {
        setShowModalCrawl(true);
      },
    }));

    const handleGetLinks = async () => {
      setCrawlLinks([]);
      const url = form.getFieldValue('urlListTest');
      const res = await getLinks({ url, crawlWebId: id });
      console.log(res);
      if (res.status.success) {
        setCrawlLinks(res.data);
        setShowModalLinks(true);
      }
    };

    const handleGetContent = async () => {
      const url = form.getFieldValue('urlDetailTest');
      const res = await getContent({ url, crawlWebId: id });
      console.log(res);

      if (res.status.success) {
        console.log(res.data);
        setCrawlContent(res.data);
        setShowModalContent(true);
      }
    };

    return (
      <>
        <Form
          form={form}
          name="form"
          layout="vertical"
          onFinish={onFinish}
          autoComplete="off"
          initialValues={initForm}
          onValuesChange={handleValuesChange}
        >
          <div className="form_content">
            <Row>
              <Col xs={24} lg={24}>
                <Row gutter={16}>
                  <Col xs={24} lg={12}>
                    <FormItem
                      label={t('form.domain')}
                      name="domain"
                      rules={[
                        { required: true, message: t('form.pleaseEnterData') },
                      ]}
                    >
                      <Input />
                    </FormItem>
                  </Col>
                  <Col xs={24} lg={12}>
                    <FormItem
                      label={t('form.status')}
                      name="status"
                      rules={[
                        { required: true, message: t('form.pleaseEnterData') },
                      ]}
                    >
                      <Select
                        options={Object.entries(CrawlWebStatus).map(
                          ([value, label]) => ({
                            value,
                            label: t(`status.${label}`),
                          }),
                        )}
                      />
                    </FormItem>
                  </Col>

                  <Divider />
                  <Col xs={24} lg={12}>
                    <FormItem
                      label={t('form.list')}
                      name="list"
                      rules={[
                        { required: false, message: t('form.pleaseEnterData') },
                      ]}
                    >
                      <Input />
                    </FormItem>
                  </Col>
                  <Col xs={24} lg={12}>
                    <FormItem
                      label={t('form.title')}
                      name="title"
                      rules={[
                        { required: false, message: t('form.pleaseEnterData') },
                      ]}
                    >
                      <Input />
                    </FormItem>
                  </Col>
                  <Col xs={24} lg={12}>
                    <FormItem
                      label={t('form.url')}
                      name="url"
                      rules={[
                        { required: false, message: t('form.pleaseEnterData') },
                      ]}
                    >
                      <Input />
                    </FormItem>
                  </Col>
                  <Col xs={24} lg={12}>
                    <FormItem
                      label={t('form.image')}
                      name="image"
                      rules={[
                        { required: false, message: t('form.pleaseEnterData') },
                      ]}
                    >
                      <Input />
                    </FormItem>
                  </Col>
                  <Divider />
                  <Col xs={24} lg={12}>
                    <FormItem
                      label={t('form.title_detail')}
                      name="titleDetail"
                      rules={[
                        { required: false, message: t('form.pleaseEnterData') },
                      ]}
                    >
                      <Input />
                    </FormItem>
                  </Col>
                  <Col xs={24} lg={12}>
                    <FormItem
                      label={t('form.description')}
                      name="description"
                      rules={[
                        { required: false, message: t('form.pleaseEnterData') },
                      ]}
                    >
                      <Input />
                    </FormItem>
                  </Col>
                  <Col xs={24} lg={12}>
                    <FormItem
                      label={t('form.content')}
                      name="content"
                      rules={[
                        { required: false, message: t('form.pleaseEnterData') },
                      ]}
                    >
                      <Input />
                    </FormItem>
                  </Col>

                  <Divider />
                  <Col xs={24} lg={12}>
                    <FormItem
                      label={t('form.removeElement')}
                      name="removeElement"
                      rules={[
                        { required: false, message: t('form.pleaseEnterData') },
                      ]}
                    >
                      <Select
                        mode="tags"
                        showSearch={false}
                        open={false}
                        style={{ width: '100%' }}
                        placeholder={t('form.pleaseEnterData')}
                      ></Select>
                    </FormItem>
                  </Col>

                  <Col xs={24} lg={12}>
                    <FormItem
                      label={t('form.replaceText')}
                      name="replaceText"
                      rules={[
                        { required: false, message: t('form.pleaseEnterData') },
                      ]}
                    >
                      <Select
                        mode="tags"
                        showSearch={false}
                        open={false}
                        style={{ width: '100%' }}
                        placeholder={t('form.pleaseEnterData')}
                      ></Select>
                    </FormItem>
                  </Col>

                  <Col xs={24} lg={12}>
                    <FormItem
                      label={t('form.removeContentElement')}
                      name="removeContentElement"
                      rules={[
                        { required: false, message: t('form.pleaseEnterData') },
                      ]}
                    >
                      <Select
                        mode="tags"
                        showSearch={false}
                        open={false}
                        style={{ width: '100%' }}
                        placeholder={t('form.pleaseEnterData')}
                      ></Select>
                    </FormItem>
                  </Col>

                  <Col xs={24} lg={12}>
                    <FormItem
                      label={t('form.removeImage')}
                      name="removeContentElement"
                    >
                      <RemoveImage
                        ref={removeImageRef}
                        replaceImage={item?.replaceImage}
                      ></RemoveImage>
                    </FormItem>
                  </Col>

                  <Divider />
                  <Col xs={24} lg={12}>
                    <FormItem
                      label={t('form.urlListTest')}
                      name="urlListTest"
                      rules={[
                        { required: false, message: t('form.pleaseEnterData') },
                      ]}
                    >
                      <Input
                        addonAfter={
                          <Button type="primary" onClick={handleGetLinks}>
                            {t('form.btnGetLinks')}
                          </Button>
                        }
                      />
                    </FormItem>
                  </Col>
                  <Col xs={24} lg={12}>
                    <FormItem
                      label={t('form.urlDetailTest')}
                      name="urlDetailTest"
                      rules={[
                        { required: false, message: t('form.pleaseEnterData') },
                      ]}
                    >
                      <Input
                        addonAfter={
                          <Button type="primary" onClick={handleGetContent}>
                            {t('form.btnGetContent')}
                          </Button>
                        }
                      />
                    </FormItem>
                  </Col>
                </Row>
              </Col>
            </Row>
          </div>
        </Form>
        {showModalConfig && (
          <ConfigModal
            showModal={showModalConfig}
            onChange={() => {
              {
                setShowModalConfig(false);
              }
            }}
            config={JSON.stringify(item, null, 4)}
          ></ConfigModal>
        )}
        {showModalCrawl && item && (
          <ModalCrawl
            showModal={showModalCrawl}
            onChange={() => {
              {
                setShowModalCrawl(false);
              }
            }}
            config={item}
          ></ModalCrawl>
        )}
        {showModalLinks && (
          <ModalLinks
            showModal={showModalLinks}
            onChange={() => {
              {
                setShowModalLinks(false);
              }
            }}
            crawlLinks={crawlLinks}
          ></ModalLinks>
        )}
        {showModalContent && (
          <ModalContent
            showModal={showModalContent}
            onChange={() => {
              {
                setShowModalContent(false);
              }
            }}
            crawlContent={crawlContent}
          ></ModalContent>
        )}
      </>
    );
  },
);
CrawlWebForm.displayName = 'CrawlWebForm';

export { CrawlWebForm };
