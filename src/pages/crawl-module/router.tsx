import { Navigate, Route, Routes } from 'react-router-dom';
import { CrawlBlogRouter } from '../crawl-blog';
import { CrawlLogRouter } from '../crawl-log';
import { CrawlUserRouter } from '../crawl-user';
import { CrawlWebRouter } from '../crawl-web';
import { Layout } from './layout';
import { CrawlDashboardRouter } from '../crawl-dashboard';

export const CrawlModuleRouter = () => (
  <Routes>
    <Route element={<Layout />}>
      <Route
        path="/"
        element={<Navigate to="/crawl/crawl-dashboard" replace />}
      />
      <Route path="/crawl-dashboard/*" element={<CrawlDashboardRouter />} />
      <Route path="/crawl-user/*" element={<CrawlUserRouter />} />
      <Route path="/crawl-web/*" element={<CrawlWebRouter />} />
      <Route path="/crawl-blog/*" element={<CrawlBlogRouter />} />
      <Route path="/crawl-log/*" element={<CrawlLogRouter />} />
    </Route>
  </Routes>
);
