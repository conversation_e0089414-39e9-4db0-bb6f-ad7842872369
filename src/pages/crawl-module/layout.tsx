import {
  UserOutlined,
  GlobalOutlined,
  ReadOutlined,
  FileSearchOutlined,
  DashboardOutlined,
} from '@ant-design/icons';
import { Menu } from 'antd';
import { NavLink, Outlet, useLocation } from 'react-router-dom';

export function Layout() {
  const location = useLocation();
  const selectedKey = location.pathname.split('/').slice(0, 3).join('/');

  return (
    <div className="flex">
      <div
        style={{
          width: '250px',
          position: 'sticky',
          top: '0',
          height: '100vh',
          overflowY: 'auto',
        }}
      >
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[selectedKey]}
          items={[
            {
              key: '/crawl/crawl-dashboard',
              label: (
                <NavLink
                  to="/crawl/crawl-dashboard"
                  className="flex justify-between items-center"
                >
                  <span className="flex items-center">
                    <DashboardOutlined className="mr-2" />
                    Dashboard
                  </span>
                </NavLink>
              ),
            },
            {
              key: '/crawl/crawl-user',
              label: (
                <NavLink
                  to="/crawl/crawl-user"
                  className="flex justify-between items-center"
                >
                  <span className="flex items-center">
                    <UserOutlined className="mr-2" />
                    User
                  </span>
                </NavLink>
              ),
            },
            {
              key: '/crawl/crawl-web',
              label: (
                <NavLink
                  to="/crawl/crawl-web"
                  className="flex justify-between items-center"
                >
                  <span className="flex items-center">
                    <GlobalOutlined className="mr-2" />
                    Web
                  </span>
                </NavLink>
              ),
            },
            {
              key: '/crawl/crawl-blog',
              label: (
                <NavLink
                  to="/crawl/crawl-blog"
                  className="flex justify-between items-center"
                >
                  <span className="flex items-center">
                    <ReadOutlined className="mr-2" />
                    Blog
                  </span>
                </NavLink>
              ),
            },
            {
              key: '/crawl/crawl-log',
              label: (
                <NavLink
                  to="/crawl/crawl-log"
                  className="flex justify-between items-center"
                >
                  <span className="flex items-center">
                    <FileSearchOutlined className="mr-2" />
                    Log
                  </span>
                </NavLink>
              ),
            },
          ]}
          style={{ height: '100%' }}
        />
      </div>

      <div style={{ flex: 1, paddingLeft: '14px' }}>
        <Outlet />
      </div>
    </div>
  );
}
