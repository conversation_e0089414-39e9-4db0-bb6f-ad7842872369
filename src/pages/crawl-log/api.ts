import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MODULE } from './config';
import { CrawlLog } from './type';

const url = `/api/cms/v1/${MODULE}`;

export async function getItems(
  params: any,
): Promise<ApiResponsePaginationCursor<CrawlUser[]>> {
  const response = await apiService.get<
    ApiResponsePaginationCursor<CrawlUser[]>
  >(url, { params });
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<CrawlLog>> {
  const response = await apiService.get<ApiResponse<CrawlLog>>(`${url}/${id}`);
  return response.data;
}

export async function createItem(payload: any): Promise<ApiResponse<CrawlLog>> {
  const response = await apiService.post<ApiResponse<CrawlLog>>(url, payload);
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<CrawlLog>> {
  const response = await apiService.put<ApiResponse<CrawlLog>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(id: string): Promise<ApiResponse<CrawlLog>> {
  const response = await apiService.delete<ApiResponse<CrawlLog>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getSearch(params: any): Promise<ApiResponse<CrawlLog[]>> {
  const response = await apiService.get<ApiResponse<CrawlLog[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<CrawlLog[]>> {
  const response = await apiService.get<ApiResponse<CrawlLog[]>>(`${url}/all`);
  return response.data;
}
