import {
  ApiResponse,
  ApiResponsePagination,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MODULE } from './config';
import { BlogType } from './type';

const url = `/api/cms/v1/${MODULE}`;

export async function getItems(
  params: any,
): Promise<ApiResponsePagination<BlogType[]>> {
  const response = await apiService.get<ApiResponsePagination<BlogType[]>>(
    url,
    { params },
  );
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<BlogType>> {
  const response = await apiService.get<ApiResponse<BlogType>>(`${url}/${id}`);
  return response.data;
}

export async function createItem(payload: any): Promise<ApiResponse<BlogType>> {
  const response = await apiService.post<ApiResponse<BlogType>>(url, payload);
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<BlogType>> {
  const response = await apiService.put<ApiResponse<BlogType>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(id: string): Promise<ApiResponse<BlogType>> {
  const response = await apiService.delete<ApiResponse<BlogType>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getSearch(params: any): Promise<ApiResponse<BlogType[]>> {
  const response = await apiService.get<ApiResponse<BlogType[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<BlogType[]>> {
  const response = await apiService.get<ApiResponse<BlogType[]>>(`${url}/all`);
  return response.data;
}
