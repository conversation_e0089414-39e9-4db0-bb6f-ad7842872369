export const MODULE = 'notification';
export const MODULE_POPUP = true;

export const API_ENDPOINTS = {
  NOTIFICATIONS: '/api/cms/v1/notifications',
  TEMPLATES: '/api/cms/v1/templates',
  STATISTICS: '/api/cms/v1/notifications/statistics',
  UNREAD_COUNT: '/api/cms/v1/notifications/me/unread-count',
  MARK_ALL_READ: '/api/cms/v1/notifications/mark-all-read',
  SETTINGS: '/api/cms/v1/notifications/settings',
  PREVIEW: '/api/cms/v1/notifications/preview',
  TEST: '/api/cms/v1/notifications/test',
  BULK: '/api/cms/v1/notifications/bulk',
} as const;

export const NOTIFICATION_CHANNELS = {
  EMAIL: 'email',
  SOCKET: 'socket',
  PUSH: 'push',
  SMS: 'sms',
} as const;

export const NOTIFICATION_CHANNEL_OPTIONS = [
  { value: 'email', label: 'Email', icon: '📧', color: 'blue' },
  { value: 'socket', label: 'Socket', icon: '🔌', color: 'cyan' },
  { value: 'push', label: 'Push Notification', icon: '🔔', color: 'green' },
  { value: 'sms', label: 'SMS', icon: '📱', color: 'orange' },
] as const;

export const NOTIFICATION_PRIORITIES = {
  LOW: 'low',
  NORMAL: 'normal',
  HIGH: 'high',
  URGENT: 'urgent',
} as const;

export const NOTIFICATION_PRIORITY_OPTIONS = [
  { value: 'low', label: 'Low', color: 'default' },
  { value: 'normal', label: 'Normal', color: 'blue' },
  { value: 'high', label: 'High', color: 'orange' },
  { value: 'urgent', label: 'Urgent', color: 'red' },
] as const;

export const NOTIFICATION_STATUS_OPTIONS = [
  { value: 'pending', label: 'Pending', color: 'default' },
  { value: 'queued', label: 'Queued', color: 'blue' },
  { value: 'sent', label: 'Sent', color: 'cyan' },
  { value: 'delivered', label: 'Delivered', color: 'green' },
  { value: 'failed', label: 'Failed', color: 'red' },
  { value: 'cancelled', label: 'Cancelled', color: 'orange' },
] as const;

export const RECIPIENT_TYPE_OPTIONS = [
  { value: 'user', label: 'User', icon: '👤', color: 'blue' },
  { value: 'email', label: 'Email', icon: '📧', color: 'green' },
  { value: 'phone', label: 'Phone', icon: '📱', color: 'orange' },
  { value: 'device', label: 'Device', icon: '📱', color: 'purple' },
] as const;

export const RECIPIENT_STATUS_OPTIONS = [
  { value: 'pending', label: 'Pending', color: 'default' },
  { value: 'sent', label: 'Sent', color: 'blue' },
  { value: 'delivered', label: 'Delivered', color: 'green' },
  { value: 'read', label: 'Read', color: 'cyan' },
  { value: 'failed', label: 'Failed', color: 'red' },
  { value: 'bounced', label: 'Bounced', color: 'orange' },
] as const;

export const NOTIFICATION_TYPES = {
  SYSTEM: 'system',
  MESSAGE: 'message',
  ACTIVITY: 'activity',
  MARKETING: 'marketing',
} as const;

export const NOTIFICATION_TYPE_OPTIONS = [
  { value: 'system', label: 'System', icon: '⚙️', color: 'blue' },
  { value: 'message', label: 'Message', icon: '💬', color: 'green' },
  { value: 'activity', label: 'Activity', icon: '🎯', color: 'orange' },
  { value: 'marketing', label: 'Marketing', icon: '📢', color: 'purple' },
] as const;

export const PAGINATION_CONFIG = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100,
} as const;

export const REFRESH_INTERVALS = {
  NOTIFICATIONS: 30000, // 30 seconds
  UNREAD_COUNT: 10000, // 10 seconds
} as const;

export const DEFAULT_NOTIFICATION_SETTINGS = {
  emailNotifications: true,
  pushNotifications: true,
  inAppNotifications: true,
  notificationTypes: {
    system: true,
    message: true,
    activity: true,
    marketing: false,
  },
} as const;
