import {
  ClockCircleOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Button,
  Descriptions,
  Divider,
  Modal,
  Popconfirm,
  Space,
  Tag,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  MODULE,
  NOTIFICATION_CHANNEL_OPTIONS,
  NOTIFICATION_PRIORITY_OPTIONS,
} from '../config';
import { NotificationResponse } from '../type';

const { Text, Paragraph } = Typography;

interface NotificationDetailProps {
  notification: NotificationResponse;
  visible: boolean;
  onClose: () => void;
  onMarkAsRead?: (id: number) => void;
  onDelete?: (id: number) => void;
}

const NotificationDetail: React.FC<NotificationDetailProps> = ({
  notification,
  visible,
  onClose,
  onMarkAsRead,
  onDelete,
}) => {
  const { t } = useTranslation(MODULE);

  const getChannelInfo = (channel: string) => {
    return (
      NOTIFICATION_CHANNEL_OPTIONS.find((c) => c.value === channel) || {
        icon: '📄',
        label: channel,
        color: 'default',
      }
    );
  };

  const getPriorityInfo = (priority: string) => {
    return (
      NOTIFICATION_PRIORITY_OPTIONS.find((p) => p.value === priority) || {
        label: priority,
        color: 'default',
      }
    );
  };

  const isExpired =
    notification.scheduled_at &&
    dayjs().isAfter(dayjs(notification.scheduled_at));
  const willExpireSoon =
    notification.scheduled_at &&
    dayjs().add(1, 'day').isAfter(dayjs(notification.scheduled_at)) &&
    !isExpired;

  const channelInfo = getChannelInfo(notification.channel);
  const priorityInfo = getPriorityInfo(notification.priority || 'normal');

  const handleMarkAsRead = () => {
    onMarkAsRead?.(notification.id);
  };

  const handleDelete = () => {
    onDelete?.(notification.id);
    onClose();
  };

  const formatDate = (dateString: string) => {
    return dayjs(dateString).format('DD/MM/YYYY HH:mm:ss');
  };

  return (
    <Modal
      title={
        <Space>
          <InfoCircleOutlined />
          {t('notificationDetail')}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={700}
      footer={[
        <Button key="close" onClick={onClose}>
          {t('closeDetail')}
        </Button>,
        notification.status === 'pending' && (
          <Button
            key="send"
            type="primary"
            icon={<EyeOutlined />}
            onClick={handleMarkAsRead}
          >
            {t('btnSend')}
          </Button>
        ),
        <Popconfirm
          key="delete"
          title={t('deleteConfirm')}
          onConfirm={handleDelete}
          okText="Yes"
          cancelText="No"
        >
          <Button danger icon={<DeleteOutlined />}>
            {t('btnDelete')}
          </Button>
        </Popconfirm>,
      ].filter(Boolean)}
      destroyOnClose
    >
      <div>
        {/* Status Alerts */}
        {notification.status === 'pending' && (
          <Alert
            message={t('statusPending')}
            description="This notification is pending to be sent"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {notification.status === 'failed' && (
          <Alert
            message={t('notificationFailed')}
            description={
              notification.error_message || 'This notification failed to send'
            }
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {notification.scheduled_at &&
          dayjs().isBefore(dayjs(notification.scheduled_at)) && (
            <Alert
              message="Scheduled Notification"
              description={`This notification is scheduled to be sent on ${formatDate(notification.scheduled_at)}`}
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}

        {/* Basic Information */}
        <Descriptions
          title="Basic Information"
          bordered
          column={2}
          size="small"
          style={{ marginBottom: 24 }}
        >
          <Descriptions.Item label={t('notification_id')} span={2}>
            <Text code>{notification.id}</Text>
          </Descriptions.Item>

          <Descriptions.Item label={t('subject')} span={2}>
            <Text strong style={{ fontSize: 16 }}>
              {notification.subject}
            </Text>
          </Descriptions.Item>

          <Descriptions.Item label={t('channel')}>
            <Tag color={channelInfo.color}>
              {channelInfo.icon}{' '}
              {t(`channel${channelInfo.label.replace(/\s+/g, '')}`)}
            </Tag>
          </Descriptions.Item>

          <Descriptions.Item label={t('priority')}>
            <Tag color={priorityInfo.color}>
              {t(`priority${priorityInfo.label}`)}
            </Tag>
          </Descriptions.Item>

          <Descriptions.Item label={t('status')}>
            <Tag
              color={
                notification.status === 'delivered'
                  ? 'green'
                  : notification.status === 'failed'
                    ? 'red'
                    : 'blue'
              }
            >
              {t(`status_${notification.status}`)}
            </Tag>
          </Descriptions.Item>

          <Descriptions.Item label={t('template_id')}>
            <Text code>{notification.template_id || 'N/A'}</Text>
          </Descriptions.Item>

          <Descriptions.Item label={t('created_at')}>
            <Space>
              <ClockCircleOutlined />
              {formatDate(notification.created_at)}
            </Space>
          </Descriptions.Item>

          {notification.sent_at && (
            <Descriptions.Item label={t('sent_at')}>
              <Space>
                <ClockCircleOutlined />
                {formatDate(notification.sent_at)}
              </Space>
            </Descriptions.Item>
          )}

          {notification.delivered_at && (
            <Descriptions.Item label={t('delivered_at')}>
              <Space>
                <ClockCircleOutlined />
                {formatDate(notification.delivered_at)}
              </Space>
            </Descriptions.Item>
          )}

          {notification.scheduled_at && (
            <Descriptions.Item label={t('scheduled_at')} span={2}>
              <Space>
                <ExclamationCircleOutlined
                  style={{ color: isExpired ? 'red' : 'orange' }}
                />
                {formatDate(notification.scheduled_at)}
                {isExpired && <Text type="danger">(Past Due)</Text>}
              </Space>
            </Descriptions.Item>
          )}
        </Descriptions>

        {/* Template Data */}
        {notification.template_data &&
          Object.keys(notification.template_data).length > 0 && (
            <div style={{ marginBottom: 24 }}>
              <h4>{t('template_data')}</h4>
              <div
                style={{
                  background: '#f5f5f5',
                  padding: '16px',
                  borderRadius: '6px',
                  border: '1px solid #d9d9d9',
                  fontFamily: 'monospace',
                  fontSize: '12px',
                  maxHeight: '200px',
                  overflow: 'auto',
                }}
              >
                <pre style={{ margin: 0 }}>
                  {JSON.stringify(notification.template_data, null, 2)}
                </pre>
              </div>
            </div>
          )}

        {/* Metadata */}
        {notification.metadata &&
          Object.keys(notification.metadata).length > 0 && (
            <div style={{ marginBottom: 24 }}>
              <h4>{t('metadata')}</h4>
              <div
                style={{
                  background: '#f5f5f5',
                  padding: '16px',
                  borderRadius: '6px',
                  border: '1px solid #d9d9d9',
                  fontFamily: 'monospace',
                  fontSize: '12px',
                  maxHeight: '200px',
                  overflow: 'auto',
                }}
              >
                <pre style={{ margin: 0 }}>
                  {JSON.stringify(notification.metadata, null, 2)}
                </pre>
              </div>
            </div>
          )}

        <Divider />

        {/* Footer Metadata */}
        <div style={{ fontSize: 12, color: '#666' }}>
          <Space split={<Divider type="vertical" />}>
            <span>ID: {notification.id}</span>
            <span>Created: {formatDate(notification.created_at)}</span>
            <span>Updated: {formatDate(notification.updated_at)}</span>
            <span>Retry Count: {notification.retry_count}</span>
          </Space>
        </div>
      </div>
    </Modal>
  );
};

export default NotificationDetail;
