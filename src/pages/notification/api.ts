import {
  ApiResponse,
  CursorResponse,
  apiService,
} from '../../services/api.service';
import { API_ENDPOINTS } from './config';
import {
  NotificationResponse,
  NotificationFilter,
  NotificationListResponse,
  NotificationCreateRequest,
  NotificationUpdateRequest,
  NotificationBulkActionRequest,
  NotificationStatsRequest,
  NotificationStatsResponse,
  NotificationPreviewRequest,
  NotificationPreviewResponse,
  NotificationTestRequest,
  NotificationTestResponse,
  CursorPagination,
  NotificationTemplateResponse,
  NotificationTemplateCreateRequest,
  NotificationTemplateUpdateRequest,
  NotificationTemplateFilter,
  NotificationTemplateListResponse,
  NotificationTemplateVersionResponse,
  NotificationTemplateVersionCreateRequest,
  NotificationTemplateVersionUpdateRequest,
} from './type';

const url = API_ENDPOINTS.NOTIFICATIONS;

// Get all notifications with cursor pagination
export async function getNotifications(
  params: NotificationFilter = {},
): Promise<CursorResponse<NotificationResponse>> {
  const response = await apiService.get<CursorResponse<NotificationResponse>>(
    url,
    { params },
  );
  return response.data;
}

// Get notification by ID
export async function getNotification(
  id: number,
): Promise<ApiResponse<NotificationResponse>> {
  const response = await apiService.get<ApiResponse<NotificationResponse>>(
    `${url}/${id}`,
  );
  return response.data;
}

// Mark notification as read
export async function markAsRead(
  id: number,
): Promise<ApiResponse<NotificationResponse>> {
  const response = await apiService.patch<ApiResponse<NotificationResponse>>(
    `${url}/${id}/read`,
  );
  return response.data;
}

// Mark all notifications as read
export async function markAllAsRead(): Promise<ApiResponse<{ count: number }>> {
  const response = await apiService.patch<ApiResponse<{ count: number }>>(
    API_ENDPOINTS.MARK_ALL_READ,
  );
  return response.data;
}

// Delete notification
export async function deleteNotification(
  id: number,
): Promise<ApiResponse<any>> {
  const response = await apiService.delete<ApiResponse<any>>(`${url}/${id}`);
  return response.data;
}

// Get unread count
export async function getUnreadCount(): Promise<
  ApiResponse<{ unread_count: number }>
> {
  const response = await apiService.get<ApiResponse<{ unread_count: number }>>(
    API_ENDPOINTS.UNREAD_COUNT,
  );
  return response.data;
}

// Get notification settings
export async function getNotificationSettings(): Promise<ApiResponse<any>> {
  const response = await apiService.get<ApiResponse<any>>(
    API_ENDPOINTS.SETTINGS,
  );
  return response.data;
}

// Update notification settings
export async function updateNotificationSettings(
  settings: any,
): Promise<ApiResponse<any>> {
  const response = await apiService.put<ApiResponse<any>>(
    API_ENDPOINTS.SETTINGS,
    settings,
  );
  return response.data;
}

// Get notification statistics
export async function getNotificationStats(
  params?: NotificationStatsRequest,
): Promise<ApiResponse<NotificationStatsResponse>> {
  const response = await apiService.get<ApiResponse<NotificationStatsResponse>>(
    API_ENDPOINTS.STATISTICS,
    { params },
  );
  return response.data;
}

// Create notification
export async function createNotification(
  payload: NotificationCreateRequest,
): Promise<ApiResponse<NotificationResponse>> {
  const response = await apiService.post<ApiResponse<NotificationResponse>>(
    url,
    payload,
  );
  return response.data;
}

// Update notification
export async function updateNotification(
  id: number,
  payload: NotificationUpdateRequest,
): Promise<ApiResponse<NotificationResponse>> {
  const response = await apiService.put<ApiResponse<NotificationResponse>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

// Cancel notification
export async function cancelNotification(
  id: number,
): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${url}/${id}/cancel`,
  );
  return response.data;
}

// Retry notification
export async function retryNotification(id: number): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${url}/${id}/retry`,
  );
  return response.data;
}

// Send notification
export async function sendNotification(id: number): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(`${url}/${id}/send`);
  return response.data;
}

// Legacy bulk operations (deprecated - use bulkNotificationAction instead)
export async function bulkMarkAsRead(
  ids: number[],
): Promise<ApiResponse<{ count: number }>> {
  return bulkNotificationAction({
    notification_ids: ids,
    action: 'cancel', // This should be updated to match actual API
  }) as any;
}

export async function bulkDelete(
  ids: number[],
): Promise<ApiResponse<{ count: number }>> {
  return bulkNotificationAction({
    notification_ids: ids,
    action: 'delete',
  }) as any;
}

// Search notifications
export async function searchNotifications(
  query: string,
  params: NotificationFilter = {},
): Promise<CursorResponse<NotificationResponse>> {
  const queryParams = {
    q: query,
    ...params,
  };

  const response = await apiService.get<CursorResponse<NotificationResponse>>(
    `${url}/search`,
    { params: queryParams },
  );
  return response.data;
}

// Bulk operations
export async function bulkNotificationAction(
  payload: NotificationBulkActionRequest,
): Promise<
  ApiResponse<{ success: boolean; message: string; affected_count: number }>
> {
  const response = await apiService.post<
    ApiResponse<{ success: boolean; message: string; affected_count: number }>
  >(`${url}/bulk`, payload);
  return response.data;
}

// Preview notification
export async function previewNotification(
  payload: NotificationPreviewRequest,
): Promise<ApiResponse<NotificationPreviewResponse>> {
  const response = await apiService.post<
    ApiResponse<NotificationPreviewResponse>
  >(`${url}/preview`, payload);
  return response.data;
}

// Test notification
export async function testNotification(
  payload: NotificationTestRequest,
): Promise<ApiResponse<NotificationTestResponse>> {
  const response = await apiService.post<ApiResponse<NotificationTestResponse>>(
    `${url}/test`,
    payload,
  );
  return response.data;
}

// ===================== TEMPLATE MANAGEMENT API =====================

const templatesUrl = API_ENDPOINTS.TEMPLATES;

// Get all templates with pagination
export async function getTemplates(
  params: NotificationTemplateFilter = {},
): Promise<ApiResponse<NotificationTemplateListResponse>> {
  const response = await apiService.get<
    ApiResponse<NotificationTemplateListResponse>
  >(templatesUrl, { params });
  return response.data;
}

// Get template by ID
export async function getTemplate(
  id: number,
): Promise<ApiResponse<NotificationTemplateResponse>> {
  const response = await apiService.get<
    ApiResponse<NotificationTemplateResponse>
  >(`${templatesUrl}/${id}`);
  return response.data;
}

// Create new template
export async function createTemplate(
  payload: NotificationTemplateCreateRequest,
): Promise<ApiResponse<NotificationTemplateResponse>> {
  const response = await apiService.post<
    ApiResponse<NotificationTemplateResponse>
  >(templatesUrl, payload);
  return response.data;
}

// Update template
export async function updateTemplate(
  id: number,
  payload: NotificationTemplateUpdateRequest,
): Promise<ApiResponse<NotificationTemplateResponse>> {
  const response = await apiService.put<
    ApiResponse<NotificationTemplateResponse>
  >(`${templatesUrl}/${id}`, payload);
  return response.data;
}

// Delete template
export async function deleteTemplate(id: number): Promise<ApiResponse<any>> {
  const response = await apiService.delete<ApiResponse<any>>(
    `${templatesUrl}/${id}`,
  );
  return response.data;
}

// Activate/Deactivate template
export async function toggleTemplateStatus(
  id: number,
  is_active: boolean,
): Promise<ApiResponse<NotificationTemplateResponse>> {
  const response = await apiService.patch<
    ApiResponse<NotificationTemplateResponse>
  >(`${templatesUrl}/${id}/status`, { is_active });
  return response.data;
}

// ===================== TEMPLATE VERSION MANAGEMENT API =====================

// Get template versions
export async function getTemplateVersions(
  templateId: number,
): Promise<ApiResponse<NotificationTemplateVersionResponse[]>> {
  const response = await apiService.get<
    ApiResponse<NotificationTemplateVersionResponse[]>
  >(`${templatesUrl}/${templateId}/versions`);
  return response.data;
}

// Get template version by ID
export async function getTemplateVersion(
  templateId: number,
  versionId: number,
): Promise<ApiResponse<NotificationTemplateVersionResponse>> {
  const response = await apiService.get<
    ApiResponse<NotificationTemplateVersionResponse>
  >(`${templatesUrl}/${templateId}/versions/${versionId}`);
  return response.data;
}

// Create new template version
export async function createTemplateVersion(
  templateId: number,
  payload: NotificationTemplateVersionCreateRequest,
): Promise<ApiResponse<NotificationTemplateVersionResponse>> {
  const response = await apiService.post<
    ApiResponse<NotificationTemplateVersionResponse>
  >(`${templatesUrl}/${templateId}/versions`, payload);
  return response.data;
}

// Update template version
export async function updateTemplateVersion(
  templateId: number,
  versionId: number,
  payload: NotificationTemplateVersionUpdateRequest,
): Promise<ApiResponse<NotificationTemplateVersionResponse>> {
  const response = await apiService.put<
    ApiResponse<NotificationTemplateVersionResponse>
  >(`${templatesUrl}/${templateId}/versions/${versionId}`, payload);
  return response.data;
}

// Delete template version
export async function deleteTemplateVersion(
  templateId: number,
  versionId: number,
): Promise<ApiResponse<any>> {
  const response = await apiService.delete<ApiResponse<any>>(
    `${templatesUrl}/${templateId}/versions/${versionId}`,
  );
  return response.data;
}

// Activate template version
export async function activateTemplateVersion(
  templateId: number,
  versionId: number,
): Promise<ApiResponse<NotificationTemplateVersionResponse>> {
  const response = await apiService.patch<
    ApiResponse<NotificationTemplateVersionResponse>
  >(`${templatesUrl}/${templateId}/versions/${versionId}/activate`);
  return response.data;
}

// Approve template version
export async function approveTemplateVersion(
  templateId: number,
  versionId: number,
): Promise<ApiResponse<NotificationTemplateVersionResponse>> {
  const response = await apiService.patch<
    ApiResponse<NotificationTemplateVersionResponse>
  >(`${templatesUrl}/${templateId}/versions/${versionId}/approve`);
  return response.data;
}
