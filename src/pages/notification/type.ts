export enum NotificationChannel {
  EMAIL = 'email',
  SOCKET = 'socket',
  PUSH = 'push',
  SMS = 'sms',
}

export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

export enum NotificationStatus {
  PENDING = 'pending',
  QUEUED = 'queued',
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export enum RecipientType {
  USER = 'user',
  EMAIL = 'email',
  PHONE = 'phone',
  DEVICE = 'device',
}

export enum RecipientStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
  BOUNCED = 'bounced',
}

// Create request DTOs
export interface NotificationRecipientRequest {
  user_id?: number;
  recipient_type: RecipientType;
  recipient_address: string;
  device_token?: string;
  metadata?: Record<string, any>;
}

export interface NotificationCreateRequest {
  type: string;
  channel: NotificationChannel;
  priority?: NotificationPriority;
  subject: string;
  template_id?: number;
  template_data?: Record<string, any>;
  recipients: NotificationRecipientRequest[];
  scheduled_at?: string;
  metadata?: Record<string, any>;
}

export interface NotificationUpdateRequest {
  status?: NotificationStatus;
  scheduled_at?: string;
  metadata?: Record<string, any>;
}

// Response DTOs
export interface NotificationRecipientResponse {
  id: number;
  tenant_id: number;
  notification_id: number;
  user_id?: number;
  recipient_type: RecipientType;
  recipient_address: string;
  device_token?: string;
  status: RecipientStatus;
  delivered_at?: string;
  read_at?: string;
  failed_at?: string;
  bounced_at?: string;
  error_message?: string;
  delivery_info?: Record<string, any>;
  engagement_data?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface NotificationResponse {
  id: number;
  tenant_id: number;
  type: string;
  channel: NotificationChannel;
  priority: NotificationPriority;
  subject: string;
  template_id?: number;
  template_data?: Record<string, any>;
  status: NotificationStatus;
  scheduled_at?: string;
  sent_at?: string;
  delivered_at?: string;
  failed_at?: string;
  error_message?: string;
  retry_count: number;
  max_retries: number;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  template?: NotificationTemplateResponse;
  recipients?: NotificationRecipientResponse[];
}

export interface NotificationTemplateResponse {
  id: number;
  tenant_id: number;
  code: string;
  name: string;
  type: string;
  channel: NotificationChannel;
  description?: string;
  variables?: string[];
  is_active: boolean;
  version_count: number;
  active_version_id?: number;
  created_by?: number;
  updated_by?: number;
  created_at: string;
  updated_at: string;
}

// Template management types
export interface NotificationTemplateCreateRequest {
  code: string;
  name: string;
  type: string;
  channel: NotificationChannel;
  description?: string;
  variables?: string[];
  is_active?: boolean;
}

export interface NotificationTemplateUpdateRequest {
  name?: string;
  description?: string;
  variables?: string[];
  is_active?: boolean;
}

export interface NotificationTemplateFilter {
  type?: string;
  channel?: NotificationChannel;
  is_active?: boolean;
  search?: string;
  page?: number;
  page_size?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface NotificationTemplateListResponse {
  templates: NotificationTemplateResponse[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

// Template version types
export interface NotificationTemplateVersionResponse {
  id: number;
  tenant_id: number;
  template_id: number;
  version_number: number;
  language: string;
  subject: string;
  body_html: string;
  body_text?: string;
  variables?: string[];
  is_active: boolean;
  is_approved: boolean;
  created_at: string;
  updated_at: string;
}

export interface NotificationTemplateVersionCreateRequest {
  language: string;
  subject: string;
  body_html: string;
  body_text?: string;
  variables?: string[];
  is_active?: boolean;
}

export interface NotificationTemplateVersionUpdateRequest {
  subject?: string;
  body_html?: string;
  body_text?: string;
  variables?: string[];
  is_active?: boolean;
  is_approved?: boolean;
}

export interface NotificationFilter {
  cursor?: string;
  limit?: number;
  type?: string;
  channel?: NotificationChannel;
  status?: NotificationStatus;
  priority?: NotificationPriority;
  date_from?: string;
  date_to?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface CursorPagination {
  cursor?: string;
  limit?: number;
}

export interface CursorResponse<T> {
  data: T[];
  pagination: {
    next_cursor?: string;
    has_more: boolean;
    total_count?: number;
  };
}

export interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  inAppNotifications: boolean;
  notificationTypes: {
    system: boolean;
    message: boolean;
    activity: boolean;
    marketing: boolean;
  };
}

export interface UnreadCount {
  unread_count: number;
}

export interface NotificationMeta {
  current_page: number;
  total_pages: number;
  total_items: number;
  has_more: boolean;
  next_cursor?: string;
}

export interface NotificationListResponse {
  notifications: NotificationResponse[];
}

export interface NotificationBulkActionRequest {
  notification_ids: number[];
  action: 'cancel' | 'retry' | 'delete';
  reason?: string;
}

export interface NotificationStatsRequest {
  date_from?: string;
  date_to?: string;
  group_by?: 'day' | 'week' | 'month' | 'channel' | 'type' | 'status';
}

export interface NotificationChannelStats {
  channel: NotificationChannel;
  total: number;
  sent: number;
  delivered: number;
  failed: number;
  open_rate: number;
  click_rate: number;
}

export interface NotificationTypeStats {
  type: string;
  total: number;
  sent: number;
  delivered: number;
  failed: number;
  open_rate: number;
  click_rate: number;
}

export interface NotificationTimelineStats {
  date: string;
  total: number;
  sent: number;
  delivered: number;
  failed: number;
}

export interface NotificationStatsResponse {
  total_notifications: number;
  sent_notifications: number;
  delivered_notifications: number;
  failed_notifications: number;
  click_rate: number;
  open_rate: number;
  bounce_rate: number;
  by_channel: Record<string, NotificationChannelStats>;
  by_type: Record<string, NotificationTypeStats>;
  by_status: Record<string, number>;
  timeline?: NotificationTimelineStats[];
}

export interface NotificationPreviewRequest {
  template_id: number;
  template_data?: Record<string, any>;
  recipient_address: string;
}

export interface NotificationPreviewResponse {
  subject: string;
  body_html: string;
  body_text: string;
  preview_url: string;
}

export interface NotificationTestRequest {
  type: string;
  channel: NotificationChannel;
  subject: string;
  template_id?: number;
  template_data?: Record<string, any>;
  recipients: NotificationRecipientRequest[];
}

export interface NotificationTestResponse {
  success: boolean;
  message: string;
  notification_id: number;
  test_id: string;
}
