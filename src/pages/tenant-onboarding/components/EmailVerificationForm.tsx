import {
  CheckCircleOutlined,
  MailOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>ton, Card, Result, Space, Typography, message } from 'antd';
import React, { useEffect, useState } from 'react';
import useUserStore from '../../user/store';
import { tenantOnboardingApi } from '../api';
import OnboardingService from '../../onboarding/components/onboarding-service';
import { OnboardingStep } from '../../onboarding/type';

const { Title, Text, Paragraph } = Typography;

interface EmailVerificationFormProps {
  userEmail?: string;
  isVerified?: boolean;
  onVerificationComplete: () => void;
  onResendEmail?: () => void;
  loading?: boolean;
}

const EmailVerificationForm: React.FC<EmailVerificationFormProps> = ({
  userEmail: propUserEmail,
  isVerified = false,
  onVerificationComplete,
  onResendEmail,
  loading = false,
}) => {
  const [isChecking, setIsChecking] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);

  // Get user email from store
  const { profile, getProfile } = useUserStore();
  const userEmail = propUserEmail || profile?.email || '<EMAIL>';

  // Load user profile when component mounts
  useEffect(() => {
    if (!profile) {
      getProfile();
    }
  }, [profile, getProfile]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (resendCooldown > 0) {
      interval = setInterval(() => {
        setResendCooldown((prev) => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendCooldown]);

  const handleCheckVerification = async () => {
    setIsChecking(true);
    try {
      // Call the real API to check onboarding status
      const response = await tenantOnboardingApi.getOnboardingStatus();

      // Update OnboardingService localStorage with the API response
      if (response.data) {
        const { step, onboarding_step, current_step, is_completed } =
          response.data;

        // Determine the step value from different possible fields
        const stepValue = step || onboarding_step || current_step;

        // Map API step values to OnboardingStep enum
        if (stepValue) {
          let onboardingStepEnum: OnboardingStep;
          switch (stepValue.toLowerCase()) {
            case 'organization_setup':
            case 'organization':
            case 'tenant':
              onboardingStepEnum = OnboardingStep.ORGANIZATION_SETUP;
              break;
            case 'website_creation':
            case 'website':
            case 'website_setup':
              onboardingStepEnum = OnboardingStep.WEBSITE_CREATION;
              break;
            case 'completed':
            case 'complete':
              onboardingStepEnum = OnboardingStep.COMPLETED;
              break;
            default:
              onboardingStepEnum = OnboardingStep.ORGANIZATION_SETUP;
          }

          OnboardingService.setCurrentStep(onboardingStepEnum);
        }

        // Update completion status
        if (is_completed !== undefined && is_completed) {
          OnboardingService.markAsCompleted();
        }
      }

      if (response.data && (response.data as any).progress) {
        // Check if email_verification step is completed
        const emailVerificationStep = (response.data as any).progress.find(
          (step: any) => step.step_name === 'email_verification',
        );

        if (
          emailVerificationStep &&
          emailVerificationStep.status === 'completed'
        ) {
          message.success('Email đã được xác thực thành công!');
          onVerificationComplete();
        } else {
          message.info(
            'Email chưa được xác thực. Vui lòng kiểm tra hộp thư của bạn.',
          );
        }
      } else {
        message.warning(
          'Không thể kiểm tra trạng thái xác thực. Vui lòng thử lại.',
        );
      }
    } catch (error) {
      console.error('Error checking verification status:', error);
      message.error('Có lỗi xảy ra khi kiểm tra xác thực email');
    } finally {
      setIsChecking(false);
    }
  };

  const handleResendEmail = async () => {
    if (resendCooldown > 0) return;

    try {
      if (onResendEmail) {
        await onResendEmail();
      }
      message.success('Email xác thực đã được gửi lại!');
      setResendCooldown(60); // 60 seconds cooldown
    } catch (error) {
      message.error('Có lỗi xảy ra khi gửi lại email');
    }
  };

  if (isVerified) {
    return (
      <Card>
        <Result
          status="success"
          icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
          title="Email đã được xác thực"
          subTitle={`Email ${userEmail} đã được xác thực thành công.`}
          extra={
            <Button type="primary" onClick={onVerificationComplete}>
              Tiếp tục
            </Button>
          }
        />
      </Card>
    );
  }

  return (
    <Card>
      <div style={{ textAlign: 'center', padding: '20px 0' }}>
        <MailOutlined
          style={{ fontSize: '64px', color: '#1890ff', marginBottom: '24px' }}
        />

        <Title level={3}>Xác thực địa chỉ email</Title>

        <Paragraph style={{ fontSize: '16px', marginBottom: '24px' }}>
          Chúng tôi đã gửi một email xác thực đến địa chỉ:
        </Paragraph>

        <Text strong style={{ fontSize: '18px', color: '#1890ff' }}>
          {userEmail}
        </Text>

        <Alert
          message="Kiểm tra hộp thư của bạn"
          description="Vui lòng kiểm tra hộp thư (bao gồm cả thư mục spam) và nhấp vào liên kết xác thực trong email."
          type="info"
          showIcon
          style={{ margin: '24px 0', textAlign: 'left' }}
        />

        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Button
            type="primary"
            size="large"
            loading={isChecking}
            onClick={handleCheckVerification}
            style={{ minWidth: '200px' }}
          >
            {isChecking ? 'Đang kiểm tra...' : 'Tôi đã xác thực email'}
          </Button>

          <Space>
            <Text>Không nhận được email?</Text>
            <Button
              type="link"
              icon={<ReloadOutlined />}
              onClick={handleResendEmail}
              disabled={resendCooldown > 0}
              loading={loading}
            >
              {resendCooldown > 0
                ? `Gửi lại sau ${resendCooldown}s`
                : 'Gửi lại email'}
            </Button>
          </Space>
        </Space>
      </div>
    </Card>
  );
};

export default EmailVerificationForm;
