import React from 'react';
import { Route, Routes } from 'react-router-dom';
import List from './list';
import Form from './form';
import Detail from './detail';

export default function CrmCustomerGroup() {
  return (
    <Routes>
      <Route path="/:id" element={<Detail />} />
      <Route path="/create" element={<Form />} />
      <Route path="/edit/:id" element={<Form />} />
      <Route index element={<List />} />
    </Routes>
  );
}
