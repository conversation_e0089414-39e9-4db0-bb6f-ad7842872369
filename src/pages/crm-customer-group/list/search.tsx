import React from 'react';
import { Button, Col, Form, Input, Row, Select } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { useTranslation } from 'react-i18next';

export default function Search({ onSearch }: any) {
  const { t } = useTranslation();
  const [form] = useForm();

  return (
    <>
      <Form form={form} onFinish={onSearch}>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="name">
              <Input placeholder={t('crm-customer-group:search.name')} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="is_active">
              <Select
                placeholder={t('crm-customer-group:search.status')}
                allowClear
              >
                <Select.Option value="true">{t('common:active')}</Select.Option>
                <Select.Option value="false">
                  {t('common:inactive')}
                </Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <Button type="primary" htmlType="submit">
              {t('common:search')}
            </Button>
            <Button
              style={{ margin: '0 8px' }}
              onClick={() => {
                form.resetFields();
                onSearch({});
              }}
            >
              {t('common:clear')}
            </Button>
          </Col>
        </Row>
      </Form>
    </>
  );
}
