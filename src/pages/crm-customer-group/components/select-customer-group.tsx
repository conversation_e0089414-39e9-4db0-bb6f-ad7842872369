import React, { useEffect, useState } from 'react';
import { Select, SelectProps, Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import { getOptions } from '../api';
import { SelectOption } from '../../../types.global';

interface Props extends Omit<SelectProps<any>, 'options'> {
  placeholder?: string;
}

export default function SelectCustomerGroup({ placeholder, ...rest }: Props) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<SelectOption[]>([]);

  useEffect(() => {
    fetchOptions();
  }, []);

  const fetchOptions = async () => {
    try {
      setLoading(true);
      const res = await getOptions();
      setOptions(res.data || []);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Select
      placeholder={placeholder || t('crm-customer-group:select.placeholder')}
      allowClear
      showSearch
      filterOption={(input, option) =>
        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
      }
      options={options}
      loading={loading}
      notFoundContent={loading ? <Spin size="small" /> : null}
      {...rest}
    />
  );
}
