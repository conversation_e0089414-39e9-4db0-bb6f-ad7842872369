import React, { useEffect, useState } from 'react';
import {
  Button,
  Col,
  Form,
  Input,
  InputNumber,
  Modal,
  Row,
  Switch,
  message,
} from 'antd';
import { useTranslation } from 'react-i18next';
import { createItem, updateItem } from '../api';
import { CustomerGroup } from '../type';

interface Props {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  initialValues?: CustomerGroup;
  isEdit?: boolean;
}

export default function CustomerGroupModal({
  visible,
  onCancel,
  onSuccess,
  initialValues,
  isEdit = false,
}: Props) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible && initialValues) {
      form.setFieldsValue(initialValues);
    } else {
      form.resetFields();
    }
  }, [visible, initialValues]);

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);

      if (isEdit && initialValues?.group_id) {
        await updateItem(String(initialValues.group_id), values);
        message.success(t('common:updated_successfully'));
      } else {
        await createItem(values);
        message.success(t('common:created_successfully'));
      }

      form.resetFields();
      onSuccess();
    } catch (error) {
      console.error(error);
      message.error(t('common:error_occurred'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={
        isEdit
          ? t('crm-customer-group:form.edit_title')
          : t('crm-customer-group:form.create_title')
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      maskClosable={false}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{ is_active: true, discount_percentage: 0 }}
      >
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="name"
              label={t('crm-customer-group:form.name')}
              rules={[
                {
                  required: true,
                  message: t('common:field_required'),
                },
              ]}
            >
              <Input />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item
              name="discount_percentage"
              label={t('crm-customer-group:form.discount_percentage')}
              rules={[
                {
                  required: true,
                  message: t('common:field_required'),
                },
              ]}
            >
              <InputNumber
                min={0}
                max={100}
                style={{ width: '100%' }}
                formatter={(value) => `${value}%`}
                parser={(value) => value!.replace('%', '')}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="description"
          label={t('crm-customer-group:form.description')}
        >
          <Input.TextArea rows={4} />
        </Form.Item>

        <Form.Item
          name="is_active"
          label={t('crm-customer-group:form.status')}
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Button onClick={onCancel} style={{ marginRight: 8 }}>
            {t('common:cancel')}
          </Button>
          <Button type="primary" htmlType="submit" loading={loading}>
            {isEdit ? t('common:update') : t('common:create')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
}
