import {
  ApiResponse,
  ApiResponsePagination,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { CustomerGroup } from './type';

const url = `/api/cms/v1/crm/customer-groups`;

export async function getItems(
  params: any,
): Promise<ApiResponsePagination<CustomerGroup[]>> {
  const response = await apiService.get<ApiResponsePagination<CustomerGroup[]>>(
    url,
    {
      params,
    },
  );
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<CustomerGroup>> {
  const response = await apiService.get<ApiResponse<CustomerGroup>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function createItem(
  payload: any,
): Promise<ApiResponse<CustomerGroup>> {
  const response = await apiService.post<ApiResponse<CustomerGroup>>(
    url,
    payload,
  );
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<CustomerGroup>> {
  const response = await apiService.put<ApiResponse<CustomerGroup>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(
  id: string,
): Promise<ApiResponse<CustomerGroup>> {
  const response = await apiService.delete<ApiResponse<CustomerGroup>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<CustomerGroup[]>> {
  const response = await apiService.get<ApiResponse<CustomerGroup[]>>(
    `${url}/all`,
  );
  return response.data;
}
