import { create } from 'zustand';
import { CustomerGroup } from './type';

interface CrmCustomerGroupState {
  items: CustomerGroup[];
  item: CustomerGroup | null;
  setItems: (data: CustomerGroup[]) => void;
  setItem: (data: CustomerGroup | null) => void;
}

export const useStore = create<CrmCustomerGroupState>((set) => ({
  items: [],
  item: null,
  setItems: (data) => set({ items: data }),
  setItem: (data) => set({ item: data }),
}));
