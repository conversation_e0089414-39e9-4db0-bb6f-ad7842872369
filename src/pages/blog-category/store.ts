import { create } from 'zustand';
import { deleteItem, getOptions, rebuildHierarchy } from './api';
import { BlogCategory, BlogCategoryResponse } from './type';

export interface StoreState {
  loading: boolean;
  items: BlogCategoryResponse[]; // Updated to use new API v3 response type
  options: any[];
  setLoading: (loading: boolean) => void;
  delete: (id: string) => void;
  setOptions: (options: any) => void;
  deleteItem: (id: string) => Promise<void>;
  getOptions: () => Promise<void>;
  rebuildHierarchy: () => Promise<void>;
}

const useBlogCategoryStore = create<StoreState>((set) => ({
  items: [],
  item: undefined,
  options: [],
  loading: false,
  setLoading: (loading: boolean) => {
    set({ loading });
  },
  delete: (id: string) => {
    set((state) => {
      const itemsNew = state.items.filter((b) => b.id !== id);
      return { items: itemsNew };
    });
  },
  setOptions: (options: any) => {
    set({ options });
  },
  deleteItem: async (id: string) => {
    set({ loading: true });
    await deleteItem(id);
    set((state) => {
      const itemsNew = state.items.filter((b) => b.id !== id);
      return { items: itemsNew, loading: false };
    });
  },
  getOptions: async () => {
    const response = await getOptions();
    set({ options: response.data });
  },
  rebuildHierarchy: async () => {
    set({ loading: true });
    try {
      await rebuildHierarchy();
    } catch (error) {
      console.error('Failed to rebuild hierarchy:', error);
    } finally {
      set({ loading: false });
    }
  },
}));

export default useBlogCategoryStore;
