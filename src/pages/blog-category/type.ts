// Blog Category Status enum - matches API v3
export type BlogCategoryStatus = 'active' | 'inactive' | 'deleted';

export const STATUS = [
  {
    label: 'Bật',
    value: 'active' as BlogCategoryStatus,
    color: 'green',
  },
  {
    label: 'Tắt',
    value: 'inactive' as BlogCategoryStatus,
    color: 'red',
  },
  {
    label: 'Đã xóa',
    value: 'deleted' as BlogCategoryStatus,
    color: 'gray',
  },
];

// CRITICAL: Use BlogCategoryResponse for all API responses and data display
// NO backward compatibility allowed - align with API v3 DTOs
export interface BlogCategoryResponse {
  id: number;
  tenant_id: number;
  website_id: number;
  parent_id?: number | null;
  name: string;
  slug: string;
  description?: string | null;
  status: BlogCategoryStatus;
  lft: number; // Nested set left boundary
  rgt: number; // Nested set right boundary
  depth: number; // Hierarchy depth
  children_count: number; // Count of direct children
  post_count?: number;
  id_path?: string | null; // Hierarchical path for traversal
  created_at: string;
  updated_at: string;
  children?: BlogCategoryResponse[];
}

// Legacy interface for backward compatibility - DEPRECATED
// Use BlogCategoryResponse instead
export interface BlogCategory {
  id: number;
  tenant_id: number;
  parent_id: number | null;
  name: string;
  slug: string;
  description: string;
  image: string;
  lft: number;
  rgt: number;
  depth: number;
  position: number;
  is_active: boolean;
  is_featured: boolean;
  meta_title: string;
  meta_description: string;
  created_at: string;
  updated_at: string;
  created_by: number;
  updated_by: number;
  children?: BlogCategory[];
  post_count?: number;
}

// API v3 Create Request DTO - matches BlogCategoryCreateRequest
export interface BlogCategoryCreateRequest {
  name: string;
  slug?: string;
  description?: string;
}

// Legacy interface - DEPRECATED
// Use BlogCategoryCreateRequest instead
export interface BlogCategoryCreate {
  parent_id?: number | null;
  name: string;
  slug?: string;
  description?: string;
  image?: string;
  position?: number;
  is_active?: boolean;
  is_featured?: boolean;
  meta_title?: string;
  meta_description?: string;
}

// API v3 Update Request DTO - matches BlogCategoryUpdateRequest
export interface BlogCategoryUpdateRequest {
  name: string;
  slug: string;
  description?: string;
}

// Legacy interface - DEPRECATED
// Use BlogCategoryUpdateRequest instead
export interface BlogCategoryUpdate {
  parent_id?: number | null;
  name?: string;
  slug?: string;
  description?: string;
  image?: string;
  position?: number;
  is_active?: boolean;
  is_featured?: boolean;
  meta_title?: string;
  meta_description?: string;
}

// @deprecated - Sử dụng MoveNodeRequest hoặc UpdatePositionRequest thay thế
export interface MoveCategoryRequest {
  category_id: number;
  target_id: number;
  position: 'before' | 'after' | 'child';
}

// Yêu cầu di chuyển danh mục vào danh mục cha khác
export interface MoveNodeRequest {
  category_id: number;
  new_parent_id: number;
  position: number;
}

// Yêu cầu cập nhật vị trí của danh mục
export interface UpdatePositionRequest {
  category_id: number;
  target_id: number; // ID của category làm mốc, node sẽ đứng TRƯỚC node này
}

// Tree structure for UI components
export interface TreeItem {
  id: string;
  content: string;
  children: TreeItem[];
}

// Transform function to convert new API response to legacy format for backward compatibility
export const transformCategoryResponse = (
  category: BlogCategoryResponse,
): BlogCategory => {
  return {
    ...category,
    // Map new fields to legacy fields
    position: 0, // Default position since API v3 uses nested set
    is_active: category.status === 'active',
    description: category.description || '',
    // Set default values for removed legacy fields
    image: '',
    is_featured: false,
    meta_title: '',
    meta_description: '',
    created_by: 0,
    updated_by: 0,
  };
};

// API v3 Move Category Request (POST /categories/{id}/move)
export interface CategoryMoveRequest {
  new_parent_id: number; // 0 for root level, or parent category ID
}

// API v3 Position Update Request (POST /categories/positions)
export interface CategoryPositionUpdate {
  id: number;
  sort_order: number;
}

// API v3 Filter for query parameters - supports cursor pagination
export interface BlogCategoryFilter {
  status?: BlogCategoryStatus;
  sort_by?: 'lft' | 'name' | 'created_at' | 'updated_at';
  sort_order?: 'asc' | 'desc';
  limit?: number;
  search?: string;
  parent_id?: number;
  cursor?: string;
}

// API v3 Bulk position update request
export interface CategoryPositionUpdateRequest {
  positions: Array<{
    id: number;
    position: number;
  }>;
}

// Transform function for create/update requests to new API format
export const transformCategoryForAPI = (
  category: BlogCategoryCreate | BlogCategoryUpdate,
): BlogCategoryCreateRequest | BlogCategoryUpdateRequest => {
  // Remove legacy fields that are not supported in API v3
  const {
    image,
    position,
    is_featured,
    is_active,
    meta_title,
    meta_description,
    parent_id,
    ...apiData
  } = category as any;

  // Only keep fields that are supported in the new API
  return {
    name: apiData.name,
    slug: apiData.slug,
    description: apiData.description,
  };
};
