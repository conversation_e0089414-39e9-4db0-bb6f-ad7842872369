import { PlusCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import { UniqueIdentifier } from '@dnd-kit/core';
import { Button, message } from 'antd';
import { BackButton } from '../../../components/button';
import { ButtonLink } from '../../../components/button';
import queryString from 'query-string';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { SortableTree } from '../../../components/tree';
import { TreeItems } from '../../../components/tree/types';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  deleteItem,
  getHierarchy,
  getTree,
  moveCategory,
  moveCategoryToRight,
  updateCategoryPositions,
  rebuildHierarchy,
} from '../api';
import { CategoryMoveRequest, CategoryMoveToRightRequest, CategoryPositionUpdate } from '../type';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import useBlogCategoryStore from '../store';
import { BlogCategory } from '../type';
import Search from './search';

// Hàm chuyển đổi BlogCategory[] sang TreeItems
const convertToTreeItems = (categories: BlogCategory[]): TreeItems => {
  const convertCategory = (category: BlogCategory): any => {
    return {
      id: category.id,
      name: category.name,
      parentId: category.parent_id,
      children: category.children ? category.children.map(convertCategory) : [],
      position: category.position,
      depth: category.depth,
    };
  };

  return categories.map(convertCategory);
};

export default function List() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading, rebuildHierarchy: storeRebuild } = useBlogCategoryStore();
  const [pagination, setPagination] = useState<any>({
    page: 1,
    limit: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [showSortModal, setShowSortModal] = useState(false);
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();

  // Chuyển đổi categories sang định dạng TreeItems
  const treeItems = useMemo(() => {
    return convertToTreeItems(categories);
  }, [categories]);

  async function fetchData(payload?: any) {
    const params = {
      ...pagination,
      ...query,
      ...filters,
      ...payload,
    };
    //changeBrowserLocation(navigate, pathname, params);
    const response = await getTree(params);
    if (response.status.success) {
      setCategories(response.data || []);
      // Update total for pagination if needed
      // setTotal(response.meta.total);
    } else {
      message.error(response.status.message);
    }
  }

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
    fetchData(values);
  };

  const getTreeData = async () => {
    const response = await getHierarchy();
    if (response.status.success) {
      setCategories(response.data || []);
    }
  };

  useEffectOnce(() => {
    getTreeData();
  });

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleRebuild = async () => {
    try {
      await storeRebuild();
      message.success(t('rebuildSuccess', 'Hierarchy rebuilt successfully'));
      getTreeData(); // Refresh tree data after rebuild
    } catch (error) {
      message.error(t('rebuildError', 'Failed to rebuild hierarchy'));
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    } else if (action === 'sort') {
      setShowSortModal(true);
    } else if (action === 'rebuild') {
      handleRebuild();
    }
  };

  const handleMoveNodeRoot = async (
    category_id: UniqueIdentifier,
    position: number,
  ) => {
    // Move to root level using target_id: 0 and direction: 'inside' as per Postman specification
    const payload: CategoryMoveRequest = {
      target_id: 0, // 0 for root level as per Postman specification
      direction: 'inside', // Move inside root (make it a root category)
    };
    const response = await moveCategory(category_id.toString(), payload);
    if (response.status.success) {
      message.success(t('moveSuccess') || 'Di chuyển danh mục thành công');
    } else {
      message.error(response.status.message);
    }
    getTreeData();
  };

  const handleMoveNode = async (
    activeId: UniqueIdentifier,
    overId: UniqueIdentifier,
    position: string,
  ) => {
    logger('[handleMoveNode]', { activeId, overId, position });

    // Determine direction based on position parameter
    let direction: CategoryMoveRequest['direction'] = 'inside';
    if (position === 'before') {
      direction = 'before';
    } else if (position === 'after') {
      direction = 'after';
    } else {
      direction = 'inside'; // Default to inside (as child)
    }

    // Move category using target_id and direction as per Postman specification
    const payload: CategoryMoveRequest = {
      target_id: Number(overId), // Target category ID (0 for root level)
      direction: direction,
    };

    logger('[moveCategory]', payload);
    const response = await moveCategory(activeId.toString(), payload);

    if (response.status.success) {
      message.success(t('moveSuccess') || 'Di chuyển danh mục thành công');
    } else {
      message.error(response.status.message);
    }
    getTreeData();
  };

  const handleUpdatePosition = async (categoryId: number, targetId: number) => {
    logger('[handleUpdatePosition]', { categoryId, targetId });

    // Find the target category to determine its position
    const findCategoryPosition = (
      categories: BlogCategory[],
      id: number,
    ): number | null => {
      for (let i = 0; i < categories.length; i++) {
        if (categories[i].id === id) {
          return i; // Return the index as position
        }
        if (categories[i].children) {
          const childPosition = findCategoryPosition(
            categories[i].children,
            id,
          );
          if (childPosition !== null) return childPosition;
        }
      }
      return null;
    };

    // Find parent and siblings of the target category
    const findParentAndSiblings = (
      categories: BlogCategory[],
      targetId: number,
      parentId: number | null = null,
    ): { parent: number | null; siblings: BlogCategory[] } | null => {
      for (const category of categories) {
        if (category.id === targetId) {
          // Found target, return parent's children
          const siblings = categories.filter((c) => c.parent_id === parentId);
          return { parent: parentId, siblings };
        }
        if (category.children && category.children.length > 0) {
          const result = findParentAndSiblings(
            category.children,
            targetId,
            category.id,
          );
          if (result) return result;
        }
      }
      return null;
    };

    const parentInfo = findParentAndSiblings(categories, targetId);
    if (!parentInfo) {
      message.error('Không tìm thấy vị trí danh mục');
      return;
    }

    // Find position of target category among its siblings
    const targetPosition = parentInfo.siblings.findIndex(
      (cat) => cat.id === targetId,
    );

    // The desired position is the position of the target category (move before it)
    const desiredPosition = targetPosition >= 0 ? targetPosition : 0;

    logger('[updateCategoryPositions]', {
      categoryId,
      targetId,
      desiredPosition,
    });

    // Update positions using bulk update API - updated to match API v3
    const payload: CategoryPositionUpdate[] = [
      {
        id: categoryId,
        sort_order: desiredPosition, // The 0-based position where we want to move the category
      },
    ];

    const response = await updateCategoryPositions(payload);

    if (response.status.success) {
      message.success(t('moveSuccess') || 'Di chuyển danh mục thành công');
    } else {
      message.error(response.status.message);
    }
    getTreeData();
  };
  return (
    <div>
      <div className="bg-gray flex justify-between p-4">
        <div className="flex items-center space-x-2">
          <BackButton destination="dashboard" />
          <div className="text-xl font-bold">{t('module')}</div>
        </div>
        <div className="gap-4 flex">
          <Button
            type="default"
            icon={<ReloadOutlined />}
            onClick={() => handleActions('rebuild', null)}
            loading={loading}
          >
            {t('btnRebuild', 'Rebuild')}
          </Button>
          <ButtonLink
            type="primary"
            icon={<PlusCircleOutlined />}
            to={`${MODULE}/create`}
          >
            {t('btnAdd')}
          </ButtonLink>
        </div>
      </div>
      <Search query={query} loading={loading} onChange={handleFilters}></Search>
      {treeItems.length > 0 && (
        <SortableTree
          collapsible
          indicator
          removable
          items={treeItems}
          onMoveNode={handleMoveNode}
          onMoveNodeRoot={handleMoveNodeRoot}
          onUpdatePosition={handleUpdatePosition}
        />
      )}
    </div>
  );
}
