import {
  ApiResponse,
  apiService,
  CursorResponse,
} from '../../services/api.service';
import {
  BlogCategory,
  BlogCategoryCreateRequest,
  BlogCategoryFilter,
  BlogCategoryResponse,
  BlogCategoryUpdateRequest,
  CategoryMoveRequest,
  CategoryMoveToRightRequest,
  CategoryPositionUpdateRequest,
} from './type';

// API v3 endpoint - matches Postman tests
const url = `/api/cms/v1/blog/categories`;

// Get categories list with cursor pagination - matches API v3
export async function getItems(
  params: BlogCategoryFilter = {},
): Promise<CursorResponse<BlogCategoryResponse[]>> {
  const response = await apiService.get<CursorResponse<BlogCategoryResponse[]>>(
    url,
    { params },
  );
  return response.data;
}

// Get complete category hierarchy - matches API v3
export async function getHierarchy(
  params?: any,
): Promise<ApiResponse<BlogCategoryResponse[]>> {
  const response = await apiService.get<ApiResponse<BlogCategoryResponse[]>>(
    `${url}/hierarchy`,
    { params },
  );
  return response.data;
}

// Get category by ID - matches API v3
export async function getItem(
  id: string,
): Promise<ApiResponse<BlogCategoryResponse>> {
  const response = await apiService.get<ApiResponse<BlogCategoryResponse>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function createItem(
  payload: BlogCategoryCreateRequest,
): Promise<ApiResponse<BlogCategoryResponse>> {
  const response = await apiService.post<ApiResponse<BlogCategoryResponse>>(
    url,
    payload,
  );
  return response.data;
}

export async function updateItem(
  id: string,
  payload: BlogCategoryUpdateRequest,
): Promise<ApiResponse<BlogCategoryResponse>> {
  const response = await apiService.put<ApiResponse<BlogCategoryResponse>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(
  id: string,
): Promise<ApiResponse<BlogCategoryResponse>> {
  const response = await apiService.delete<ApiResponse<BlogCategoryResponse>>(
    `${url}/${id}`,
  );
  return response.data;
}

// Move category to different parent and position - matches Postman specification
export async function moveCategory(
  categoryId: string,
  payload: CategoryMoveRequest,
): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${url}/${categoryId}/move`,
    payload,
  );
  return response.data;
}

// Move category to the right of a target category - matches Postman specification
export async function moveCategoryToRight(
  categoryId: string,
  payload: CategoryMoveToRightRequest,
): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${url}/${categoryId}/move-to-right`,
    payload,
  );
  return response.data;
}

// Update category positions in bulk - matches API v3
export async function updateCategoryPositions(
  payload: CategoryPositionUpdateRequest,
): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${url}/positions`,
    payload,
  );
  return response.data;
}

// Get category by slug - matches API v3
export async function getItemBySlug(
  slug: string,
): Promise<ApiResponse<BlogCategoryResponse>> {
  const response = await apiService.get<ApiResponse<BlogCategoryResponse>>(
    `${url}/slug/${slug}`,
  );
  return response.data;
}

// Get category ancestors (breadcrumb) - matches API v3
export async function getCategoryAncestors(
  id: string,
): Promise<ApiResponse<BlogCategoryResponse[]>> {
  const response = await apiService.get<ApiResponse<BlogCategoryResponse[]>>(
    `${url}/${id}/ancestors`,
  );
  return response.data;
}

// Get category breadcrumb - matches API v3
export async function getCategoryBreadcrumb(id: string): Promise<
  ApiResponse<
    Array<{
      id: number;
      name: string;
      slug: string;
    }>
  >
> {
  const response = await apiService.get<ApiResponse<any>>(
    `${url}/${id}/breadcrumb`,
  );
  return response.data;
}

// Get categories for select dropdown - matches API v3
export async function getOptions(params?: {
  show_tree?: boolean;
  include_disabled?: boolean;
  parent_id?: number;
}): Promise<
  ApiResponse<{
    options: Array<{
      value: number;
      label: string;
      indent?: number;
      level?: number;
      parent_id?: number;
      disabled?: boolean;
    }>;
    total: number;
  }>
> {
  const response = await apiService.get<ApiResponse<any>>(`${url}/select`, {
    params,
  });
  return response.data;
}

// Legacy compatibility functions - DEPRECATED
// Use new functions with BlogCategoryResponse types instead
export async function getSearch(
  params: any,
): Promise<CursorResponse<BlogCategoryResponse[]>> {
  return getItems({ ...params, search: params.keyword || params.search });
}

export async function getAll(): Promise<ApiResponse<BlogCategoryResponse[]>> {
  const response = await getItems({ limit: 1000 });
  return {
    status: response.status,
    data: response.data,
  } as ApiResponse<BlogCategoryResponse[]>;
}

// Keep old tree function for backward compatibility
export async function getTree(
  params?: any,
): Promise<ApiResponse<BlogCategoryResponse[]>> {
  return getHierarchy(params);
}

// Rebuild category hierarchy - matches API v3
export async function rebuildHierarchy(): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${url}/rebuild-tree`,
    {},
  );
  return response.data;
}

// Transform legacy BlogCategory to new BlogCategoryResponse for backward compatibility
export const transformLegacyResponse = (
  legacy: BlogCategory[],
): BlogCategoryResponse[] => {
  return legacy.map((category) => ({
    ...category,
    website_id: 0, // Default value for legacy data
    status: category.is_active ? 'active' : ('inactive' as any),
    children_count: category.children?.length || 0,
    id_path: null,
  }));
};
