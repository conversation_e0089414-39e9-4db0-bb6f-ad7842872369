import { <PERSON><PERSON>, Col, Collapse, Form, Input, message, Row, Switch } from 'antd';
import _ from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FormHeader } from '../../../components/form-header';
import { InputSlug } from '../../../components/input';
import ConsoleService from '../../../services/console.service';
import { createItem, getItem, updateItem } from '../api';
import { SelectBlogCategoryTree } from '../components';
import { MODULE } from '../config';
import useBlogStore from '../store';
import {
  BlogCategory,
  BlogCategoryResponse,
  BlogCategoryCreateRequest,
  BlogCategoryUpdateRequest,
} from '../type';

const FormItem = Form.Item;
const { TextArea } = Input;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const IndexForm: React.FC<IndexFormProps> = ({ onChange, id }) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  logger('id', id);
  const { loading } = useBlogStore();
  const [form] = Form.useForm();
  const [isNew, setIsNew] = useState<boolean>(false);
  const [item, setItem] = useState<BlogCategoryResponse>();
  const [formValues, setFormValues] = useState<any>();
  // Featured images removed - not supported in API v3
  const initForm = {
    // Only include fields supported by API v3
    name: '',
    slug: '',
    description: '',
  };

  const getItemData = useCallback(
    async (_id: string) => {
      const res = await getItem(_id);
      if (res.status.success) {
        setItem(res.data);
        form.setFieldsValue(res.data);

        // Image field handling removed - not supported in API v3
      } else {
        message.error(res.status.message);
      }
    },
    [form],
  );

  useEffect(() => {
    if (id === 'create') {
      setIsNew(true);
      form.resetFields();
      // Featured images reset removed - not needed in API v3
    } else if (id) {
      setIsNew(false);
      getItemData(id);
    }
  }, [id, form, getItemData, logger]);

  const onFinish = async (
    values: BlogCategoryCreateRequest | BlogCategoryUpdateRequest,
  ) => {
    try {
      let res;
      // API v3 only supports name, slug, and description
      const apiPayload = {
        name: values.name,
        slug: values.slug,
        description: values.description || undefined,
      };
      if (isNew) {
        res = await createItem(apiPayload as BlogCategoryCreateRequest);
        if (res.status.success) {
          message.success(t('addSuccess'));
        }
      } else {
        res = await updateItem(id!, apiPayload as BlogCategoryUpdateRequest);
        if (res.status.success) {
          message.success(t('updateSuccess'));
        }
      }
      if (!res.status.success) {
        message.error(res.status.message);
      } else {
        setItem(res.data);
        form.resetFields();
        onChange(true);
      }
    } catch (error) {
      logger('Error submitting form', error);
      message.error(
        _.get(error, 'response.data.message.0') || t('submitError'),
      );
    }
  };

  const handleValuesChange = (newValue: any, allValues: any) => {
    logger(newValue);
    logger(allValues);
    setFormValues(allValues);
  };

  // Media upload handler removed - not supported in API v3

  return (
    <div>
      <FormHeader
        title={isNew ? 'Thêm danh mục mới' : 'Chỉnh sửa danh mục'}
        backDestination="list"
      />
      <Form
        style={{ marginTop: 8 }}
        form={form}
        name="form"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        initialValues={initForm}
        onValuesChange={handleValuesChange}
      >
        <Row gutter={16}>
          <Col xs={24} lg={18}>
            <div className="form_content">
              <Row gutter={16}>
                {/* Parent category selection removed - not supported in API v3 create/update */}
                {/* Use move category API endpoint for hierarchy changes */}

                <Col xs={24} lg={24}>
                  <FormItem
                    label={t('name')}
                    name="name"
                    rules={[{ required: true, message: t('pleaseEnterData') }]}
                  >
                    <Input />
                  </FormItem>
                </Col>

                <Col xs={24} lg={24}>
                  <FormItem
                    label={t('slug')}
                    name="slug"
                    rules={[{ required: true, message: t('pleaseEnterData') }]}
                  >
                    <InputSlug
                      form={form}
                      name="slug"
                      initialValue={formValues?.name}
                      sourceField="name"
                      isEditMode={!isNew}
                    />
                  </FormItem>
                </Col>

                <Col xs={24} lg={24}>
                  <FormItem label={t('description')} name="description">
                    <TextArea rows={4} />
                  </FormItem>
                </Col>

                {/* Legacy fields removed - not supported in API v3 */}
                {/* image, position, is_active, is_featured fields deprecated */}
                {/* Use separate API endpoints for status management */}
              </Row>
            </div>
          </Col>
          {/* SEO meta fields removed - not supported in API v3 basic operations */}
          {/* meta_title, meta_description fields deprecated */}
        </Row>
        <div className="form_footer">
          <FormItem>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isNew ? t('btnAdd') : t('btnUpdate')}
            </Button>
          </FormItem>
        </div>
      </Form>
    </div>
  );
};

export default IndexForm;
