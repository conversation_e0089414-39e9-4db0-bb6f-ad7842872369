import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import ConsoleService from '../../services/console.service';
import {
  Chat,
  ChatListParams,
  CreateChatPayload,
  Message,
  MessageListParams,
  SendMessagePayload,
} from './type';

export const MODULE = 'chat';
export const MODULE_NAME = 'Chat Management';
export const MODULE_POPUP = false;
const logger = ConsoleService.register(MODULE);

const chatsUrl = `/api/cms/v1/chat/chats`;
const messagesUrl = `/api/cms/v1/chat/messages`;

// Chat API Functions
export async function getChats(
  params: ChatListParams,
): Promise<ApiResponsePaginationCursor<Chat[]>> {
  logger('getChats', params);
  const response = await apiService.get<ApiResponsePaginationCursor<Chat[]>>(
    chatsUrl,
    { params },
  );
  return response.data;
}

export async function getChat(id: string): Promise<ApiResponse<Chat>> {
  const response = await apiService.get<ApiResponse<Chat>>(`${chatsUrl}/${id}`);
  return response.data;
}

export async function createChat(
  payload: CreateChatPayload,
): Promise<ApiResponse<Chat>> {
  const response = await apiService.post<ApiResponse<Chat>>(chatsUrl, payload);
  return response.data;
}

export async function updateChat(
  id: string,
  payload: Partial<Chat>,
): Promise<ApiResponse<Chat>> {
  const response = await apiService.put<ApiResponse<Chat>>(
    `${chatsUrl}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteChat(id: string): Promise<ApiResponse<void>> {
  const response = await apiService.delete<ApiResponse<void>>(
    `${chatsUrl}/${id}`,
  );
  return response.data;
}

export async function archiveChat(id: string): Promise<ApiResponse<Chat>> {
  const response = await apiService.post<ApiResponse<Chat>>(
    `${chatsUrl}/${id}/archive`,
  );
  return response.data;
}

export async function leaveChat(id: string): Promise<ApiResponse<void>> {
  const response = await apiService.post<ApiResponse<void>>(
    `${chatsUrl}/${id}/leave`,
  );
  return response.data;
}

// Message API Functions
export async function getMessages(
  params: MessageListParams,
): Promise<ApiResponsePaginationCursor<Message[]>> {
  logger('getMessages', params);
  const response = await apiService.get<ApiResponsePaginationCursor<Message[]>>(
    messagesUrl,
    { params },
  );
  return response.data;
}

export async function sendMessage(
  payload: SendMessagePayload,
): Promise<ApiResponse<Message>> {
  const response = await apiService.post<ApiResponse<Message>>(
    messagesUrl,
    payload,
  );
  return response.data;
}

export async function markMessageAsRead(
  messageId: string,
): Promise<ApiResponse<void>> {
  const response = await apiService.post<ApiResponse<void>>(
    `${messagesUrl}/${messageId}/read`,
  );
  return response.data;
}

export async function markChatAsRead(
  chatId: string,
): Promise<ApiResponse<void>> {
  const response = await apiService.post<ApiResponse<void>>(
    `${chatsUrl}/${chatId}/read`,
  );
  return response.data;
}

export async function deleteMessage(
  messageId: string,
): Promise<ApiResponse<void>> {
  const response = await apiService.delete<ApiResponse<void>>(
    `${messagesUrl}/${messageId}`,
  );
  return response.data;
}

// Search functions
export async function searchChats(params: {
  search: string;
}): Promise<ApiResponse<Chat[]>> {
  const response = await apiService.get<ApiResponse<Chat[]>>(
    `${chatsUrl}/search`,
    { params },
  );
  return response.data;
}

export async function searchMessages(params: {
  chat_id: number;
  search: string;
}): Promise<ApiResponse<Message[]>> {
  const response = await apiService.get<ApiResponse<Message[]>>(
    `${messagesUrl}/search`,
    { params },
  );
  return response.data;
}
