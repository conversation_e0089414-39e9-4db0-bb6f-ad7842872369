import { message } from 'antd';
import { create } from 'zustand';
import { setAuthToken } from '../../services/api.service';
import tokenService from '../../services/token.service';
import { OnboardingService } from '../onboarding/components';
import { apiLogin } from './api';

interface LoginCredentials {
  email: string;
  password: string;
  otp?: string;
}

interface LoginResult {
  success: boolean;
  requiresOtp?: boolean;
  requiresOtpVerify?: boolean;
  requiresTenantSelection?: boolean;
  requiresOnboarding?: boolean;
  loginToken?: string;
  userId?: string | number;
  tenants?: any[];
}

interface AuthState {
  isLoggedIn: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginCredentials) => Promise<LoginResult>;
  logout: () => void;
  setIsLoggedIn: (isLoggedIn: boolean) => void;
  clearError: () => void;
}

export const useAuthStore = create<AuthState>((set) => ({
  isLoggedIn: !!tokenService.getToken(),
  isLoading: false,
  error: null,

  login: async (credentials: LoginCredentials) => {
    set({ isLoading: true, error: null });

    try {
      const response = await apiLogin(credentials);

      // Check if the response status code is not 200 (success)
      if (response.status.code !== 200) {
        // Extract and display the error message from status.message
        const errorMessage = response.status.message || 'Đăng nhập thất bại';

        // Handle specific error cases
        if (response.status.code === 403) {
          // Handle email not verified case
          if (response.status.error_code === 'EMAIL_NOT_VERIFIED') {
            message.error(errorMessage); // Display Vietnamese message like "Email chưa được xác thực"
          } else {
            message.error(errorMessage);
          }
        } else {
          // Handle other error status codes
          message.error(errorMessage);
        }

        set({
          isLoading: false,
          error: errorMessage,
          isLoggedIn: false,
        });
        return { success: false };
      }

      // Success case - status code is 200
      if (response.status.success && response.data) {
        // Handle different response scenarios
        if (response.data.status === 'require_otp') {
          // OTP setup required
          set({ isLoading: false, error: null });
          return { success: false, requiresOtp: true };
        } else if (response.data.status === 'require_otp_verify') {
          // OTP verification required
          set({ isLoading: false, error: null });
          return {
            success: false,
            requiresOtpVerify: true,
            loginToken: response.data.login_token,
          };
        } else if (response.data.access_token) {
          // Successful login with tokens
          tokenService.saveToken(response.data.access_token);
          tokenService.saveRefreshToken(response.data.refresh_token);

          // Set expiry dates if provided
          if (response.data.access_token_expires_in) {
            const expiryTime =
              Date.now() + response.data.access_token_expires_in * 1000;
            tokenService.saveExpiryDate(expiryTime);
          }
          if (response.data.refresh_token_expires_in) {
            const refreshExpiryTime =
              Date.now() + response.data.refresh_token_expires_in * 1000;
            tokenService.saveRefreshExpiryDate(refreshExpiryTime);
          }

          // Set auth token for future requests
          setAuthToken(response.data.access_token);

          set({
            isLoggedIn: true,
            isLoading: false,
            error: null,
          });

          message.success('Đăng nhập thành công!');

          // Store user data if available
          if (response.data.user) {
            localStorage.setItem('userId', response.data.user.id.toString());
            if (response.data.user.email) {
              localStorage.setItem('userEmail', response.data.user.email);
            }
          }

          // Store email from credentials if user data not available
          if (credentials.email) {
            localStorage.setItem('userEmail', credentials.email);
          }

          // Store session ID if available
          if (response.data.session_id) {
            localStorage.setItem(
              'sessionId',
              response.data.session_id.toString(),
            );
          }

          // Check if user requires onboarding
          console.log(
            '[AuthStore] Checking requires_onboarding:',
            response.data.requires_onboarding,
          );
          if (response.data.requires_onboarding === true) {
            console.log(
              '[AuthStore] User requires onboarding, returning requiresOnboarding: true',
            );
            // Use OnboardingService to manage onboarding status
            OnboardingService.setRequiresOnboarding(true);
            return { success: true, requiresOnboarding: true };
          } else {
            // Clear onboarding flag if not required
            OnboardingService.setRequiresOnboarding(false);
          }

          return { success: true };
        }
      }

      // Fallback error case
      const fallbackError = 'Đăng nhập thất bại. Vui lòng thử lại.';
      message.error(fallbackError);
      set({
        isLoading: false,
        error: fallbackError,
        isLoggedIn: false,
      });
      return { success: false };
    } catch (error: any) {
      // Handle network errors or other exceptions
      let errorMessage = 'Có lỗi xảy ra khi đăng nhập. Vui lòng thử lại.';

      // Try to extract error message from response
      if (error.response?.data?.status?.message) {
        errorMessage = error.response.data.status.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      message.error(errorMessage);
      set({
        isLoading: false,
        error: errorMessage,
        isLoggedIn: false,
      });
      return { success: false };
    }
  },

  logout: () => {
    set({ isLoggedIn: false, error: null });
    tokenService.removeAll();
    // Remove tenant information
    localStorage.removeItem('selectedTenantId');
    localStorage.removeItem('selectedTenantName');
    localStorage.removeItem('selectedTenantRole');
    // Clear all onboarding data
    OnboardingService.clearAll();
    // Force page reload to clear all state and redirect to login
    window.location.href = '/auth/login';
  },

  setIsLoggedIn: (isLoggedIn: boolean) => {
    set({ isLoggedIn });
  },

  clearError: () => {
    set({ error: null });
  },
}));
