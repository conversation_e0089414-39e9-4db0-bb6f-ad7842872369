import { apiService } from '../../services/api.service';
import { Tenant } from './type';

interface LoginRequest {
  email: string;
  password: string;
  otp?: string;
}

interface RegisterRequest {
  email: string;
  password: string;
  first_name?: string;
  last_name?: string;
  username?: string;
  invitation_token?: string;
}

interface StatusResponse {
  code: number;
  message: string;
  success: boolean;
  error_code?: string;
  path: string;
  timestamp: string;
  details?: Array<{
    message: string;
  }>;
}

interface LoginResponseData {
  user_id: string;
  username: string;
  access_token: string;
  refresh_token: string;
  access_token_expires_in: number;
  refresh_token_expires_in: number;
  status?: string;
  login_token?: string;
  requires_onboarding?: boolean;
  session_id?: number;
  user?: {
    id: number;
    email: string;
    first_name?: string;
    last_name?: string;
  };
}

interface VerifyEmailResponseData {
  status: string;
  email_verified: boolean;
  user_id: number;
  access_token?: string;
  refresh_token?: string;
  user?: {
    id: number;
    email: string;
    first_name?: string;
    last_name?: string;
  };
}

export interface LoginResponse {
  status: StatusResponse;
  data: LoginResponseData;
}

export interface VerifyEmailResponse {
  status: StatusResponse;
  data: VerifyEmailResponseData;
}

export const apiLogin = async (
  payload: LoginRequest,
): Promise<LoginResponse> => {
  const response = await apiService.post<any>(
    '/api/cms/v1/auth/login',
    payload,
  );
  return response.data;
};

interface RegisterResponse {
  email_verification_sent: boolean;
  message: string;
  requires_email_verification: boolean;
}

export interface RegisterApiResponse {
  status: StatusResponse;
  data: RegisterResponse;
}

export const apiRegister = async (
  payload: RegisterRequest,
): Promise<RegisterApiResponse> => {
  const response = await apiService.post<any>(
    '/api/cms/v1/auth/register',
    payload,
  );
  return response.data;
};

export const otpConfig = async (
  payload: LoginRequest,
): Promise<LoginResponse> => {
  const response = await apiService.post<any>(
    '/api/cms/v1/auth/otp-config',
    payload,
  );
  return response.data;
};

export const otpConfirm = async (
  payload: LoginRequest,
): Promise<LoginResponse> => {
  const response = await apiService.post<any>(
    '/api/cms/v1/auth/otp-config-confirm',
    payload,
  );
  return response.data;
};

interface VerifyOtpRequest {
  login_token: string;
  token: string;
}

export const verifyOtp = async (
  payload: VerifyOtpRequest,
): Promise<LoginResponse> => {
  try {
    const response = await apiService.post<any>(
      '/api/cms/v1/auth/verify-otp',
      payload,
    );
    return response.data;
  } catch (error: any) {
    if (error.response.data.status.code === 400) {
      return error.response.data;
    } else {
      throw error;
    }
  }
};

interface RefreshTokenRequest {
  refresh_token: string;
}

export const refreshToken = async (
  refreshToken: string,
): Promise<LoginResponse> => {
  try {
    const response = await apiService.post<any>(
      '/api/cms/v1/auth/refresh-token',
      {
        refresh_token: refreshToken,
      },
    );
    return response.data;
  } catch (error: any) {
    if (error.response && error.response.data) {
      return error.response.data;
    }
    throw error;
  }
};

export const verifyEmail = async (
  token: string,
): Promise<VerifyEmailResponse> => {
  const response = await apiService.post<any>('/api/cms/v1/auth/verify-email', {
    token,
  });
  return response.data;
};

export const resendVerification = async (
  email: string,
): Promise<LoginResponse> => {
  const response = await apiService.post<any>(
    '/api/cms/v1/auth/resend-verification',
    { email },
  );
  return response.data;
};

export const getUserTenants = async (
  cursor?: string,
  limit?: number,
): Promise<{
  status: {
    code: number;
    message: string;
    success: boolean;
    error_code?: string;
    path: string;
    timestamp: string;
    details?: Array<{ message: string }>;
  };
  data: Tenant[];
}> => {
  const params = new URLSearchParams();
  if (cursor) params.append('cursor', cursor);
  if (limit) params.append('limit', limit.toString());

  const queryString = params.toString();
  const url = `/api/cms/v1/tenants/me/list${queryString ? `?${queryString}` : ''}`;

  const response = await apiService.get<any>(url);
  return response.data;
};

interface ForgotPasswordRequest {
  email: string;
}

interface ForgotPasswordResponse {
  status: string;
}

export interface ForgotPasswordApiResponse {
  status: StatusResponse;
  data: ForgotPasswordResponse;
}

export const forgotPassword = async (
  email: string,
): Promise<ForgotPasswordApiResponse> => {
  const response = await apiService.post<any>(
    '/api/cms/v1/auth/forgot-password',
    { email },
  );
  return response.data;
};

interface ResetPasswordRequest {
  token: string;
  new_password: string;
  confirm_password: string;
}

interface ResetPasswordResponse {
  status: string;
}

export interface ResetPasswordApiResponse {
  status: StatusResponse;
  data: ResetPasswordResponse;
}

export const resetPassword = async (
  payload: ResetPasswordRequest,
): Promise<ResetPasswordApiResponse> => {
  const response = await apiService.post<any>(
    '/api/cms/v1/auth/reset-password',
    payload,
  );
  return response.data;
};

// 2FA API Endpoints
interface TwoFactorLoginRequest {
  login_token: string;
  code: string;
}

interface TwoFactorLoginResponse {
  user_id: string;
  username: string;
  access_token: string;
  refresh_token: string;
  access_token_expires_in: number;
  refresh_token_expires_in: number;
  onboarding_status?: 'not_started' | 'in_progress' | 'completed';
  tenant_id?: string | number;
}

interface TwoFactorEnableResponse {
  qr_code?: string;
  secret?: string;
  backup_codes?: string[];
  enabled: boolean;
}

interface TwoFactorDisableRequest {
  code: string;
  password?: string;
}

interface TwoFactorVerifyRequest {
  code: string;
}

interface TwoFactorVerifyResponse {
  valid: boolean;
  message: string;
}

interface TwoFactorStatusResponse {
  enabled: boolean;
  backup_codes_remaining?: number;
}

export interface TwoFactorApiResponse<T> {
  status: StatusResponse;
  data: T;
}

export const twoFactorApi = {
  // Complete login with 2FA
  completeLogin: async (
    payload: TwoFactorLoginRequest,
  ): Promise<TwoFactorApiResponse<TwoFactorLoginResponse>> => {
    const response = await apiService.post<any>(
      '/api/cms/v1/2fa/complete-login',
      payload,
    );
    return response.data;
  },

  // Enable 2FA for user
  enable: async (): Promise<TwoFactorApiResponse<TwoFactorEnableResponse>> => {
    const response = await apiService.post<any>('/api/cms/v1/2fa/enable');
    return response.data;
  },

  // Disable 2FA for user
  disable: async (
    payload: TwoFactorDisableRequest,
  ): Promise<TwoFactorApiResponse<{ disabled: boolean }>> => {
    const response = await apiService.post<any>(
      '/api/cms/v1/2fa/disable',
      payload,
    );
    return response.data;
  },

  // Verify 2FA code
  verify: async (
    payload: TwoFactorVerifyRequest,
  ): Promise<TwoFactorApiResponse<TwoFactorVerifyResponse>> => {
    const response = await apiService.post<any>(
      '/api/cms/v1/2fa/verify',
      payload,
    );
    return response.data;
  },

  // Get 2FA status
  status: async (): Promise<TwoFactorApiResponse<TwoFactorStatusResponse>> => {
    const response = await apiService.get<any>('/api/cms/v1/2fa/status');
    return response.data;
  },
};
