{"username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "email": "Email", "fullName": "Full Name", "firstName": "First Name", "lastName": "Last Name", "invitationToken": "Invitation Token", "submit": "<PERSON><PERSON>", "register": "Register", "login": "<PERSON><PERSON>", "loginTitle": "<PERSON><PERSON>", "registerTitle": "Register", "loginWithEmail": "Continue with <PERSON>ail", "registerWithEmail": "Register with Email", "loginWithGoogle": "Continue with Google", "loginWithFacebook": "Continue with Facebook", "loginWithApple": "Continue with Apple", "loginWithSSO": "Continue with SSO", "haveAccount": "Already have an account?", "noAccount": "Don't have an account?", "or": "or", "termsAgreement": "By registering, you agree to our Terms of Service and Privacy Policy", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "passwordStrength": "Password strength", "strong": "Strong", "weak": "Weak", "loggingIn": "Logging in...", "loginError": "<PERSON><PERSON>", "registerError": "Registration Error", "backToLogin": "Back to Login", "confirm": "Confirm", "goBack": "Go Back", "rememberPassword": "Remember your password?", "validation": {"emailRequired": "Please enter your email!", "emailInvalid": "Invalid email format!", "passwordRequired": "Please enter your password!", "passwordMinLength": "Password must be at least 8 characters!", "usernameRequired": "Please enter your username!", "usernameMinLength": "<PERSON><PERSON><PERSON> must be at least 3 characters!", "fullNameRequired": "Please enter your full name!", "firstNameRequired": "Please enter your first name!", "lastNameRequired": "Please enter your last name!", "invitationTokenTooltip": "Optional: Enter invitation token if you have one", "confirmPasswordRequired": "Please confirm your password!", "passwordMismatch": "Passwords do not match!", "otpRequired": "Please enter OTP code!", "otpLength": "OTP must be 6 digits!", "otpDigitsOnly": "OTP must contain only digits!"}, "messages": {"registerSuccess": "Registration successful!", "registerSuccessWithVerification": "Registration successful! Please check your email to verify your account.", "registerFailed": "Registration failed. Please try again later.", "passwordMismatch": "Passwords do not match!", "loginSuccess": "Login successful!", "loginFailed": "<PERSON><PERSON> failed. Please check your credentials.", "otpVerifySuccess": "OTP verification successful!", "otpVerifyFailed": "OTP verification failed!", "otpVerifyError": "An error occurred during OTP verification", "loadTenantsError": "Failed to load tenants. Please try again later.", "apiError": "An unexpected error occurred. Please try again later."}, "errorCodes": {"invalid_credentials": "Invalid login credentials. Please check your email and password.", "user_already_exists": "An account with this email already exists. Please try logging in instead.", "invalid_token": "Invalid or malformed token. Please try again.", "expired_token": "<PERSON><PERSON> has expired. Please request a new one.", "user_not_found": "User account not found. Please check your email or register a new account.", "token_not_found": "Token not found. Please request a new verification token.", "invalid_password": "Invalid password format. Please ensure your password meets the requirements.", "email_not_verified": "Email address has not been verified. Please check your email for the verification link.", "account_locked": "Account has been temporarily locked due to multiple failed login attempts.", "weak_password": "Password is too weak. Please use a stronger password.", "invalid_email_format": "Invalid email address format. Please enter a valid email.", "username_taken": "This username is already taken. Please choose a different username."}, "placeholders": {"invitationTokenOptional": "Enter invitation token (optional)"}, "otp": {"title": "OTP Verification", "description": "Please enter the OTP code from your authenticator app", "placeholder": "Enter 6-digit code", "setup": "OTP Setup", "setupDescription": "Set up two-factor authentication for your account", "scanQR": "Scan this QR code with your authenticator app", "enterCode": "Enter the code from your app to verify setup", "setupSuccess": "OTP setup successful!", "setupFailed": "OTP setup failed!"}, "emailVerification": {"verifying": "Verifying email...", "pleaseWait": "Please wait a moment.", "success": "Email verification successful!", "failed": "Email verification failed", "invalidToken": "Invalid verification token.", "loginNow": "Login now", "resendVerification": "Resend verification email", "resendTitle": "Resend Verification", "resendDescription": "Enter your email to receive a new verification link", "resendSuccess": "Verification email sent successfully!", "resendFailed": "Failed to send verification email. Please try again later.", "resendButton": "Send Verification Email", "checkEmail": "Please check your email for the verification link.", "checkEmailTitle": "Check Your Email", "checkEmailMessage": "We've sent a verification link to your email address. Please check your inbox and click the link to verify your account.", "resendEmail": "<PERSON><PERSON><PERSON>", "checkSpamFolder": "💡 If you don't see the email, please check your spam or junk folder."}, "tenant_selection": {"title": "Select Tenant", "subtitle": "Please select a tenant to continue", "select_button": "Select", "logout_button": "Logout", "role": "Role", "domain": "Domain", "primary": "Primary"}, "forgotPassword": {"title": "Forgot Password", "description": "Enter your email address and we'll send you a link to reset your password.", "emailPlaceholder": "Enter your email address", "sendResetLink": "Send Reset Link", "emailSentSuccess": "Password reset email sent successfully!", "checkEmailTitle": "Check Your Email", "checkEmailMessage": "We've sent a password reset link to your email address. Please check your inbox and click the link to reset your password.", "checkSpamFolder": "💡 If you don't see the email, please check your spam or junk folder.", "didNotReceive": "Didn't receive the email?", "tryAgain": "Try again", "forgotPasswordLink": "Forgot password?", "failed": "Failed to send password reset email", "error": "Forgot Password Error"}, "resetPassword": {"title": "Reset Password", "description": "Enter your new password below", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "newPasswordPlaceholder": "Enter new password", "confirmPasswordPlaceholder": "Confirm new password", "resetButton": "Reset Password", "success": "Password reset successfully!", "failed": "Failed to reset password", "error": "Reset Password Error", "invalidToken": "Invalid or expired reset token"}}