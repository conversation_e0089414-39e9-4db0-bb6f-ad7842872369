import { TFunction } from 'i18next';

/**
 * Utility function to get translated error message from API response
 * @param errorResponse - The error response from API
 * @param t - Translation function
 * @returns Translated error message
 */
export const getTranslatedErrorMessage = (
  errorResponse: {
    message: string;
    details?: Array<{ message: string }>;
    error_code?: string;
  },
  t: TFunction,
): string => {
  let errorMessage = errorResponse.message;

  // Check if there are details and show them
  if (errorResponse.details && errorResponse.details.length > 0) {
    const detailMessages = errorResponse.details
      .map((detail: any) => {
        // Check if the detail message is an error code we have translations for
        const errorCodeKey = `errorCodes.${detail.message}`;
        if (t(errorCodeKey) !== errorCodeKey) {
          return t(errorCodeKey);
        }
        return detail.message;
      })
      .filter(Boolean);

    if (detailMessages.length > 0) {
      errorMessage = detailMessages.join(', ');
    }
  }

  // Also check the main error_code field
  if (errorResponse.error_code) {
    const errorCodeKey = `errorCodes.${errorResponse.error_code.toLowerCase()}`;
    if (t(errorCodeKey) !== errorCodeKey) {
      errorMessage = t(errorCodeKey);
    }
  }

  return errorMessage;
};

/**
 * Utility function to handle API errors consistently across forms
 * @param error - The caught error
 * @param t - Translation function
 * @param fallbackMessage - Fallback message key if error parsing fails
 * @returns Error message to display
 */
export const handleApiError = (
  error: any,
  t: TFunction,
  fallbackMessage: string = 'messages.apiError',
): string => {
  // Handle error response from API
  if (error.response && error.response.data && error.response.data.status) {
    return getTranslatedErrorMessage(error.response.data.status, t);
  }

  // Handle direct API response with error
  if (error.status && !error.status.success) {
    return getTranslatedErrorMessage(error.status, t);
  }

  // Default fallback
  return t(fallbackMessage);
};
