export interface Login {
  email: string;
  password: string;
}

// export enum AuthErrorCode {
//   OTP_NOT_CONFIG = 10001,
//   OTP_CONFIG_NOT_FOUND = 10002,
// }

export interface Tenant {
  id: number;
  name: string;
  domain: string;
  logo_url?: string;
  status: string;
  membership_status: string;
  is_primary: boolean;
  joined_at: string;
}

export interface MyTenantsResponse {
  tenants: Tenant[];
  meta: {
    total: number;
    has_more: boolean;
    next_cursor?: string;
  };
}
