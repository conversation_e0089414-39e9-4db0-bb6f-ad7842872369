import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ge,
  <PERSON><PERSON>,
  Card,
  Divider,
  List,
  Space,
  Spin,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { getUserTenants } from './api';
import { useAuthStore } from './auth';
import useLoginStore from './store';
import { Tenant } from './type';

const { Title, Text } = Typography;

const TenantSelectionPage: React.FC = () => {
  const { t } = useTranslation(['auth', 'common']);
  const navigate = useNavigate();
  const { tenants, setTenants, setSelectedTenant } = useLoginStore();
  const { logout } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadTenants();
  }, []);

  const loadTenants = async () => {
    try {
      setLoading(true);
      setError(null);
      const res = await getUserTenants();
      if (res.status.success && res.data) {
        setTenants(res.data);
      } else {
        setError(res.status.message || t('messages.loadTenantsError'));
      }
    } catch (error: any) {
      console.error('Error loading tenants:', error);
      setError(error.message || t('messages.loadTenantsError'));
    } finally {
      setLoading(false);
    }
  };
  const handleSelectTenant = (tenant: Tenant) => {
    setSelectedTenant(tenant);
    // Sau khi chọn tenant, chuyển hướng đến trang dashboard với id
    navigate(`/dashboard/${tenant.id}`);
  };

  const handleLogout = () => {
    logout();
    // No need to navigate manually since logout function now handles redirect
  };

  // Show loading state
  if (loading) {
    return (
      <div className="max-w-[600px] mx-auto p-5">
        <Card bordered={false} className="shadow-md text-center">
          <Spin size="large" />
          <div className="mt-4">
            <Typography.Text>{t('messages.loadingTenants')}</Typography.Text>
          </div>
        </Card>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="max-w-[600px] mx-auto p-5">
        <Card bordered={false} className="shadow-md">
          <Alert
            message={t('messages.loadTenantsError')}
            description={error}
            type="error"
            showIcon
            action={
              <Space>
                <Button size="small" onClick={() => window.location.reload()}>
                  {t('common.retry')}
                </Button>
                <Button size="small" onClick={handleLogout}>
                  {t('tenant_selection.logout_button')}
                </Button>
              </Space>
            }
          />
        </Card>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'error';
      default:
        return 'warning';
    }
  };

  // Nếu không có tenants hoặc đã chọn tenant, chuyển hướng đến trang chính
  // React.useEffect(() => {
  //   if (!tenants || tenants.length === 0) {
  //     navigate('/');
  //   }

  //   if (tenantService.hasTenantSelected()) {
  //     navigate('/');
  //   }
  // }, [tenants, navigate]);

  return (
    <div className="max-w-[600px] mx-auto p-5">
      <Card bordered={false} className="shadow-md">
        <Title level={4}>{t('tenant_selection.title')}</Title>
        <Text type="secondary">{t('tenant_selection.subtitle')}</Text>

        <Divider />

        <List
          itemLayout="horizontal"
          dataSource={tenants}
          renderItem={(tenant) => (
            <List.Item
              key={tenant.id}
              actions={[
                <Button
                  key={`select-tenant-${tenant.id}`}
                  type="primary"
                  onClick={() => handleSelectTenant(tenant)}
                >
                  {t('tenant_selection.select_button')}
                </Button>,
              ]}
            >
              <List.Item.Meta
                avatar={
                  tenant.logo_url ? (
                    <Avatar src={tenant.logo_url} size="large" />
                  ) : (
                    <Avatar style={{ backgroundColor: '#1890ff' }} size="large">
                      {tenant.name?.charAt(0).toUpperCase() || '?'}
                    </Avatar>
                  )
                }
                title={
                  <Space>
                    {tenant.name}
                    <Badge
                      status={getStatusColor(tenant.membership_status) as any}
                      text={tenant.membership_status}
                    />
                    {tenant.is_primary && (
                      <Badge
                        color="gold"
                        text={t('tenant_selection.primary')}
                      />
                    )}
                  </Space>
                }
                description={
                  <Text type="secondary">
                    {t('tenant_selection.domain')}: {tenant.domain}
                  </Text>
                }
              />
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

export default TenantSelectionPage;
