import { <PERSON><PERSON>, <PERSON>, Result, Spin, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import * as api from '../api';
import { handleApiError } from '../utils/errorHandler';

const { Title, Text } = Typography;

const VerifyEmail: React.FC = () => {
  const { t } = useTranslation('auth');
  const [isVerifying, setIsVerifying] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [message, setMessage] = useState('');
  const navigate = useNavigate();
  const location = useLocation();
  const [hasVerified, setHasVerified] = useState(false);

  useEffect(() => {
    // Prevent multiple API calls
    if (hasVerified) return;

    const verifyEmail = async () => {
      try {
        // L<PERSON>y token từ URL query parameter
        const query = new URLSearchParams(location.search);
        const token = query.get('token');

        if (!token) {
          setMessage(t('emailVerification.invalidToken'));
          setIsSuccess(false);
          setIsVerifying(false);
          return;
        }

        // Mark as verified to prevent re-runs
        setHasVerified(true);

        // Call email verification API
        const response = await api.verifyEmail(token);
        console.log(response);
        if (response.status.success && response.data) {
          setIsSuccess(true);
          setMessage(response.status.message || 'Email verified successfully');

          // Always redirect to login after email verification
          setTimeout(() => {
            navigate('/auth/login');
          }, 3000);
        } else {
          setIsSuccess(false);
          setMessage(response.status.message || 'Email verification failed');
        }
      } catch (error: any) {
        console.error(error);
        setIsSuccess(false);
        // Use utility function to handle API error
        const errorMessage = handleApiError(
          error,
          t,
          'emailVerification.failed',
        );
        setMessage(errorMessage);
      } finally {
        setIsVerifying(false);
      }
    };

    verifyEmail();
  }, [location.search, navigate, t, hasVerified]);

  const goToLogin = () => {
    navigate('/auth/login');
  };

  const goToResendVerification = () => {
    navigate('/auth/resend-verification');
  };

  if (isVerifying) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Card style={{ width: 400, textAlign: 'center' }}>
          <Spin size="large" />
          <Title level={4} style={{ marginTop: 20 }}>
            Đang xác thực email...
          </Title>
          <Text>Vui lòng đợi trong giây lát</Text>
        </Card>
      </div>
    );
  }

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
      }}
    >
      <Card style={{ width: 500, textAlign: 'center' }}>
        {isSuccess ? (
          <Result
            status="success"
            title="Email đã được xác thực thành công!"
            subTitle={message}
            extra={[
              <Button type="primary" key="login" onClick={goToLogin}>
                Đăng nhập ngay
              </Button>,
            ]}
          />
        ) : (
          <Result
            status="error"
            title="Xác thực email thất bại"
            subTitle={message}
            extra={[
              <Button type="primary" key="login" onClick={goToLogin}>
                Đăng nhập
              </Button>,
              <Button key="resend" onClick={goToResendVerification}>
                Gửi lại email xác thực
              </Button>,
            ]}
          />
        )}
      </Card>
    </div>
  );
};

export default VerifyEmail;
