import { Alert, Button, Form, Input, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { LockOutlined } from '@ant-design/icons';
import ConsoleService from '../../../services/console.service';
import { resetPassword } from '../api';
import {
  getTranslatedErrorMessage,
  handleApiError,
} from '../utils/errorHandler';

interface ResetPasswordFormProps {
  onChange: (action: string) => void;
}

const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({ onChange }) => {
  const { t } = useTranslation('auth');
  const logger = ConsoleService.register('reset-password-form');
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [token, setToken] = useState<string>('');

  useEffect(() => {
    // Get token from URL query parameter
    const query = new URLSearchParams(location.search);
    const resetToken = query.get('token');

    if (!resetToken) {
      setError(t('resetPassword.invalidToken'));
    } else {
      setToken(resetToken);
    }
  }, [location.search, t]);

  const onFinish = async (values: any) => {
    if (!token) {
      setError(t('resetPassword.invalidToken'));
      return;
    }

    if (values.new_password !== values.confirm_password) {
      message.error(t('messages.passwordMismatch'));
      return;
    }

    setError(null);
    setLoading(true);

    try {
      const response = await resetPassword({
        token,
        new_password: values.new_password,
        confirm_password: values.confirm_password,
      });

      if (response.status.success) {
        message.success(t('resetPassword.success'));
        // Navigate to login after successful reset
        setTimeout(() => {
          onChange('login');
        }, 2000);
      } else {
        const errorMessage = getTranslatedErrorMessage(response.status, t);
        setError(errorMessage);
      }
    } catch (error: any) {
      logger('Reset password error:', error);
      const errorMessage = handleApiError(error, t, 'resetPassword.failed');
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow min-w-96">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold">{t('resetPassword.title')}</h2>
        <p className="text-gray-500 mt-2">{t('resetPassword.description')}</p>
      </div>

      {error && (
        <Alert
          message={t('resetPassword.error')}
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
          className="mb-4"
        />
      )}

      <Form
        name="reset-password"
        layout="vertical"
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
      >
        <Form.Item
          label={t('resetPassword.newPassword')}
          name="new_password"
          rules={[
            { required: true, message: t('validation.passwordRequired') },
            { min: 8, message: t('validation.passwordMinLength') },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined className="text-gray-400" />}
            placeholder={t('resetPassword.newPasswordPlaceholder')}
            size="large"
          />
        </Form.Item>

        <Form.Item
          label={t('resetPassword.confirmPassword')}
          name="confirm_password"
          rules={[
            {
              required: true,
              message: t('validation.confirmPasswordRequired'),
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('new_password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error(t('validation.passwordMismatch')),
                );
              },
            }),
          ]}
        >
          <Input.Password
            prefix={<LockOutlined className="text-gray-400" />}
            placeholder={t('resetPassword.confirmPasswordPlaceholder')}
            size="large"
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            className="w-full"
            loading={loading}
            size="large"
          >
            {t('resetPassword.resetButton')}
          </Button>
        </Form.Item>

        <div className="text-center">
          <a
            href="#"
            onClick={() => onChange('login')}
            className="text-blue-500"
          >
            {t('backToLogin')}
          </a>
        </div>
      </Form>
    </div>
  );
};

export default ResetPasswordForm;
