import { Button, Card, Result, Typography } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { MailOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface ForgotPasswordSuccessProps {
  email?: string;
  onChange: (action: string) => void;
}

const ForgotPasswordSuccess: React.FC<ForgotPasswordSuccessProps> = ({
  email,
  onChange,
}) => {
  const { t } = useTranslation('auth');

  return (
    <div className="bg-white p-6 rounded-lg shadow min-w-96">
      <Card style={{ border: 'none', textAlign: 'center' }}>
        <Result
          icon={<MailOutlined style={{ color: '#1890ff', fontSize: '72px' }} />}
          title={
            <Title level={3} style={{ color: '#1890ff', marginBottom: 16 }}>
              {t('forgotPassword.checkEmailTitle')}
            </Title>
          }
          subTitle={
            <div style={{ marginBottom: 24 }}>
              <Text style={{ fontSize: '16px', color: '#666' }}>
                {t('forgotPassword.checkEmailMessage')}
              </Text>
              {email && (
                <div style={{ marginTop: 8 }}>
                  <Text strong style={{ color: '#1890ff' }}>
                    {email}
                  </Text>
                </div>
              )}
            </div>
          }
          extra={[
            <Button key="login" onClick={() => onChange('login')}>
              {t('backToLogin')}
            </Button>,
          ]}
        />

        <div
          style={{
            marginTop: 24,
            padding: '16px',
            backgroundColor: '#f0f2f5',
            borderRadius: '8px',
          }}
        >
          <Text style={{ fontSize: '14px', color: '#666' }}>
            {t('forgotPassword.checkSpamFolder')}
          </Text>
        </div>

        <div style={{ marginTop: 16 }}>
          <Text style={{ fontSize: '14px', color: '#666' }}>
            {t('forgotPassword.didNotReceive')}{' '}
            <a
              href="#"
              onClick={() => onChange('forgot-password')}
              className="text-blue-500"
            >
              {t('forgotPassword.tryAgain')}
            </a>
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default ForgotPasswordSuccess;
