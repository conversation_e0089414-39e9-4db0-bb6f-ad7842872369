import { But<PERSON>, <PERSON>, Result, Typography } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { MailOutlined, ReloadOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface EmailVerificationReminderProps {
  email?: string;
  onChange: (action: string) => void;
}

const EmailVerificationReminder: React.FC<EmailVerificationReminderProps> = ({
  email,
  onChange,
}) => {
  const { t } = useTranslation('auth');

  const handleResendVerification = () => {
    onChange('resend-verification');
  };

  const handleBackToLogin = () => {
    onChange('login');
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow min-w-96">
      <Card style={{ border: 'none', textAlign: 'center' }}>
        <Result
          icon={<MailOutlined style={{ color: '#1890ff', fontSize: '72px' }} />}
          title={
            <Title level={3} style={{ color: '#1890ff', marginBottom: 16 }}>
              {t('emailVerification.checkEmailTitle')}
            </Title>
          }
          subTitle={
            <div style={{ marginBottom: 24 }}>
              <Text style={{ fontSize: '16px', color: '#666' }}>
                {t('emailVerification.checkEmailMessage')}
              </Text>
              {email && (
                <div style={{ marginTop: 8 }}>
                  <Text strong style={{ color: '#1890ff' }}>
                    {email}
                  </Text>
                </div>
              )}
            </div>
          }
          extra={[
            <Button
              type="primary"
              key="resend"
              icon={<ReloadOutlined />}
              onClick={handleResendVerification}
              style={{ marginRight: 8 }}
            >
              {t('emailVerification.resendEmail')}
            </Button>,
            <Button key="login" onClick={handleBackToLogin}>
              {t('backToLogin')}
            </Button>,
          ]}
        />

        <div
          style={{
            marginTop: 24,
            padding: '16px',
            backgroundColor: '#f0f2f5',
            borderRadius: '8px',
          }}
        >
          <Text style={{ fontSize: '14px', color: '#666' }}>
            {t('emailVerification.checkSpamFolder')}
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default EmailVerificationReminder;
