import { <PERSON><PERSON>, But<PERSON>, Divider, Form, Input, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import ConsoleService from '../../../services/console.service';
import { apiRegister } from '../api';
import {
  getTranslatedErrorMessage,
  handleApiError,
} from '../utils/errorHandler';

interface RegisterFormProps {
  onChange: (action: string, email?: string) => void;
}

const RegisterForm: React.FC<RegisterFormProps> = ({ onChange }) => {
  const { t } = useTranslation('auth');
  const logger = ConsoleService.register('register-form');
  const location = useLocation();
  const [form] = Form.useForm();
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Get invitation token from URL if present
    const searchParams = new URLSearchParams(location.search);
    const invitationToken =
      searchParams.get('invitation_token') || searchParams.get('token');

    if (invitationToken) {
      form.setFieldsValue({ invitationToken });
    }
  }, [location.search, form]);

  const onFinish = async (values: any) => {
    // Clear previous errors
    setError(null);

    if (values.password !== values.confirmPassword) {
      message.error(t('messages.passwordMismatch'));
      return;
    }

    try {
      const registerData: any = {
        email: values.email,
        password: values.password,
      };

      // Add optional fields if they have values
      if (values.username) registerData.username = values.username;
      if (values.firstName) registerData.first_name = values.firstName;
      if (values.lastName) registerData.last_name = values.lastName;
      if (values.invitationToken)
        registerData.invitation_token = values.invitationToken;

      const response = await apiRegister(registerData);

      if (response.status.success) {
        if (response.data.requires_email_verification) {
          message.success(t('messages.registerSuccessWithVerification'));
          onChange('email-verification-reminder', values.email);
        } else {
          message.success(t('messages.registerSuccess'));
          onChange('login');
        }
      } else {
        // Use utility function to get translated error message
        const errorMessage = getTranslatedErrorMessage(response.status, t);
        setError(errorMessage);
      }
    } catch (error: any) {
      logger('Register error:', error);

      // Use utility function to handle API error
      const errorMessage = handleApiError(error, t, 'messages.registerFailed');
      setError(errorMessage);
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow min-w-96">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold">{t('registerTitle')}</h2>
        <p className="text-gray-500">
          {t('haveAccount')}{' '}
          <a
            href="#"
            onClick={() => onChange('login')}
            className="text-blue-500"
          >
            {t('login')}
          </a>
        </p>
      </div>

      {/* Display error message */}
      {error && (
        <Alert
          message={t('registerError')}
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
          className="mb-4"
        />
      )}

      <Form
        form={form}
        name="register"
        layout="vertical"
        initialValues={{ remember: true }}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
      >
        <Form.Item
          label={t('username')}
          name="username"
          rules={[
            { required: true, message: t('validation.usernameRequired') },
            { min: 3, message: t('validation.usernameMinLength') },
          ]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label={t('firstName')}
          name="firstName"
          rules={[
            { required: true, message: t('validation.firstNameRequired') },
          ]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label={t('lastName')}
          name="lastName"
          rules={[
            { required: true, message: t('validation.lastNameRequired') },
          ]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label={t('email')}
          name="email"
          rules={[
            { required: true, message: t('validation.emailRequired') },
            { type: 'email', message: t('validation.emailInvalid') },
          ]}
        >
          <Input />
        </Form.Item>

        <Form.Item name="invitationToken" hidden>
          <Input type="hidden" />
        </Form.Item>

        <Form.Item
          label={t('password')}
          name="password"
          rules={[
            { required: true, message: t('validation.passwordRequired') },
            { min: 8, message: t('validation.passwordMinLength') },
          ]}
        >
          <Input.Password onChange={(e) => setPassword(e.target.value)} />
        </Form.Item>

        <Form.Item
          label={t('confirmPassword')}
          name="confirmPassword"
          rules={[
            {
              required: true,
              message: t('validation.confirmPasswordRequired'),
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error(t('validation.passwordMismatch')),
                );
              },
            }),
          ]}
        >
          <Input.Password />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" className="w-full">
            {t('registerWithEmail')}
          </Button>
        </Form.Item>

        <Divider plain>{t('or')}</Divider>

        <div className="space-y-3">
          <Button
            className="w-full flex items-center justify-center"
            icon={
              <img
                src="/google-icon.svg"
                alt="Google"
                className="mr-2 h-5 w-5"
              />
            }
          >
            {t('loginWithGoogle')}
          </Button>

          <Button
            className="w-full flex items-center justify-center"
            icon={
              <img
                src="/facebook-icon.svg"
                alt="Facebook"
                className="mr-2 h-5 w-5"
              />
            }
          >
            {t('loginWithFacebook')}
          </Button>

          <Button
            className="w-full flex items-center justify-center"
            icon={
              <img src="/apple-icon.svg" alt="Apple" className="mr-2 h-5 w-5" />
            }
          >
            {t('loginWithApple')}
          </Button>
        </div>

        <div className="mt-4 text-center">
          <a href="#" className="text-blue-500">
            {t('loginWithSSO')}
          </a>
        </div>

        <div className="mt-6 text-xs text-center text-gray-500">
          <p>{t('termsAgreement')}</p>
        </div>
      </Form>
    </div>
  );
};

export default RegisterForm;
