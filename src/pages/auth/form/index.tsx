import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import useSystemSettingStore from '../../system-setting/store';
import EmailVerificationReminder from './email-verification-reminder';
import ForgotPasswordForm from './forgot-password';
import ForgotPasswordSuccess from './forgot-password-success';
import LoginForm from './form';
import OTPForm from './otpConfigForm';
import OTPVerifyForm from './otpVerifyForm';
import RegisterForm from './register';
import ResetPasswordForm from './reset-password';

const Login: React.FC = () => {
  const { itemPublicCMS, getPublicCMS } = useSystemSettingStore();
  const location = useLocation();
  const navigate = useNavigate();
  const [step, setStep] = useState<
    | 'login'
    | 'register'
    | 'otp_config'
    | 'otp_verify'
    | 'tenant_selection'
    | 'email-verification-reminder'
    | 'forgot-password'
    | 'forgot-password-success'
    | 'reset-password'
  >('login');
  const [userEmail, setUserEmail] = useState<string>('');

  useEffect(() => {
    getPublicCMS();

    // Check URL parameters to determine initial step
    const searchParams = new URLSearchParams(location.search);
    const mode = searchParams.get('mode');
    const token = searchParams.get('token');

    if (mode === 'register') {
      setStep('register');
    } else if (token && location.pathname.includes('reset-password')) {
      setStep('reset-password');
    } else {
      setStep('login');
    }
  }, [getPublicCMS, location.search, location.pathname]);

  const handleLogin = (action: string, email?: string) => {
    console.log('[Auth Index] handleLogin called with action:', action);

    if (action === 'register') {
      setStep('register');
      // Add query parameter to URL to maintain state on refresh
      navigate(`${location.pathname}?mode=register`, { replace: true });
    } else if (action === 'login') {
      setStep('login');
      // Remove query parameter when switching to login
      navigate(location.pathname, { replace: true });
    } else if (action === 'email-verification-reminder') {
      setStep('email-verification-reminder');
      if (email) {
        setUserEmail(email);
      }
    } else if (action === 'resend-verification') {
      // Navigate to resend verification page
      navigate('/auth/resend-verification');
    } else if (action === 'forgot-password') {
      setStep('forgot-password');
    } else if (action === 'forgot-password-success') {
      setStep('forgot-password-success');
      if (email) {
        setUserEmail(email);
      }
    } else if (action === 'reset-password') {
      setStep('reset-password');
    } else if (action === 'otp_config') {
      setStep('otp_config');
    } else if (action === 'otp_verify') {
      setStep('otp_verify');
    } else if (action === 'logout') {
      setStep('login');
      navigate(location.pathname, { replace: true });
    } else if (action === 'success') {
      // Successful login - redirect to tenant selection
      //navigate('/auth/tenant-selection');
    } else if (action === 'onboarding') {
      // User requires onboarding
      console.log('[Auth Index] Navigating to /onboarding');
      navigate('/onboarding');
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen">
      {step === 'login' && (
        <LoginForm
          onChange={handleLogin}
          otpEnabled={itemPublicCMS?.otpEnabled}
        />
      )}
      {step === 'register' && <RegisterForm onChange={handleLogin} />}
      {step === 'email-verification-reminder' && (
        <EmailVerificationReminder email={userEmail} onChange={handleLogin} />
      )}
      {step === 'forgot-password' && (
        <ForgotPasswordForm onChange={handleLogin} />
      )}
      {step === 'forgot-password-success' && (
        <ForgotPasswordSuccess email={userEmail} onChange={handleLogin} />
      )}
      {step === 'reset-password' && (
        <ResetPasswordForm onChange={handleLogin} />
      )}
      {step === 'otp_config' && <OTPForm onChange={handleLogin} />}
      {step === 'otp_verify' && <OTPVerifyForm onChange={handleLogin} />}
    </div>
  );
};

export default Login;
