import { <PERSON><PERSON>, Button, Form, Input, message } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MailOutlined } from '@ant-design/icons';
import ConsoleService from '../../../services/console.service';
import { forgotPassword } from '../api';
import {
  getTranslatedErrorMessage,
  handleApiError,
} from '../utils/errorHandler';

interface ForgotPasswordFormProps {
  onChange: (action: string) => void;
}

const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({
  onChange,
}) => {
  const { t } = useTranslation('auth');
  const logger = ConsoleService.register('forgot-password-form');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const onFinish = async (values: any) => {
    setError(null);
    setLoading(true);

    try {
      const response = await forgotPassword(values.email);

      if (response.status.success) {
        setSuccess(true);
        message.success(t('forgotPassword.emailSentSuccess'));
        // Navigate to success page with email
        setTimeout(() => {
          onChange('forgot-password-success', values.email);
        }, 2000);
      } else {
        const errorMessage = getTranslatedErrorMessage(response.status, t);
        setError(errorMessage);
      }
    } catch (error: any) {
      logger('Forgot password error:', error);
      const errorMessage = handleApiError(error, t, 'forgotPassword.failed');
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="bg-white p-6 rounded-lg shadow min-w-96">
        <div className="text-center">
          <MailOutlined
            style={{ fontSize: 48, color: '#1890ff', marginBottom: 24 }}
          />
          <h2 className="text-2xl font-bold mb-4">
            {t('forgotPassword.checkEmailTitle')}
          </h2>
          <p className="text-gray-500 mb-6">
            {t('forgotPassword.checkEmailMessage')}
          </p>
          <Button type="primary" onClick={() => onChange('login')}>
            {t('backToLogin')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow min-w-96">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold">{t('forgotPassword.title')}</h2>
        <p className="text-gray-500 mt-2">{t('forgotPassword.description')}</p>
      </div>

      {error && (
        <Alert
          message={t('forgotPassword.error')}
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
          className="mb-4"
        />
      )}

      <Form
        name="forgot-password"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
      >
        <Form.Item
          label={t('email')}
          name="email"
          rules={[
            { required: true, message: t('validation.emailRequired') },
            { type: 'email', message: t('validation.emailInvalid') },
          ]}
        >
          <Input
            prefix={<MailOutlined className="text-gray-400" />}
            placeholder={t('forgotPassword.emailPlaceholder')}
            size="large"
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            className="w-full"
            loading={loading}
            size="large"
          >
            {t('forgotPassword.sendResetLink')}
          </Button>
        </Form.Item>

        <div className="text-center">
          <span className="text-gray-500">{t('rememberPassword')} </span>
          <a
            href="#"
            onClick={() => onChange('login')}
            className="text-blue-500"
          >
            {t('backToLogin')}
          </a>
        </div>
      </Form>
    </div>
  );
};

export default ForgotPasswordForm;
