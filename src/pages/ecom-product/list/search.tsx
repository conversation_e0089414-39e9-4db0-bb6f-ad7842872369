import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Row, Select } from 'antd';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import { ProductStatus } from '../type';

function ProductSearch(props: any) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    logger('[values]', values);
    props.onChange(values);
  };

  function handlerRefresh() {
    form.resetFields();
    props.onChange({});
  }

  return (
    <div className="bg-white p-4">
      <Form
        form={form}
        name="search"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        initialValues={props.query}
      >
        <Row gutter={16}>
          <Col xs={24} lg={8}>
            <Form.Item label={t('search')} name="search">
              <Input
                placeholder={t('search')}
                allowClear
                suffix={<SearchOutlined />}
              />
            </Form.Item>
          </Col>
          <Col xs={24} lg={8}>
            <Form.Item label={t('status')} name="status">
              <Select
                placeholder={t('status')}
                allowClear
                options={Object.entries(ProductStatus).map(
                  ([value, label]) => ({
                    value: label,
                    label: t(`status.${label}`),
                  }),
                )}
              />
            </Form.Item>
          </Col>
          <Col xs={24} lg={8}>
            <Form.Item label=" " colon={false}>
              <div className="flex gap-2">
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={props.loading}
                  icon={<SearchOutlined />}
                >
                  {t('search')}
                </Button>
                <Button
                  type="default"
                  onClick={handlerRefresh}
                  icon={<ReloadOutlined />}
                >
                  {t('reset')}
                </Button>
              </div>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
}

export { ProductSearch };
