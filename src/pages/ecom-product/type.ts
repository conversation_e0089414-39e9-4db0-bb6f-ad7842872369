import { ProductAttribute as ImportedProductAttribute } from './components';
import { ProductVariant as ImportedProductVariant } from './components';

export interface Product {
  product_id?: number;
  name: string;
  description?: string;
  content?: string;
  slug?: string;
  sku?: string;
  product_code?: string;
  image_url?: string;
  base_price?: number;
  cost_price?: number;
  sale_price?: number;
  status: string;
  product_type: string;
  is_taxable?: boolean;
  is_virtual?: boolean;
  is_downloadable?: boolean;
  category_id?: number;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string;
  created_at?: string;
  updated_at?: string;
  attributes?: ImportedProductAttribute[];
  variants?: ImportedProductVariant[];
  manage_variant_pricing?: boolean;
}

export enum ProductStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED',
  DISCONTINUED = 'DISCONTINUED',
}

export enum ProductType {
  SIMPLE = 'SIMPLE',
  CONFIGURABLE = 'CONFIGURABLE',
  DIGITAL = 'DIGITAL',
  BUNDLE = 'BUNDLE',
}

export interface ProductPricePolicy {
  price_policy_id: number;
  tenant_id: number;
  product_id: number;
  name: string;
  type: string;
  value: number;
  min_quantity?: number;
  customer_group_id?: number;
  start_date?: string;
  end_date?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export enum PricePolicyType {
  PERCENTAGE = 'percentage',
  FIXED = 'fixed',
  SPECIAL = 'special',
}

export enum PricePolicyStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export interface ProductAttributeGroup {
  attribute_group_id: number;
  tenant_id: number;
  name: string;
  code: string;
  description?: string;
  position?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ProductAttribute {
  attribute_id: number;
  tenant_id: number;
  attribute_group_id: number;
  name: string;
  code: string;
  description?: string;
  type: string;
  is_required: boolean;
  is_unique: boolean;
  is_configurable: boolean;
  is_filterable: boolean;
  is_searchable: boolean;
  position?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export enum AttributeType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  SELECT = 'select',
  MULTISELECT = 'multiselect',
  BOOLEAN = 'boolean',
  DATE = 'date',
  PRICE = 'price',
  MEDIA = 'media',
}

export interface ProductAttributeOption {
  option_id: number;
  tenant_id: number;
  attribute_id: number;
  value: string;
  position?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ProductAttributeValue {
  value_id: number;
  tenant_id: number;
  product_id: number;
  attribute_id: number;
  option_id?: number;
  value: string;
  created_at: string;
  updated_at: string;
}

export interface ProductVariant {
  variant_id?: number;
  product_id: number;
  sku: string;
  price: number;
  quantity?: number;
  image_url?: string;
  is_active: boolean;
  attributes?: Record<string, string>;
  attribute_values?: {
    attribute_id: number;
    attribute_option_id: number;
    attribute_name?: string;
    option_value?: string;
    option_label?: string;
  }[];
}

export interface ProductVariantAttributeValue {
  variant_attribute_value_id: number;
  tenant_id: number;
  variant_id: number;
  attribute_id: number;
  option_id?: number;
  value: string;
  created_at: string;
  updated_at: string;
}

export interface ProductCategory {
  category_id: number;
  name: string;
  description?: string;
  parent_id?: number;
  level?: number;
  lft?: number;
  rgt?: number;
}
