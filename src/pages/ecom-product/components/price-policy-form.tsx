import {
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Select,
} from 'antd';
import _ from 'lodash';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { createPricePolicy, getPricePolicy, updatePricePolicy } from '../api';
import { MODULE } from '../config';
import {
  PricePolicyStatus,
  PricePolicyType,
  ProductPricePolicy,
} from '../type';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

interface PricePolicyFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
  productId?: string;
}

const PricePolicyForm = forwardRef<unknown, PricePolicyFormProps>(
  ({ onChange, id, productId }, ref) => {
    const { t } = useTranslation(MODULE);
    const logger = ConsoleService.register(MODULE);
    const [form] = Form.useForm();
    const [isNew, setIsNew] = useState<boolean>(false);
    const [item, setItem] = useState<ProductPricePolicy>();
    const [formValues, setFormValues] = useState<ProductPricePolicy>();

    const initForm = {
      product_id: productId,
      type: PricePolicyType.PERCENTAGE,
      status: PricePolicyStatus.ACTIVE,
    };

    const getItemData = async (_id: string) => {
      const res = await getPricePolicy(_id);
      if (res.status.success) {
        setItem(res.data);
        form.setFieldsValue(res.data);
      } else {
        message.error(res.status.message);
      }
    };

    useEffect(() => {
      logger(id);
      form.resetFields();
      if (['create', undefined].includes(id)) {
        setIsNew(true);
        form.setFieldsValue(initForm);
      } else if (id) {
        setIsNew(false);
        getItemData(id);
      }
    }, [id, productId]);

    const onFinish = async (values: ProductPricePolicy) => {
      try {
        let res;
        if (isNew) {
          res = await createPricePolicy(values);
          if (res.status.success) {
            message.success(t('pricePolicy.form.addSuccess'));
          }
        } else {
          res = await updatePricePolicy(id!, values);
          if (res.status.success) {
            message.success(t('pricePolicy.form.updateSuccess'));
          }
        }
        if (!res.status.success) {
          message.error(res.status.message);
        } else {
          setItem(res.data);
          form.resetFields();
          onChange(true);
        }
      } catch (error) {
        logger('Error submitting form', error);
        message.error(
          _.get(error, 'response.data.message.0') ||
            t('pricePolicy.form.submitError'),
        );
      }
    };

    const handleValuesChange = (newValue: any, allValues: any) => {
      logger(newValue);
      logger(allValues);
      setFormValues(allValues);
    };

    useImperativeHandle(ref, () => ({
      submitForm: () => form.submit(),
    }));

    return (
      <Form
        form={form}
        name="form"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        initialValues={initForm}
        onValuesChange={handleValuesChange}
      >
        <div className="form_content">
          <Row gutter={16}>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('pricePolicy.form.name')}
                name="name"
                rules={[
                  {
                    required: true,
                    message: t('pricePolicy.form.pleaseEnterData'),
                  },
                ]}
              >
                <Input />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('pricePolicy.form.type')}
                name="type"
                rules={[
                  {
                    required: true,
                    message: t('pricePolicy.form.pleaseEnterData'),
                  },
                ]}
              >
                <Select
                  options={Object.entries(PricePolicyType).map(
                    ([value, label]) => ({
                      value: label,
                      label: t(`pricePolicy.type.${label}`),
                    }),
                  )}
                />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('pricePolicy.form.value')}
                name="value"
                rules={[
                  {
                    required: true,
                    message: t('pricePolicy.form.pleaseEnterData'),
                  },
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  addonAfter={
                    formValues?.type === PricePolicyType.PERCENTAGE ? '%' : '$'
                  }
                />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('pricePolicy.form.minQuantity')}
                name="min_quantity"
              >
                <InputNumber style={{ width: '100%' }} min={1} precision={0} />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('pricePolicy.form.dateRange')}
                name="date_range"
              >
                <RangePicker
                  style={{ width: '100%' }}
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('pricePolicy.form.status')}
                name="status"
                rules={[
                  {
                    required: true,
                    message: t('pricePolicy.form.pleaseEnterData'),
                  },
                ]}
              >
                <Select
                  options={Object.entries(PricePolicyStatus).map(
                    ([value, label]) => ({
                      value: label,
                      label: t(`pricePolicy.status.${label}`),
                    }),
                  )}
                />
              </FormItem>
            </Col>
          </Row>
        </div>
      </Form>
    );
  },
);
PricePolicyForm.displayName = 'PricePolicyForm';

export { PricePolicyForm };
