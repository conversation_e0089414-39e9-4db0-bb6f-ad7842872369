import { <PERSON><PERSON>, Col, DatePicker, Form, Input, Row, Select } from 'antd';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { MODULE } from '../config';
import { PricePolicyStatus, PricePolicyType } from '../type';

const { RangePicker } = DatePicker;
const FormItem = Form.Item;

interface PricePolicySearchProps {
  onSearch: (values: any) => void;
}

export function PricePolicySearch({ onSearch }: PricePolicySearchProps) {
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();

  useEffect(() => {
    form.resetFields();
  }, []);

  const handleFinish = (values: any) => {
    onSearch(values);
  };

  const handleReset = () => {
    form.resetFields();
    onSearch({});
  };

  return (
    <Form form={form} onFinish={handleFinish} layout="vertical">
      <Row gutter={16}>
        <Col xs={24} md={8}>
          <FormItem label={t('pricePolicy.search.name')} name="name">
            <Input placeholder={t('pricePolicy.search.namePlaceholder')} />
          </FormItem>
        </Col>
        <Col xs={24} md={8}>
          <FormItem label={t('pricePolicy.search.type')} name="type">
            <Select
              allowClear
              placeholder={t('pricePolicy.search.typePlaceholder')}
              options={Object.entries(PricePolicyType).map(
                ([value, label]) => ({
                  value: label,
                  label: t(`pricePolicy.type.${label}`),
                }),
              )}
            />
          </FormItem>
        </Col>
        <Col xs={24} md={8}>
          <FormItem label={t('pricePolicy.search.status')} name="status">
            <Select
              allowClear
              placeholder={t('pricePolicy.search.statusPlaceholder')}
              options={Object.entries(PricePolicyStatus).map(
                ([value, label]) => ({
                  value: label,
                  label: t(`pricePolicy.status.${label}`),
                }),
              )}
            />
          </FormItem>
        </Col>
        <Col xs={24} md={8}>
          <FormItem label={t('pricePolicy.search.dateRange')} name="date_range">
            <RangePicker style={{ width: '100%' }} />
          </FormItem>
        </Col>
        <Col xs={24} md={16}>
          <FormItem style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button type="primary" htmlType="submit" style={{ marginRight: 8 }}>
              {t('common.search')}
            </Button>
            <Button onClick={handleReset}>{t('common.reset')}</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>
  );
}
