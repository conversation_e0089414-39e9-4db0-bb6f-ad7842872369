{"module": "Products", "id": "ID", "name": "Name", "slug": "Slug", "basePrice": "Price", "costPrice": "Cost Price", "productType": "Product Type", "category": "Category", "status": "Status", "search": "Search", "reset": "Reset", "inputData": "Please enter data!", "btnAdd": "Add", "btnEdit": "Edit", "btnUpdate": "Save", "btnDelete": "Delete", "btnCancel": "Cancel", "btnSaveAndContinue": "Save and Continue", "btnSave": "Save", "btnVariants": "Variants", "btnAttributes": "Attributes", "addSuccess": "Saved successfully", "updateSuccess": "Updated successfully", "deleteSuccess": "Deleted successfully", "submitError": "Save failed. Try again later", "pleaseEnterData": "Please enter data", "actions": "Actions", "deleteConfirm": "Do you agree to delete?", "statusActive": "Active", "statusInactive": "Inactive", "status.DRAFT": "Draft", "status.PUBLISHED": "Published", "status.ARCHIVED": "Archived", "status.DISCONTINUED": "Discontinued", "productType.SIMPLE": "Simple", "productType.CONFIGURABLE": "Configurable", "productType.DIGITAL": "Digital", "productType.BUNDLE": "Bundle", "form": {"add": "Add Product", "edit": "Edit Product", "name": "Name", "slug": "Slug", "slugTooltip": "URL-friendly name (auto-generated if left empty)", "basePrice": "Base Price", "description": "Description", "content": "Content", "status": "Status", "pleaseEnterData": "Please enter data", "btnCancel": "Cancel", "btnSave": "Save", "btnSaveAndContinue": "Save and Continue", "addSuccess": "Product saved successfully", "updateSuccess": "Product updated successfully", "submitError": "Save failed. Try again later", "loading": "Loading..."}}