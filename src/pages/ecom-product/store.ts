import { create } from 'zustand';
import { deleteItem, getOptions } from './api';
import { Product } from './type';

export interface StoreState {
  loading: boolean;
  items: Product[];
  options: any[];
  setLoading: (loading: boolean) => void;
  delete: (id: string) => void;
  setOptions: (options: any) => void;
  deleteItem: (id: string) => Promise<void>;
  getOptions: () => Promise<void>;
}

const useProductStore = create<StoreState>((set, get) => ({
  loading: false,
  items: [],
  options: [],
  setLoading: (loading: boolean) => set({ loading }),
  delete: (id: string) => {
    const { items } = get();
    set({ items: items.filter((item) => item.product_id.toString() !== id) });
  },
  setOptions: (options: any) => set({ options }),
  deleteItem: async (id: string) => {
    set({ loading: true });
    await deleteItem(id);
    get().delete(id);
    set({ loading: false });
  },
  getOptions: async () => {
    set({ loading: true });
    const response = await getOptions();
    if (response.status.success) {
      set({ options: response.data });
    }
    set({ loading: false });
  },
}));

export default useProductStore;
