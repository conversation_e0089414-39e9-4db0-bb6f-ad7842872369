import React from 'react';
import { Route, Routes } from 'react-router-dom';
import { ProductList } from './list/list';
import { ProductForm } from './form/standalone';
import { ProductVariantList } from './variants/list';
import { ProductVariantForm } from './variants/form';
import { ProductAttributeValueList } from './attributes/list';
import { ProductAttributeValueForm } from './attributes/form';

const ProductRouter: React.FC = () => {
  return (
    <Routes>
      {/* Main product routes */}
      <Route path="/" element={<ProductList />} />
      <Route path="/create" element={<ProductForm />} />
      <Route path="/:id" element={<ProductForm />} />

      {/* Product variants routes */}
      <Route path="/:productId/variants" element={<ProductVariantList />} />
      <Route
        path="/:productId/variants/create"
        element={<ProductVariantForm />}
      />
      <Route
        path="/:productId/variants/:variantId"
        element={<ProductVariantForm />}
      />

      {/* Product attribute values routes */}
      <Route
        path="/:productId/attributes"
        element={<ProductAttributeValueList />}
      />
      <Route
        path="/:productId/attributes/create"
        element={<ProductAttributeValueForm />}
      />
      <Route
        path="/:productId/attributes/:valueId"
        element={<ProductAttributeValueForm />}
      />
    </Routes>
  );
};

export default ProductRouter;
