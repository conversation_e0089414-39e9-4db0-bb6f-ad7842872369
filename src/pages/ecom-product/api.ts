import { apiService } from '../../services/api.service';
import {
  Product,
  ProductAttribute,
  ProductAttributeGroup,
  ProductAttributeOption,
  ProductAttributeValue,
  ProductPricePolicy,
  ProductVariant,
  ProductVariantAttributeValue,
} from './type';

const BASE_URL = `/api/cms/v1/`;
const PRODUCTS_URL = `${BASE_URL}/product`;
const ATTRIBUTE_GROUPS_URL = `${BASE_URL}/product-attribute-groups`;
const ATTRIBUTES_URL = `${BASE_URL}/product-attributes`;
const ATTRIBUTE_OPTIONS_URL = `${BASE_URL}/product-attribute-options`;
const ATTRIBUTE_VALUES_URL = `${BASE_URL}/product-attribute-values`;
const VARIANTS_URL = `${BASE_URL}/product-variants`;
const VARIANT_ATTRIBUTE_VALUES_URL = `${BASE_URL}/product-variant-attribute-values`;
const PRICE_POLICY_URL = `${BASE_URL}/product-price-policies`;

export const apiUrl = '/api/cms/v1/product';

interface ApiResponse<T> {
  status: {
    code: number;
    message: string;
    success: boolean;
    error_code: string | null;
    path: string;
    timestamp: string;
    details: any[] | null;
  };
  data: T;
  meta?: {
    next_cursor: string;
    has_more: boolean;
  };
}

// Lấy danh sách sản phẩm
export const getItems = async (
  params?: any,
): Promise<ApiResponse<Product[]>> => {
  try {
    const response = await apiService.get(apiUrl, { params });
    return response.data;
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data;
    }
    throw error;
  }
};

// Hàm getOptions cho dropdown sản phẩm
export const getOptions = async (
  params?: any,
): Promise<ApiResponse<Product[]>> => {
  try {
    const response = await apiService.get(`${PRODUCTS_URL}`, { params });
    return response.data;
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data;
    }
    throw error;
  }
};

// Lấy thông tin chi tiết sản phẩm
export const getItem = async (id: string): Promise<ApiResponse<Product>> => {
  try {
    const response = await apiService.get(`${apiUrl}/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data;
    }
    throw error;
  }
};

// Tạo sản phẩm mới
export const createItem = async (data: any): Promise<ApiResponse<Product>> => {
  try {
    const response = await apiService.post(apiUrl, data);
    return response.data;
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data;
    }
    throw error;
  }
};

// Cập nhật sản phẩm
export const updateItem = async (
  id: string,
  data: any,
): Promise<ApiResponse<Product>> => {
  try {
    const response = await apiService.put(`${apiUrl}/${id}`, data);
    return response.data;
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data;
    }
    throw error;
  }
};

// Xóa sản phẩm
export const deleteItem = async (id: string): Promise<ApiResponse<null>> => {
  try {
    const response = await apiService.delete(`${apiUrl}/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data;
    }
    throw error;
  }
};

// Lấy danh mục sản phẩm
export const getProductCategories = async (): Promise<ApiResponse<any[]>> => {
  try {
    const response = await apiService.get('/api/cms/v1/ecom/categories');
    return response.data;
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data;
    }
    throw error;
  }
};

// Lấy các biến thể của sản phẩm
export const getProductVariants = async (
  productId: string,
): Promise<ApiResponse<ProductVariant[]>> => {
  try {
    const response = await apiService.get(
      `/api/cms/v1/ecom/product-variants/product/${productId}`,
    );
    return response.data;
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data;
    }
    throw error;
  }
};

// Product Price Policy API
export const getPricePolicy = async (
  id: string,
): Promise<ApiResponse<ProductPricePolicy>> => {
  try {
    const response = await apiService.get(`${PRICE_POLICY_URL}/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data;
    }
    throw error;
  }
};

export const getPricePoliciesByProduct = async (
  productId: string,
  params?: any,
): Promise<ApiResponse<ProductPricePolicy[]>> => {
  try {
    const response = await apiService.get(
      `${PRICE_POLICY_URL}/product/${productId}`,
      { params },
    );
    return response.data;
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data;
    }
    throw error;
  }
};

export const createPricePolicy = async (
  data: any,
): Promise<ApiResponse<ProductPricePolicy>> => {
  try {
    const response = await apiService.post(PRICE_POLICY_URL, data);
    return response.data;
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data;
    }
    throw error;
  }
};

export const updatePricePolicy = async (
  id: string,
  data: any,
): Promise<ApiResponse<ProductPricePolicy>> => {
  try {
    const response = await apiService.put(`${PRICE_POLICY_URL}/${id}`, data);
    return response.data;
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data;
    }
    throw error;
  }
};

export const deletePricePolicy = async (
  id: string,
): Promise<ApiResponse<null>> => {
  try {
    const response = await apiService.delete(`${PRICE_POLICY_URL}/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data;
    }
    throw error;
  }
};

// Product Attribute Groups API
export async function getAttributeGroups(
  params?: any,
): Promise<ApiResponse<ProductAttributeGroup[]>> {
  const response = await apiService.get<ApiResponse<ProductAttributeGroup[]>>(
    ATTRIBUTE_GROUPS_URL,
    { params },
  );
  return response.data;
}

export async function getAttributeGroup(
  id: string,
): Promise<ApiResponse<ProductAttributeGroup>> {
  const response = await apiService.get<ApiResponse<ProductAttributeGroup>>(
    `${ATTRIBUTE_GROUPS_URL}/${id}`,
  );
  return response.data;
}

export async function getAttributeGroupByCode(
  code: string,
): Promise<ApiResponse<ProductAttributeGroup>> {
  const response = await apiService.get<ApiResponse<ProductAttributeGroup>>(
    `${ATTRIBUTE_GROUPS_URL}/code/${code}`,
  );
  return response.data;
}

export async function getAllAttributeGroups(): Promise<
  ApiResponse<ProductAttributeGroup[]>
> {
  const response = await apiService.get<ApiResponse<ProductAttributeGroup[]>>(
    `${ATTRIBUTE_GROUPS_URL}/all`,
  );
  return response.data;
}

export async function createAttributeGroup(
  payload: any,
): Promise<ApiResponse<ProductAttributeGroup>> {
  const response = await apiService.post<ApiResponse<ProductAttributeGroup>>(
    ATTRIBUTE_GROUPS_URL,
    payload,
  );
  return response.data;
}

export async function updateAttributeGroup(
  id: string,
  payload: any,
): Promise<ApiResponse<ProductAttributeGroup>> {
  const response = await apiService.put<ApiResponse<ProductAttributeGroup>>(
    `${ATTRIBUTE_GROUPS_URL}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteAttributeGroup(
  id: string,
): Promise<ApiResponse<ProductAttributeGroup>> {
  const response = await apiService.delete<ApiResponse<ProductAttributeGroup>>(
    `${ATTRIBUTE_GROUPS_URL}/${id}`,
  );
  return response.data;
}

// Product Attributes API
export async function getAttributes(
  params?: any,
): Promise<ApiResponse<ProductAttribute[]>> {
  const response = await apiService.get<ApiResponse<ProductAttribute[]>>(
    ATTRIBUTES_URL,
    { params },
  );
  return response.data;
}

export async function getAttribute(
  id: string,
): Promise<ApiResponse<ProductAttribute>> {
  const response = await apiService.get<ApiResponse<ProductAttribute>>(
    `${ATTRIBUTES_URL}/${id}`,
  );
  return response.data;
}

export async function getAttributeByCode(
  code: string,
): Promise<ApiResponse<ProductAttribute>> {
  const response = await apiService.get<ApiResponse<ProductAttribute>>(
    `${ATTRIBUTES_URL}/code/${code}`,
  );
  return response.data;
}

export async function getAllAttributes(): Promise<
  ApiResponse<ProductAttribute[]>
> {
  const response = await apiService.get<ApiResponse<ProductAttribute[]>>(
    `${ATTRIBUTES_URL}/all`,
  );
  return response.data;
}

export async function getAttributesByGroupId(
  groupId: string,
): Promise<ApiResponse<ProductAttribute[]>> {
  const response = await apiService.get<ApiResponse<ProductAttribute[]>>(
    `${ATTRIBUTES_URL}/group/${groupId}`,
  );
  return response.data;
}

export async function getConfigurableAttributes(): Promise<
  ApiResponse<ProductAttribute[]>
> {
  const response = await apiService.get<ApiResponse<ProductAttribute[]>>(
    `${ATTRIBUTES_URL}/configurable`,
  );
  return response.data;
}

export async function getFilterableAttributes(): Promise<
  ApiResponse<ProductAttribute[]>
> {
  const response = await apiService.get<ApiResponse<ProductAttribute[]>>(
    `${ATTRIBUTES_URL}/filterable`,
  );
  return response.data;
}

export async function createAttribute(
  payload: any,
): Promise<ApiResponse<ProductAttribute>> {
  const response = await apiService.post<ApiResponse<ProductAttribute>>(
    ATTRIBUTES_URL,
    payload,
  );
  return response.data;
}

export async function updateAttribute(
  id: string,
  payload: any,
): Promise<ApiResponse<ProductAttribute>> {
  const response = await apiService.put<ApiResponse<ProductAttribute>>(
    `${ATTRIBUTES_URL}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteAttribute(
  id: string,
): Promise<ApiResponse<ProductAttribute>> {
  const response = await apiService.delete<ApiResponse<ProductAttribute>>(
    `${ATTRIBUTES_URL}/${id}`,
  );
  return response.data;
}

// Product Attribute Options API
export async function getAttributeOptions(
  params?: any,
): Promise<ApiResponse<ProductAttributeOption[]>> {
  const response = await apiService.get<ApiResponse<ProductAttributeOption[]>>(
    ATTRIBUTE_OPTIONS_URL,
    { params },
  );
  return response.data;
}

export async function getAttributeOption(
  id: string,
): Promise<ApiResponse<ProductAttributeOption>> {
  const response = await apiService.get<ApiResponse<ProductAttributeOption>>(
    `${ATTRIBUTE_OPTIONS_URL}/${id}`,
  );
  return response.data;
}

export async function getAllAttributeOptions(): Promise<
  ApiResponse<ProductAttributeOption[]>
> {
  const response = await apiService.get<ApiResponse<ProductAttributeOption[]>>(
    `${ATTRIBUTE_OPTIONS_URL}/all`,
  );
  return response.data;
}

export async function getAttributeOptionsByAttributeId(
  attributeId: string,
): Promise<ApiResponse<ProductAttributeOption[]>> {
  const response = await apiService.get<ApiResponse<ProductAttributeOption[]>>(
    `${ATTRIBUTE_OPTIONS_URL}/attribute/${attributeId}`,
  );
  return response.data;
}

export async function createAttributeOption(
  payload: any,
): Promise<ApiResponse<ProductAttributeOption>> {
  const response = await apiService.post<ApiResponse<ProductAttributeOption>>(
    ATTRIBUTE_OPTIONS_URL,
    payload,
  );
  return response.data;
}

export async function updateAttributeOption(
  id: string,
  payload: any,
): Promise<ApiResponse<ProductAttributeOption>> {
  const response = await apiService.put<ApiResponse<ProductAttributeOption>>(
    `${ATTRIBUTE_OPTIONS_URL}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteAttributeOption(
  id: string,
): Promise<ApiResponse<ProductAttributeOption>> {
  const response = await apiService.delete<ApiResponse<ProductAttributeOption>>(
    `${ATTRIBUTE_OPTIONS_URL}/${id}`,
  );
  return response.data;
}

// Product Attribute Values API
export async function getAttributeValue(
  id: string,
): Promise<ApiResponse<ProductAttributeValue>> {
  const response = await apiService.get<ApiResponse<ProductAttributeValue>>(
    `${ATTRIBUTE_VALUES_URL}/${id}`,
  );
  return response.data;
}

export async function createAttributeValue(
  payload: any,
): Promise<ApiResponse<ProductAttributeValue>> {
  const response = await apiService.post<ApiResponse<ProductAttributeValue>>(
    ATTRIBUTE_VALUES_URL,
    payload,
  );
  return response.data;
}

export async function updateAttributeValue(
  id: string,
  payload: any,
): Promise<ApiResponse<ProductAttributeValue>> {
  const response = await apiService.put<ApiResponse<ProductAttributeValue>>(
    `${ATTRIBUTE_VALUES_URL}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteAttributeValue(
  id: string,
): Promise<ApiResponse<ProductAttributeValue>> {
  const response = await apiService.delete<ApiResponse<ProductAttributeValue>>(
    `${ATTRIBUTE_VALUES_URL}/${id}`,
  );
  return response.data;
}

export async function batchCreateAttributeValues(
  payload: any,
): Promise<ApiResponse<ProductAttributeValue[]>> {
  const response = await apiService.post<ApiResponse<ProductAttributeValue[]>>(
    `${ATTRIBUTE_VALUES_URL}/batch`,
    payload,
  );
  return response.data;
}

export async function getAttributeValuesByProductId(
  productId: string,
): Promise<ApiResponse<ProductAttributeValue[]>> {
  const response = await apiService.get<ApiResponse<ProductAttributeValue[]>>(
    `${ATTRIBUTE_VALUES_URL}/product/${productId}`,
  );
  return response.data;
}

export async function deleteAttributeValuesByProductId(
  productId: string,
): Promise<ApiResponse<any>> {
  const response = await apiService.delete<ApiResponse<any>>(
    `${ATTRIBUTE_VALUES_URL}/product/${productId}`,
  );
  return response.data;
}

// Product Variants API
export async function getVariants(
  params?: any,
): Promise<ApiResponse<ProductVariant[]>> {
  const response = await apiService.get<ApiResponse<ProductVariant[]>>(
    VARIANTS_URL,
    { params },
  );
  return response.data;
}

export async function getVariant(
  id: string,
): Promise<ApiResponse<ProductVariant>> {
  const response = await apiService.get<ApiResponse<ProductVariant>>(
    `${VARIANTS_URL}/${id}`,
  );
  return response.data;
}

export async function createVariant(
  payload: any,
): Promise<ApiResponse<ProductVariant>> {
  const response = await apiService.post<ApiResponse<ProductVariant>>(
    VARIANTS_URL,
    payload,
  );
  return response.data;
}

export async function updateVariant(
  id: string,
  payload: any,
): Promise<ApiResponse<ProductVariant>> {
  const response = await apiService.put<ApiResponse<ProductVariant>>(
    `${VARIANTS_URL}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteVariant(
  id: string,
): Promise<ApiResponse<ProductVariant>> {
  const response = await apiService.delete<ApiResponse<ProductVariant>>(
    `${VARIANTS_URL}/${id}`,
  );
  return response.data;
}

export async function batchCreateVariants(
  payload: any,
): Promise<ApiResponse<ProductVariant[]>> {
  const response = await apiService.post<ApiResponse<ProductVariant[]>>(
    `${VARIANTS_URL}/batch`,
    payload,
  );
  return response.data;
}

export async function getVariantsByProductId(
  productId: string,
): Promise<ApiResponse<ProductVariant[]>> {
  const response = await apiService.get<ApiResponse<ProductVariant[]>>(
    `${VARIANTS_URL}/product/${productId}`,
  );
  return response.data;
}

export async function deleteVariantsByProductId(
  productId: string,
): Promise<ApiResponse<any>> {
  const response = await apiService.delete<ApiResponse<any>>(
    `${VARIANTS_URL}/product/${productId}`,
  );
  return response.data;
}

// Product Variant Attribute Values API
export async function getVariantAttributeValue(
  id: string,
): Promise<ApiResponse<ProductVariantAttributeValue>> {
  const response = await apiService.get<
    ApiResponse<ProductVariantAttributeValue>
  >(`${VARIANT_ATTRIBUTE_VALUES_URL}/${id}`);
  return response.data;
}

export async function createVariantAttributeValue(
  payload: any,
): Promise<ApiResponse<ProductVariantAttributeValue>> {
  const response = await apiService.post<
    ApiResponse<ProductVariantAttributeValue>
  >(VARIANT_ATTRIBUTE_VALUES_URL, payload);
  return response.data;
}

export async function updateVariantAttributeValue(
  id: string,
  payload: any,
): Promise<ApiResponse<ProductVariantAttributeValue>> {
  const response = await apiService.put<
    ApiResponse<ProductVariantAttributeValue>
  >(`${VARIANT_ATTRIBUTE_VALUES_URL}/${id}`, payload);
  return response.data;
}

export async function deleteVariantAttributeValue(
  id: string,
): Promise<ApiResponse<ProductVariantAttributeValue>> {
  const response = await apiService.delete<
    ApiResponse<ProductVariantAttributeValue>
  >(`${VARIANT_ATTRIBUTE_VALUES_URL}/${id}`);
  return response.data;
}

export async function batchCreateVariantAttributeValues(
  payload: any,
): Promise<ApiResponse<ProductVariantAttributeValue[]>> {
  const response = await apiService.post<
    ApiResponse<ProductVariantAttributeValue[]>
  >(`${VARIANT_ATTRIBUTE_VALUES_URL}/batch`, payload);
  return response.data;
}

export async function getVariantAttributeValuesByVariantId(
  variantId: string,
): Promise<ApiResponse<ProductVariantAttributeValue[]>> {
  const response = await apiService.get<
    ApiResponse<ProductVariantAttributeValue[]>
  >(`${VARIANT_ATTRIBUTE_VALUES_URL}/product-variant/${variantId}`);
  return response.data;
}

export async function deleteVariantAttributeValuesByVariantId(
  variantId: string,
): Promise<ApiResponse<any>> {
  const response = await apiService.delete<ApiResponse<any>>(
    `${VARIANT_ATTRIBUTE_VALUES_URL}/product-variant/${variantId}`,
  );
  return response.data;
}
