import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MODULE } from './config';
import { CrawlBlog } from './type';

const url = `/api/cms/v1/${MODULE}`;

export async function getItems(
  params: any,
): Promise<ApiResponsePaginationCursor<CrawlBlog[]>> {
  const response = await apiService.get<
    ApiResponsePaginationCursor<CrawlBlog[]>
  >(url, { params });
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<CrawlBlog>> {
  const response = await apiService.get<ApiResponse<CrawlBlog>>(`${url}/${id}`);
  return response.data;
}

export async function createItem(
  payload: any,
): Promise<ApiResponse<CrawlBlog>> {
  const response = await apiService.post<ApiResponse<CrawlBlog>>(url, payload);
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<CrawlBlog>> {
  const response = await apiService.put<ApiResponse<CrawlBlog>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(id: string): Promise<ApiResponse<CrawlBlog>> {
  const response = await apiService.delete<ApiResponse<CrawlBlog>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getSearch(
  params: any,
): Promise<ApiResponse<CrawlBlog[]>> {
  const response = await apiService.get<ApiResponse<CrawlBlog[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<CrawlBlog[]>> {
  const response = await apiService.get<ApiResponse<CrawlBlog[]>>(`${url}/all`);
  return response.data;
}
