export enum CrawlBlogStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  DONE = 'DONE',
  ERROR = 'ERROR',
}
// export interface CrawlBlog {
//   _id: string; // Unique identifier for the blog post
//   crawlWebId: string; // Identifier referencing the source website
//   crawlWebCategoryId: string; // Identifier referencing the category of the source website
//   name: string; // Title of the blog post
//   content: string; // Main content of the blog post
//   url: string; // URL of the blog post
//   publishedDate?: Date; // Publication date of the blog post (optional)
//   source?: string; // Source or author of the blog post (optional)
//   tags?: string[]; // Tags associated with the blog post (optional)
//   status: CrawlBlogStatus; // Current status of the blog post
//   createdAt: Date; // Timestamp when the blog post was created
//   updatedAt: Date; // Timestamp when the blog post was last updated
// }

export interface CrawlBlog {
  crawlWebId: string; // Identifier referencing the source website
  crawlWebCategoryId: string; // Identifier referencing the category of the source website
  name: string; // Title of the blog post
  content: string; // Main content of the blog post
  url: string; // URL of the blog post
  source: string; // Source or author of the blog post (optional)
  //tags?: string[]; // Tags associated with the blog post (optional)
}
