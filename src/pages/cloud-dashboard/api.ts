import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MODULE } from './config';
import { CloudDashboard } from './type';

const url = `/api/cms/v1/${MODULE}`;

export async function getItems(
  params: any,
): Promise<ApiResponsePaginationCursor<CrawlUser[]>> {
  const response = await apiService.get<
    ApiResponsePaginationCursor<CrawlUser[]>
  >(url, { params });
  return response.data;
}

export async function getItem(
  id: string,
): Promise<ApiResponse<CloudDashboard>> {
  const response = await apiService.get<ApiResponse<CloudDashboard>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function createItem(
  payload: any,
): Promise<ApiResponse<CloudDashboard>> {
  const response = await apiService.post<ApiResponse<CloudDashboard>>(
    url,
    payload,
  );
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<CloudDashboard>> {
  const response = await apiService.put<ApiResponse<CloudDashboard>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(
  id: string,
): Promise<ApiResponse<CloudDashboard>> {
  const response = await apiService.delete<ApiResponse<CloudDashboard>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getSearch(
  params: any,
): Promise<ApiResponse<CloudDashboard[]>> {
  const response = await apiService.get<ApiResponse<CloudDashboard[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<CloudDashboard[]>> {
  const response = await apiService.get<ApiResponse<CloudDashboard[]>>(
    `${url}/all`,
  );
  return response.data;
}
