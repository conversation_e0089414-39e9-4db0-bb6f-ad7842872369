import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Row } from 'antd';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';

function CloudDashboardSearch(props: any) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    logger('[values]', values);
    props.onChange(values);
  };

  function handlerRefresh() {
    form.resetFields();
    props.onChange({});
  }

  return (
    <Form
      layout="horizontal"
      form={form}
      name="frmSearchCloudDashboard"
      onFinish={onFinish}
      className="p-4 bg-white rounded-lg mb-4"
    >
      <Row className="gap-4">
        <Col xs={24} md={4} lg={4}>
          <Form.Item
            name="key"
            label={t('key')}
            rules={[{ required: false, message: t('inputData') }]}
          >
            <Input placeholder={t('key')} />
          </Form.Item>
        </Col>

        <Col xs={24} md={4} lg={4}>
          <Form.Item
            name="name"
            label={t('name')}
            rules={[{ required: false, message: t('inputData') }]}
          >
            <Input placeholder={t('name')} />
          </Form.Item>
        </Col>

        <Col style={{ justifyContent: 'end' }}>
          <Button
            type="default"
            htmlType="submit"
            style={{ marginRight: '15px' }}
            loading={props.loading}
            icon={<SearchOutlined />}
          >
            {t('search')}
          </Button>

          <Button
            type="default"
            htmlType="button"
            loading={props.loading}
            onClick={handlerRefresh}
            icon={<ReloadOutlined />}
          >
            {t('reset')}
          </Button>
        </Col>
      </Row>
    </Form>
  );
}

export { CloudDashboardSearch };
