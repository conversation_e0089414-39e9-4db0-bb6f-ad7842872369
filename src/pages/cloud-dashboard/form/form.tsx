import { Col, Form, Input, message, Row, Switch } from 'antd';
import _ from 'lodash';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { createItem, getItem, updateItem } from '../api';
import { MODULE } from '../config';
import { CloudDashboard } from '../type';

const FormItem = Form.Item;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const CloudDashboardForm = forwardRef<unknown, IndexFormProps>(
  ({ onChange, id }, ref) => {
    const { t } = useTranslation(MODULE);
    const logger = ConsoleService.register(MODULE);
    const [form] = Form.useForm();
    const [isNew, setIsNew] = useState<boolean>(false);
    const [item, setItem] = useState<CloudDashboard>();
    const [formValues, setFormValues] = useState<CloudDashboard>();

    const initForm = {};

    const getItemData = async (_id: string) => {
      const res = await getItem(_id);
      if (res.status.success) {
        setItem(res.data);
        form.setFieldsValue(res.data);
      } else {
        message.error(res.status.message);
      }
    };

    useEffect(() => {
      logger(id);
      form.resetFields();
      if (['create', undefined].includes(id)) {
        setIsNew(true);
      } else if (id) {
        setIsNew(false);
        getItemData(id);
      }
    }, [id]);

    const onFinish = async (values: CloudDashboard) => {
      try {
        let res;
        if (isNew) {
          res = await createItem(values);
          if (res.status.success) {
            message.success(t('addSuccess'));
          }
        } else {
          res = await updateItem(id!, values);
          if (res.status.success) {
            message.success(t('updateSuccess'));
          }
        }
        if (!res.status.success) {
          message.error(res.status.message);
        } else {
          setItem(res.data);
          form.resetFields();
          onChange(true);
        }
      } catch (error) {
        logger('Error submitting form', error);
        message.error(
          _.get(error, 'response.data.message.0') || t('submitError'),
        );
      }
    };

    const handleValuesChange = (newValue: any, allValues: any) => {
      logger(newValue);
      logger(allValues);
      setFormValues(allValues);
    };

    useImperativeHandle(ref, () => ({
      submitForm: () => form.submit(),
    }));

    return (
      <Form
        form={form}
        name="form"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        initialValues={initForm}
        onValuesChange={handleValuesChange}
      >
        <div className="form_content">
          <Row gutter={16}>
            <Col xs={24} lg={24}>
              <FormItem
                label={t('name')}
                name="name"
                rules={[{ required: true, message: t('pleaseEnterData') }]}
              >
                <Input />
              </FormItem>
            </Col>
            <Col xs={24} lg={24}>
              <FormItem
                label={t('code')}
                name="code"
                rules={[{ required: true, message: t('pleaseEnterData') }]}
              >
                <Input />
              </FormItem>
            </Col>
            <Col xs={24} lg={24}>
              <FormItem
                label={t('active')}
                name="active"
                rules={[{ required: true, message: t('pleaseEnterData') }]}
              >
                <Switch
                  checkedChildren={t('statusActive')}
                  unCheckedChildren={t('statusInactive')}
                />
              </FormItem>
            </Col>
          </Row>
        </div>
      </Form>
    );
  },
);
CloudDashboardForm.displayName = 'CloudDashboardForm';

export { CloudDashboardForm };
