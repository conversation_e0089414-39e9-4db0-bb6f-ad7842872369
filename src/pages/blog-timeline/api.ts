import {
  ApiResponse,
  ApiResponsePagination,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import {
  BatchPostResult,
  BlogTimeline,
  BlogTimelinePost,
  PostOrder,
  ReorderResult,
} from './type';

const url = `/api/cms/v1/blog/blog-timelines`;

export async function getItems(
  params: any,
): Promise<ApiResponsePagination<BlogTimeline[]>> {
  const response = await apiService.get<ApiResponsePagination<BlogTimeline[]>>(
    url,
    { params },
  );
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<BlogTimeline>> {
  const response = await apiService.get<ApiResponse<BlogTimeline>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function createItem(
  payload: any,
): Promise<ApiResponse<BlogTimeline>> {
  const response = await apiService.post<ApiResponse<BlogTimeline>>(
    url,
    payload,
  );
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<BlogTimeline>> {
  const response = await apiService.put<ApiResponse<BlogTimeline>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(
  id: string,
): Promise<ApiResponse<BlogTimeline>> {
  const response = await apiService.delete<ApiResponse<BlogTimeline>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getSearch(
  params: any,
): Promise<ApiResponse<BlogTimeline[]>> {
  const response = await apiService.get<ApiResponse<BlogTimeline[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<BlogTimeline[]>> {
  const response = await apiService.get<ApiResponse<BlogTimeline[]>>(
    `${url}/all`,
  );
  return response.data;
}

// Blog Timeline Posts API
export async function getTimelinePosts(
  timelineId: string,
  params?: {
    page?: number;
    limit?: number;
    sort_by?: string;
    sort_order?: string;
  },
): Promise<ApiResponsePagination<BlogTimelinePost[]>> {
  const response = await apiService.get<
    ApiResponsePagination<BlogTimelinePost[]>
  >(`${url}/${timelineId}/posts`, { params });
  return response.data;
}

export async function addPostToTimeline(
  timelineId: string,
  payload: { post_id: number; priority: number },
): Promise<ApiResponse<BlogTimelinePost>> {
  const response = await apiService.post<ApiResponse<BlogTimelinePost>>(
    `${url}/${timelineId}/posts`,
    payload,
  );
  return response.data;
}

export async function removePostFromTimeline(
  timelineId: string,
  postId: string,
): Promise<ApiResponse<{ message: string }>> {
  const response = await apiService.delete<ApiResponse<{ message: string }>>(
    `${url}/${timelineId}/posts/${postId}`,
  );
  return response.data;
}

export async function reorderPosts(
  timelineId: string,
  payload: { post_orders: PostOrder[] },
): Promise<ApiResponse<ReorderResult>> {
  const response = await apiService.put<ApiResponse<ReorderResult>>(
    `${url}/${timelineId}/posts/reorder`,
    payload,
  );
  return response.data;
}

export async function batchAddPosts(
  timelineId: string,
  payload: { post_orders: PostOrder[] },
): Promise<ApiResponse<BatchPostResult>> {
  const response = await apiService.post<ApiResponse<BatchPostResult>>(
    `${url}/${timelineId}/posts/batch`,
    payload,
  );
  return response.data;
}

// Frontend API
export async function getTimelineByCode(
  code: string,
): Promise<ApiResponse<BlogTimeline & { posts: BlogTimelinePost[] }>> {
  const response = await apiService.get<
    ApiResponse<BlogTimeline & { posts: BlogTimelinePost[] }>
  >(`/api/v1/blog/blog-timelines/${code}`);
  return response.data;
}
