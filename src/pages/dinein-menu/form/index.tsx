import { Spin } from 'antd';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { getItem } from '../api';
import useMenuStore from '../store';
import { MenuItem } from '../type';
import MenuForm from './form';

export default function FormPage() {
  const { id } = useParams();
  const { loading, setLoading } = useMenuStore();
  const [item, setItem] = useState<MenuItem | undefined>();

  const isUpdate = !!id;

  useEffect(() => {
    async function fetchData() {
      if (id) {
        setLoading(true);
        try {
          const response = await getItem(id);
          if (response.status.success) {
            setItem(response.data);
          }
        } catch (error) {
          console.error('Error fetching menu item:', error);
        } finally {
          setLoading(false);
        }
      }
    }

    fetchData();
  }, [id, setLoading]);

  // Show spinner while loading for edit mode
  if (loading && isUpdate && !item) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return <MenuForm id={id} initialValues={item} />;
}
