import { <PERSON><PERSON>, Spin } from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getItem, MODULE } from '../api';
import useMenuStore from '../store';
import { MenuItem } from '../type';
import { MenuForm } from './form';

interface ModalFormProps {
  showModal: boolean;
  onChange: () => void;
  id?: string;
}

export default function ModalForm({ showModal, onChange, id }: ModalFormProps) {
  const { t } = useTranslation(MODULE);
  const [item, setItem] = useState<MenuItem | undefined>();
  const { loading, setLoading } = useMenuStore();

  const isUpdate = !!id;

  const handleCancel = () => {
    onChange();
    setItem(undefined);
  };

  useEffect(() => {
    async function fetchData() {
      if (id && showModal) {
        setLoading(true);
        try {
          const response = await getItem(id);
          if (response.status.success) {
            setItem(response.data);
          }
        } catch (error) {
          console.error('Error fetching menu item:', error);
        } finally {
          setLoading(false);
        }
      }
    }

    fetchData();
  }, [id, showModal, setLoading]);

  const handleSuccess = () => {
    onChange();
    setItem(undefined);
  };

  return (
    <Modal
      title={isUpdate ? 'Chỉnh sửa mục thực đơn' : 'Thêm mục thực đơn mới'}
      open={showModal}
      onCancel={handleCancel}
      footer={null}
      destroyOnClose
      width={800}
    >
      {loading && !item && isUpdate ? (
        <div className="text-center py-8">
          <Spin size="large" />
        </div>
      ) : (
        <MenuForm id={id} initialValues={item} onSuccess={handleSuccess} />
      )}
    </Modal>
  );
}
