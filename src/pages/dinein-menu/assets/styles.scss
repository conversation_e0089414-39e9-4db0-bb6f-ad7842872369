.menu {
  &-tree {
    &-container {
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      padding: 8px;
      background-color: #fafafa;
    }
  }

  &-item {
    margin-bottom: 8px;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &-content {
      display: flex;
      align-items: center;
    }

    &-dragging {
      opacity: 0.7;
    }
  }

  &-children {
    margin-top: 8px;
  }
}

.drag-handle {
  padding: 8px;
  margin-right: 8px;
  cursor: grab;
  color: #999;

  &:hover {
    color: #1890ff;
  }
}
