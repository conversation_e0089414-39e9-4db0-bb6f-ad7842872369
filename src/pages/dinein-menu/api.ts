import {
  ApiResponse,
  ApiResponsePagination,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MenuItem } from './type';

const url = `/api/cms/v1/dinein/menu`;

export async function getItems(
  params: any,
): Promise<ApiResponsePagination<MenuItem[]>> {
  const response = await apiService.get<ApiResponsePagination<MenuItem[]>>(
    url,
    { params },
  );
  return response.data;
}

export async function getTree(
  params?: any,
): Promise<ApiResponsePagination<MenuItem[]>> {
  const response = await apiService.get<ApiResponsePagination<MenuItem[]>>(
    `${url}/tree`,
    { params },
  );
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<MenuItem>> {
  const response = await apiService.get<ApiResponse<MenuItem>>(`${url}/${id}`);
  return response.data;
}

export async function createItem(payload: any): Promise<ApiResponse<MenuItem>> {
  const response = await apiService.post<ApiResponse<MenuItem>>(url, payload);
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<MenuItem>> {
  const response = await apiService.put<ApiResponse<MenuItem>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(id: string): Promise<ApiResponse<MenuItem>> {
  const response = await apiService.delete<ApiResponse<MenuItem>>(
    `${url}/${id}`,
  );
  return response.data;
}

// Di chuyển menu item vào menu item cha khác và đặt ở vị trí cụ thể
export async function moveMenuItem(payload: {
  menu_id: number;
  new_parent_id: number;
  position: number;
}): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${url}/move-node`,
    payload,
  );
  return response.data;
}

// Di chuyển menu item thành node gốc (root) và đặt ở vị trí cụ thể
export async function moveMenuItemRoot(payload: {
  menu_id: number;
  position: number;
}): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${url}/move-node-root`,
    payload,
  );
  return response.data;
}

// Cập nhật vị trí của menu item trong cùng một menu item cha
export async function updateMenuPosition(payload: {
  menu_id: number;
  target_id: number;
}): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${url}/update-position`,
    payload,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getSearch(params: any): Promise<ApiResponse<MenuItem[]>> {
  const response = await apiService.get<ApiResponse<MenuItem[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<MenuItem[]>> {
  const response = await apiService.get<ApiResponse<MenuItem[]>>(`${url}/all`);
  return response.data;
}
