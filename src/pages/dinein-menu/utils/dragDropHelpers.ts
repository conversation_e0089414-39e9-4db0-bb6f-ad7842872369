import { MenuItem, TreeItem } from '../type';

// Flatten the tree structure into a flat array
export const flattenTree = (items: MenuItem[]): MenuItem[] => {
  let result: MenuItem[] = [];

  items.forEach((item) => {
    result.push(item);
    if (item.children && item.children.length > 0) {
      result = [...result, ...flattenTree(item.children)];
    }
  });

  return result;
};

// Convert MenuItem array to TreeItem structure for react-beautiful-dnd
export const convertToTreeItems = (items: MenuItem[]): TreeItem[] => {
  return items.map((item) => ({
    id: `item-${item.id}`,
    content: item.name,
    children: item.children ? convertToTreeItems(item.children) : [],
  }));
};

// Find a menu item by ID in the tree
export const findItemById = (
  items: MenuItem[],
  id: number,
): MenuItem | null => {
  for (const item of items) {
    if (item.id === id) {
      return item;
    }
    if (item.children && item.children.length > 0) {
      const found = findItemById(item.children, id);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

// Find parent of a menu item by ID
export const findParentById = (
  items: MenuItem[],
  id: number,
): MenuItem | null => {
  for (const item of items) {
    if (item.children && item.children.some((child) => child.id === id)) {
      return item;
    }
    if (item.children && item.children.length > 0) {
      const found = findParentById(item.children, id);
      if (found) {
        return found;
      }
    }
  }
  return null;
};
