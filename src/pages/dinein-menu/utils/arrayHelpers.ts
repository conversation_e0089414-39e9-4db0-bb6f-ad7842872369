import { MenuItem } from '../type';

// Find a menu item by ID in a flat array
export const findById = (
  items: MenuItem[],
  id: number,
): MenuItem | undefined => {
  return items.find((item) => item.id === id);
};

// Filter menu items by status
export const filterByStatus = (
  items: MenuItem[],
  status: string,
): MenuItem[] => {
  return items.filter((item) => item.status === status);
};

// Find all child items for a given parent ID
export const findChildren = (
  items: MenuItem[],
  parentId: number,
): MenuItem[] => {
  return items.filter((item) => item.parent_id === parentId);
};

// Calculate total menu items
export const countTotalItems = (items: MenuItem[]): number => {
  let count = items.length;

  items.forEach((item) => {
    if (item.children && item.children.length > 0) {
      count += countTotalItems(item.children);
    }
  });

  return count;
};

// Generate breadcrumb path for a menu item
export const generateBreadcrumb = (
  items: MenuItem[],
  itemId: number,
  path: MenuItem[] = [],
): MenuItem[] | null => {
  // Find the item in the top level
  const item = items.find((item) => item.id === itemId);

  if (item) {
    return [...path, item];
  }

  // Look in children
  for (const parentItem of items) {
    if (parentItem.children && parentItem.children.length > 0) {
      const childPath = generateBreadcrumb(parentItem.children, itemId, [
        ...path,
        parentItem,
      ]);

      if (childPath) {
        return childPath;
      }
    }
  }

  return null;
};
