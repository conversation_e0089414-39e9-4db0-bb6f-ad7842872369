export const STATUS = [
  {
    label: 'Chờ duyệt',
    value: 'pending',
    color: 'orange',
  },
  {
    label: 'Đang phục vụ',
    value: 'active',
    color: 'green',
  },
  {
    label: 'Ngừng phục vụ',
    value: 'inactive',
    color: 'red',
  },
];

export interface MenuItem {
  id: number;
  tenant_id: number;
  parent_id: number | null;
  name: string;
  slug: string;
  description: string;
  image: string;
  price: number;
  sale_price: number | null;
  lft: number;
  rgt: number;
  depth: number;
  position: number;
  is_active: boolean;
  is_featured: boolean;
  is_available: boolean;
  estimated_cooking_time: number; // in minutes
  status: 'pending' | 'active' | 'inactive';
  created_at: string;
  updated_at: string;
  created_by: number;
  updated_by: number;
  children?: MenuItem[];
  dish_count?: number;
}

export interface MenuItemCreate {
  parent_id?: number | null;
  name: string;
  slug?: string;
  description?: string;
  image?: string;
  price?: number;
  sale_price?: number | null;
  position?: number;
  is_active?: boolean;
  is_featured?: boolean;
  is_available?: boolean;
  estimated_cooking_time?: number;
  status?: string;
}

export interface MenuItemUpdate {
  parent_id?: number | null;
  name?: string;
  slug?: string;
  description?: string;
  image?: string;
  price?: number;
  sale_price?: number | null;
  position?: number;
  is_active?: boolean;
  is_featured?: boolean;
  is_available?: boolean;
  estimated_cooking_time?: number;
  status?: string;
}

// Yêu cầu di chuyển menu item vào menu item cha khác
export interface MoveNodeRequest {
  menu_id: number;
  new_parent_id: number;
  position: number;
}

// Yêu cầu cập nhật vị trí của menu item
export interface UpdatePositionRequest {
  menu_id: number;
  target_id: number; // ID của menu item làm mốc, node sẽ đứng TRƯỚC node này
}

// Định nghĩa kiểu dữ liệu cho cây menu sử dụng trong react-beautiful-dnd
export interface TreeItem {
  id: string;
  content: string;
  children: TreeItem[];
}
