import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { MODULE } from './config';

interface MenuState {
  loading: boolean;
  setLoading: (loading: boolean) => void;
  selectedMenu: any;
  setSelectedMenu: (menu: any) => void;
}

const useMenuStore = create<MenuState>()(
  devtools(
    (set) => ({
      loading: false,
      setLoading: (loading: boolean) => set({ loading }),
      selectedMenu: null,
      setSelectedMenu: (menu: any) => set({ selectedMenu: menu }),
    }),
    {
      name: `${MODULE}-store`,
    },
  ),
);

export default useMenuStore;
