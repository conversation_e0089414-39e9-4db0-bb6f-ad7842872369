import { Button, Col, Form, Input, message, Row, Select, Switch } from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { SelectLocationCountry } from '../../location-country/components';
import { createItem, getItem, updateItem } from '../api';
import { MODULE } from '../config';
import useBlogStore from '../store';
import { CloudCloudflare } from '../type';

const FormItem = Form.Item;
const { Option } = Select;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const IndexForm: React.FC<IndexFormProps> = ({ onChange, id }) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const { loading } = useBlogStore();
  const [form] = Form.useForm();
  const [isNew, setIsNew] = useState<boolean>(false);
  const [item, setItem] = useState<CloudCloudflare>();
  const [formValues, setFormValues] = useState<CloudCloudflare>();

  const initForm = {};

  const getItemData = async (_id: string) => {
    const res = await getItem(_id);
    if (res.status.success) {
      setItem(res.data);
      form.setFieldsValue(res.data);
    } else {
      message.error(res.status.message);
    }
  };

  useEffect(() => {
    if (['create', undefined].includes(id)) {
      setIsNew(true);
    } else if (id) {
      setIsNew(false);
      getItemData(id);
    }
  }, [id]);

  const onFinish = async (values: CloudCloudflare) => {
    try {
      let res;
      if (isNew) {
        res = await createItem(values);
        if (res.status.success) {
          message.success(t('addSuccess'));
        }
      } else {
        res = await updateItem(id!, values);
        if (res.status.success) {
          message.success(t('updateSuccess'));
        }
      }
      if (!res.status.success) {
        message.error(res.status.message);
      } else {
        setItem(res.data);
        form.resetFields();
        onChange(true);
      }
    } catch (error) {
      logger('Error submitting form', error);
      message.error(
        _.get(error, 'response.data.message.0') || t('submitError'),
      );
    }
  };
  const handleValuesChange = (newValue: any, allValues: any) => {
    logger(newValue);
    logger(allValues);
    setFormValues(allValues);
  };
  return (
    <Form
      style={{ marginTop: 8 }}
      form={form}
      name="form"
      layout="vertical"
      onFinish={onFinish}
      autoComplete="off"
      initialValues={initForm}
      onValuesChange={handleValuesChange}
    >
      <div className="form_content">
        <Row gutter={16}>
          <Col xs={24} lg={24}>
            <FormItem
              label={t('country')}
              name="country"
              rules={[{ required: true, message: t('pleaseEnterData') }]}
            >
              <SelectLocationCountry />
            </FormItem>
          </Col>

          <Col xs={24} lg={24}>
            <FormItem
              label={t('name')}
              name="name"
              rules={[{ required: true, message: t('pleaseEnterData') }]}
            >
              <Input />
            </FormItem>
          </Col>
          <Col xs={24} lg={24}>
            <FormItem
              label={t('code')}
              name="code"
              rules={[{ required: true, message: t('pleaseEnterData') }]}
            >
              <Input />
            </FormItem>
          </Col>

          <Col xs={24} lg={24}>
            <FormItem
              label={t('active')}
              name="active"
              rules={[{ required: true, message: t('pleaseEnterData') }]}
            >
              <Switch
                checkedChildren={t('statusActive')}
                unCheckedChildren={t('statusDeActive')}
              />
            </FormItem>
          </Col>
        </Row>
      </div>
      <div className="form_footer">
        <FormItem>
          <Button type="primary" htmlType="submit" loading={loading}>
            {isNew ? t('btnAdd') : t('btnUpdate')}
          </Button>
        </FormItem>
      </div>
    </Form>
  );
};

export default IndexForm;
