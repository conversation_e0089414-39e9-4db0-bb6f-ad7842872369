import {
  ApiResponse,
  ApiResponsePagination,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MODULE } from './config';
import { CloudCloudflare } from './type';

const url = `/api/cms/v1/${MODULE}`;

export async function getItems(
  params: any,
): Promise<ApiResponsePagination<CloudCloudflare[]>> {
  const response = await apiService.get<
    ApiResponsePagination<CloudCloudflare[]>
  >(url, { params });
  return response.data;
}

export async function getItem(
  id: string,
): Promise<ApiResponse<CloudCloudflare>> {
  const response = await apiService.get<ApiResponse<CloudCloudflare>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function createItem(
  payload: any,
): Promise<ApiResponse<CloudCloudflare>> {
  const response = await apiService.post<ApiResponse<CloudCloudflare>>(
    url,
    payload,
  );
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<CloudCloudflare>> {
  const response = await apiService.put<ApiResponse<CloudCloudflare>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(
  id: string,
): Promise<ApiResponse<CloudCloudflare>> {
  const response = await apiService.delete<ApiResponse<CloudCloudflare>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getSearch(
  params: any,
): Promise<ApiResponse<CloudCloudflare[]>> {
  const response = await apiService.get<ApiResponse<CloudCloudflare[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<CloudCloudflare[]>> {
  const response = await apiService.get<ApiResponse<CloudCloudflare[]>>(
    `${url}/all`,
  );
  return response.data;
}
