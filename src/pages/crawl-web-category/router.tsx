import React from 'react';
import { Route, Routes } from 'react-router-dom';
import { CrawlWebCategoryList } from './list';
import { CrawlWebCategoryPage } from './form';

const CrawlWebCategoryRouter: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<CrawlWebCategoryList />} />
      <Route path="/create" element={<CrawlWebCategoryPage />} />
    </Routes>
  );
};

export { CrawlWebCategoryRouter };
