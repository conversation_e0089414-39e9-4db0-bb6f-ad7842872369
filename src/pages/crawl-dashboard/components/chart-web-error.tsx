import React, { useEffect, useState } from 'react';
import { Column, Line } from '@ant-design/plots';
import { getWebError } from '../api';

export const ChartWebError = () => {
  const [data, setData] = useState<any>([]);

  const getData = async () => {
    const res = await getWebError();
    console.log(res);
    setData(res.data);
  };

  useEffect(() => {
    getData();
  }, []);

  const config = {
    data: data,
    //xField: (d) => new Date(d.date),
    // yField: 'count',
    // sizeField: 'count',
    xField: 'domain',
    yField: 'count',
    // shapeField: 'trail',
    //legend: { size: false },
    //colorField: 'domain',
    // scrollbar: {
    //   x: {
    //     ratio: 0.05,
    //   },
    // },
  };
  return <Column {...config} />;
};
