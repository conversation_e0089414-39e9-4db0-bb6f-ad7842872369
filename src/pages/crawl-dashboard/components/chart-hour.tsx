import React, { useEffect, useState } from 'react';
import { Line } from '@ant-design/plots';
import { getAnalyticsWebGroupDate } from '../api';

export const ChartHour = () => {
  const [data, setData] = useState<any>([]);

  const getData = async () => {
    const res = await getAnalyticsWebGroupDate();
    //console.log(res);
    setData(res.data);
  };

  useEffect(() => {
    getData();
  }, []);
  const config = {
    data: data,
    xField: (d) => new Date(d.date),
    yField: 'count',
    sizeField: 'count',
    shapeField: 'trail',
    legend: { size: false },
    colorField: 'domain',
  };
  return <Line {...config} />;
};
