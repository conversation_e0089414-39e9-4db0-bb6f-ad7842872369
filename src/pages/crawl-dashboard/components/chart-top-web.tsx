import { Column } from '@ant-design/plots';
import { useEffect, useRef, useState } from 'react';
import { getAnalyticsWeb } from '../api';

export const ChartTopWeb = () => {
  const chartRef = useRef();
  const [data, setData] = useState<any>([]);

  const getData = async () => {
    const res = await getAnalyticsWeb();
    console.log(res);
    setData(res.data);
  };

  useEffect(() => {
    console.log({ chartRef });
    getData();
    if (chartRef.current) {
      // Khối mã trống
    }
  }, []);

  const config = {
    data,
    xField: 'domain',
    yField: 'count',
    // slider: {
    //   x: {
    //     values: [0.1, 0.2],
    //   },
    // },
  };
  return <Column {...config} ref={chartRef} />;
};
