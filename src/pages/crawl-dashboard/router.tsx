import React from 'react';
import { Route, Routes } from 'react-router-dom';
import { CrawlDashboardList } from './list';
import { CrawlDashboardPage } from './form';

const CrawlDashboardRouter: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<CrawlDashboardList />} />
      <Route path="/create" element={<CrawlDashboardPage />} />
    </Routes>
  );
};

export { CrawlDashboardRouter };
