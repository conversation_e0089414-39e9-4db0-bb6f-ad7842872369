import { Card } from 'antd';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import '../assets/styles.scss';
import { ChartHour, ChartTopWeb, ChartWebError } from '../components';
import { MODULE } from '../config';

function CrawlDashboardList() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  return (
    <div className="">
      <Card
        bordered={false}
        key="source"
        title="Top Web"
        className="bg-white mb-4"
      >
        <ChartTopWeb></ChartTopWeb>
      </Card>
      <Card
        bordered={false}
        key="source2"
        title="Theo giờ"
        className="bg-white mb-4"
      >
        <ChartHour></ChartHour>
      </Card>
      <Card
        bordered={false}
        key="source3"
        title="Web Error"
        className="bg-white mb-4"
      >
        <ChartWebError></ChartWebError>
      </Card>
    </div>
  );
}
export { CrawlDashboardList };
