import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MODULE } from './config';
import { CrawlDashboard } from './type';

const url = `/api/cms/v1/${MODULE}`;

export async function getAnalyticsWeb(
  params?: any,
): Promise<ApiResponse<any[]>> {
  const response = await apiService.get<ApiResponse<any[]>>(`${url}/web`, {
    params,
  });
  return response.data;
}

export async function getAnalyticsWebGroupDate(
  params?: any,
): Promise<ApiResponse<any[]>> {
  const response = await apiService.get<ApiResponse<any[]>>(
    `${url}/group-date`,
    { params },
  );
  return response.data;
}

export async function getWebError(params?: any): Promise<ApiResponse<any[]>> {
  const response = await apiService.get<ApiResponse<any[]>>(
    `${url}/web-error`,
    { params },
  );
  return response.data;
}

export async function getItems(
  params: any,
): Promise<ApiResponsePaginationCursor<any[]>> {
  const response = await apiService.get<ApiResponsePaginationCursor<any[]>>(
    url,
    { params },
  );
  return response.data;
}

export async function getItem(
  id: string,
): Promise<ApiResponse<CrawlDashboard>> {
  const response = await apiService.get<ApiResponse<CrawlDashboard>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function createItem(
  payload: any,
): Promise<ApiResponse<CrawlDashboard>> {
  const response = await apiService.post<ApiResponse<CrawlDashboard>>(
    url,
    payload,
  );
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<CrawlDashboard>> {
  const response = await apiService.put<ApiResponse<CrawlDashboard>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(
  id: string,
): Promise<ApiResponse<CrawlDashboard>> {
  const response = await apiService.delete<ApiResponse<CrawlDashboard>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getSearch(
  params: any,
): Promise<ApiResponse<CrawlDashboard[]>> {
  const response = await apiService.get<ApiResponse<CrawlDashboard[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<CrawlDashboard[]>> {
  const response = await apiService.get<ApiResponse<CrawlDashboard[]>>(
    `${url}/all`,
  );
  return response.data;
}
