// filepath: /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/web/cms/src/pages/dinein-product-topping/api.ts
import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import {
  DineinTopping,
  DineinToppingGroup,
  DineinToppingGroupItem,
} from './type';

export const MODULE = 'dinein-product-topping';
export const MODULE_NAME = 'Topping dùng tại chỗ';
export const MODULE_POPUP = false;

const toppingUrl = `/api/cms/v1/dinein/toppings`;
const categoryUrl = `/api/cms/v1/dinein/categories`;
const toppingGroupUrl = `/api/cms/v1/dinein/topping-groups`;
const toppingGroupItemUrl = `/api/cms/v1/dinein/topping-group-items`;

// Topping APIs
export async function getToppings(
  params: any,
): Promise<ApiResponsePaginationCursor<DineinTopping[]>> {
  const response = await apiService.get<
    ApiResponsePaginationCursor<DineinTopping[]>
  >(toppingUrl, params);
  return response.data;
}

export async function getTopping(
  id: string,
): Promise<ApiResponse<DineinTopping>> {
  const response = await apiService.get<ApiResponse<DineinTopping>>(
    `${toppingUrl}/${id}`,
  );
  return response.data;
}

export async function createTopping(
  payload: any,
): Promise<ApiResponse<DineinTopping>> {
  const response = await apiService.post<ApiResponse<DineinTopping>>(
    toppingUrl,
    payload,
  );
  return response.data;
}

export async function updateTopping(
  id: string,
  payload: any,
): Promise<ApiResponse<DineinTopping>> {
  const response = await apiService.put<ApiResponse<DineinTopping>>(
    `${toppingUrl}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteTopping(
  id: string,
): Promise<ApiResponse<DineinTopping>> {
  const response = await apiService.delete<ApiResponse<DineinTopping>>(
    `${toppingUrl}/${id}`,
  );
  return response.data;
}

export async function getToppingsByCategory(
  categoryId: string,
): Promise<ApiResponse<DineinTopping[]>> {
  const response = await apiService.get<ApiResponse<DineinTopping[]>>(
    `${toppingUrl}/by-category/${categoryId}`,
  );
  return response.data;
}

export async function getCategoryTree(): Promise<ApiResponse<any[]>> {
  const response = await apiService.get<ApiResponse<any[]>>(
    `${categoryUrl}/tree`,
  );
  return response.data;
}

// Topping Group APIs
export async function getToppingGroups(
  params: any,
): Promise<ApiResponsePaginationCursor<DineinToppingGroup[]>> {
  const response = await apiService.get<
    ApiResponsePaginationCursor<DineinToppingGroup[]>
  >(toppingGroupUrl, params);
  return response.data;
}

export async function getToppingGroup(
  id: string,
): Promise<ApiResponse<DineinToppingGroup>> {
  const response = await apiService.get<ApiResponse<DineinToppingGroup>>(
    `${toppingGroupUrl}/${id}`,
  );
  return response.data;
}

export async function createToppingGroup(
  payload: any,
): Promise<ApiResponse<DineinToppingGroup>> {
  const response = await apiService.post<ApiResponse<DineinToppingGroup>>(
    toppingGroupUrl,
    payload,
  );
  return response.data;
}

export async function updateToppingGroup(
  id: string,
  payload: any,
): Promise<ApiResponse<DineinToppingGroup>> {
  const response = await apiService.put<ApiResponse<DineinToppingGroup>>(
    `${toppingGroupUrl}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteToppingGroup(
  id: string,
): Promise<ApiResponse<DineinToppingGroup>> {
  const response = await apiService.delete<ApiResponse<DineinToppingGroup>>(
    `${toppingGroupUrl}/${id}`,
  );
  return response.data;
}

// Topping Group Item APIs
export async function getToppingGroupItems(
  groupId: string,
): Promise<ApiResponse<DineinToppingGroupItem[]>> {
  const response = await apiService.get<ApiResponse<DineinToppingGroupItem[]>>(
    `${toppingGroupItemUrl}/by-group/${groupId}`,
  );
  return response.data;
}

export async function createToppingGroupItem(
  payload: any,
): Promise<ApiResponse<DineinToppingGroupItem>> {
  const response = await apiService.post<ApiResponse<DineinToppingGroupItem>>(
    toppingGroupItemUrl,
    payload,
  );
  return response.data;
}

export async function updateToppingGroupItem(
  id: string,
  payload: any,
): Promise<ApiResponse<DineinToppingGroupItem>> {
  const response = await apiService.put<ApiResponse<DineinToppingGroupItem>>(
    `${toppingGroupItemUrl}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteToppingGroupItem(
  id: string,
): Promise<ApiResponse<DineinToppingGroupItem>> {
  const response = await apiService.delete<ApiResponse<DineinToppingGroupItem>>(
    `${toppingGroupItemUrl}/${id}`,
  );
  return response.data;
}
