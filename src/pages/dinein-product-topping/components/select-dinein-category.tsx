// filepath: /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/web/cms/src/pages/dinein-product-topping/components/select-dinein-category.tsx
import React, { useEffect, useState } from 'react';
import { Select, Spin } from 'antd';
import { SelectProps } from 'antd/lib/select';
import { useTranslation } from 'react-i18next';
import { getCategoryTree } from '../api';

interface CategoryOption {
  value: number;
  label: string;
  children?: CategoryOption[];
}

interface SelectDineinCategoryProps extends Omit<SelectProps<any>, 'options'> {
  value?: number;
  onChange?: (value: number) => void;
  placeholder?: string;
}

const SelectDineinCategory: React.FC<SelectDineinCategoryProps> = ({
  value,
  onChange,
  placeholder,
  ...props
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<CategoryOption[]>([]);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    setLoading(true);
    try {
      const response = await getCategoryTree();
      if (response.data) {
        const transformCategories = (categories: any[]): CategoryOption[] => {
          return categories.map((category) => ({
            value: category.id,
            label: category.name,
            children: category.children
              ? transformCategories(category.children)
              : undefined,
          }));
        };
        setOptions(transformCategories(response.data));
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (selectedValue: number) => {
    if (onChange) {
      onChange(selectedValue);
    }
  };

  return (
    <Select
      value={value}
      onChange={handleChange}
      placeholder={
        placeholder || t('dinein-product-topping:form.categoryPlaceholder')
      }
      allowClear
      showSearch
      notFoundContent={loading ? <Spin size="small" /> : null}
      filterOption={(input, option) =>
        (option?.label as string).toLowerCase().indexOf(input.toLowerCase()) >=
        0
      }
      options={options}
      {...props}
    />
  );
};

export default SelectDineinCategory;
