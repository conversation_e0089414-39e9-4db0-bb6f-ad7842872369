// filepath: /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/web/cms/src/pages/dinein-product-topping/config.ts
export const ROUTES = {
  LIST: '/dinein-product-topping',
  DETAIL: '/dinein-product-topping/:id',
  CREATE: '/dinein-product-topping/create',
  EDIT: '/dinein-product-topping/edit/:id',
};

export const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

export const tailFormItemLayout = {
  wrapperCol: {
    xs: {
      span: 24,
      offset: 0,
    },
    sm: {
      span: 18,
      offset: 6,
    },
  },
};
