import React from 'react';
import { Card } from 'antd';
import { useTranslation } from 'react-i18next';

import ToppingForm from '.';

const ToppingStandalone: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div>
      <h2>{t('dinein-product-topping:title')}</h2>
      <Card bordered={false}>
        <ToppingForm />
      </Card>
    </div>
  );
};

export default ToppingStandalone;
