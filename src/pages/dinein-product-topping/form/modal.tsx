import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Modal,
  message,
  Spin,
  InputNumber,
  Switch,
  Select,
  Upload,
} from 'antd';
import { useTranslation } from 'react-i18next';
import { UploadOutlined } from '@ant-design/icons';

import { DineinTopping } from '../type';
import { createTopping, updateTopping, getCategoryTree } from '../api';
import { UploadFile } from 'antd/lib/upload/interface';

const { TextArea } = Input;

interface ToppingModalFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  initialData?: DineinTopping;
}

interface CategoryOption {
  value: number;
  label: string;
  children?: CategoryOption[];
}

const ToppingModalForm: React.FC<ToppingModalFormProps> = ({
  visible,
  onCancel,
  onSuccess,
  initialData,
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [categoryOptions, setCategoryOptions] = useState<CategoryOption[]>([]);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const isEdit = !!initialData?.topping_id;

  useEffect(() => {
    if (visible) {
      fetchCategories();
      if (isEdit && initialData) {
        form.setFieldsValue({
          name: initialData.name,
          description: initialData.description,
          price: initialData.price,
          category_id: initialData.category_id,
          is_active: initialData.is_active,
        });

        if (initialData.image_url) {
          setFileList([
            {
              uid: '-1',
              name: 'image.png',
              status: 'done',
              url: initialData.image_url,
            },
          ]);
        } else {
          setFileList([]);
        }
      } else {
        form.resetFields();
        setFileList([]);
      }
    }
  }, [visible, initialData, isEdit, form]);

  const fetchCategories = async () => {
    try {
      const response = await getCategoryTree();
      if (response.data) {
        const transformCategories = (categories: any[]): CategoryOption[] => {
          return categories.map((category) => ({
            value: category.id,
            label: category.name,
            children: category.children
              ? transformCategories(category.children)
              : undefined,
          }));
        };
        setCategoryOptions(transformCategories(response.data));
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      message.error(t('common:messages.fetchFailed'));
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // Handle image upload
      let imageUrl = undefined;
      if (fileList.length > 0 && fileList[0].originFileObj) {
        // In a real app, you would use a proper upload service here
        // This is just a placeholder for demonstration
        imageUrl = await uploadImage(fileList[0].originFileObj);
      } else if (fileList.length > 0 && fileList[0].url) {
        imageUrl = fileList[0].url;
      }

      const toppingData: DineinTopping = {
        ...values,
        image_url: imageUrl,
      };

      if (isEdit && initialData?.topping_id) {
        await updateTopping(String(initialData.topping_id), toppingData);
        message.success(t('common:messages.updateSuccess'));
      } else {
        await createTopping(toppingData);
        message.success(t('common:messages.createSuccess'));
      }
      onSuccess();
    } catch (error) {
      if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error(
          isEdit
            ? t('common:messages.updateFailed')
            : t('common:messages.createFailed'),
        );
      }
    } finally {
      setLoading(false);
    }
  };

  // Mock function for image upload - in a real app this would upload to your server or CDN
  const uploadImage = async (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = () => {
        resolve(reader.result as string);
      };
      reader.readAsDataURL(file);
    });
  };

  const normFile = (e: any) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  const beforeUpload = (file: File) => {
    const isImage = file.type.indexOf('image/') === 0;
    if (!isImage) {
      message.error(t('common:upload.onlyImages'));
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error(t('common:upload.lessThan2MB'));
    }
    return isImage && isLt2M;
  };

  const handleChange = ({ fileList }: any) => {
    setFileList(fileList);
  };

  return (
    <Modal
      title={
        isEdit
          ? t('dinein-product-topping:edit.title')
          : t('dinein-product-topping:create.title')
      }
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          {t('common:buttons.cancel')}
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          {isEdit ? t('common:buttons.update') : t('common:buttons.create')}
        </Button>,
      ]}
      width={700}
    >
      <Spin spinning={loading}>
        <Form form={form} layout="vertical" initialValues={{ is_active: true }}>
          <Form.Item
            name="name"
            label={t('dinein-product-topping:form.name')}
            rules={[
              {
                required: true,
                message: t('dinein-product-topping:validation.nameRequired'),
              },
            ]}
          >
            <Input
              placeholder={t('dinein-product-topping:form.namePlaceholder')}
            />
          </Form.Item>

          <Form.Item
            name="description"
            label={t('dinein-product-topping:form.description')}
          >
            <TextArea
              placeholder={t(
                'dinein-product-topping:form.descriptionPlaceholder',
              )}
              rows={4}
            />
          </Form.Item>

          <Form.Item
            name="price"
            label={t('dinein-product-topping:form.price')}
            rules={[
              {
                required: true,
                message: t('dinein-product-topping:validation.priceRequired'),
              },
            ]}
          >
            <InputNumber
              min={0}
              step={0.01}
              style={{ width: '100%' }}
              formatter={(value) =>
                `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              }
              parser={(value) => value?.replace(/\$\s?|(,*)/g, '') || ''}
              placeholder={t('dinein-product-topping:form.pricePlaceholder')}
            />
          </Form.Item>

          <Form.Item
            name="category_id"
            label={t('dinein-product-topping:form.category')}
          >
            <Select
              showSearch
              placeholder={t('dinein-product-topping:form.categoryPlaceholder')}
              options={categoryOptions}
            />
          </Form.Item>

          <Form.Item
            name="image"
            label={t('dinein-product-topping:form.image')}
            valuePropName="fileList"
            getValueFromEvent={normFile}
          >
            <Upload
              listType="picture"
              fileList={fileList}
              beforeUpload={beforeUpload}
              onChange={handleChange}
              maxCount={1}
              customRequest={({ file, onSuccess }: any) => {
                setTimeout(() => {
                  onSuccess('ok');
                }, 0);
              }}
            >
              <Button icon={<UploadOutlined />}>
                {t('common:buttons.upload')}
              </Button>
            </Upload>
          </Form.Item>

          <Form.Item
            name="is_active"
            label={t('dinein-product-topping:form.status')}
            valuePropName="checked"
          >
            <Switch
              checkedChildren={t('common:status.active')}
              unCheckedChildren={t('common:status.inactive')}
            />
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default ToppingModalForm;
