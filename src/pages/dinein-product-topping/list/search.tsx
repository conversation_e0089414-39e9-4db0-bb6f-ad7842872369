// filepath: /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/web/cms/src/pages/dinein-product-topping/list/search.tsx
import React from 'react';
import { Form, Input, Button, Row, Col, Select } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

interface ToppingSearchProps {
  onSearch: (values: any) => void;
}

const ToppingSearch: React.FC<ToppingSearchProps> = ({ onSearch }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  const handleSearch = () => {
    const values = form.getFieldsValue();
    onSearch(values);
  };

  const handleReset = () => {
    form.resetFields();
    onSearch({});
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSearch}
      className="mb-4"
    >
      <Row gutter={16}>
        <Col lg={8} md={12} sm={24} xs={24}>
          <Form.Item
            name="name"
            label={t('dinein-product-topping:search.name')}
          >
            <Input
              placeholder={t('dinein-product-topping:search.namePlaceholder')}
            />
          </Form.Item>
        </Col>
        <Col lg={8} md={12} sm={24} xs={24}>
          <Form.Item
            name="category_id"
            label={t('dinein-product-topping:search.category')}
          >
            <Select
              placeholder={t(
                'dinein-product-topping:search.categoryPlaceholder',
              )}
              allowClear
            >
              {/* Category options will be rendered here */}
            </Select>
          </Form.Item>
        </Col>
        <Col lg={8} md={12} sm={24} xs={24}>
          <Form.Item
            name="is_active"
            label={t('dinein-product-topping:search.status')}
          >
            <Select
              placeholder={t('dinein-product-topping:search.statusPlaceholder')}
              allowClear
            >
              <Select.Option value={true}>
                {t('common:status.active')}
              </Select.Option>
              <Select.Option value={false}>
                {t('common:status.inactive')}
              </Select.Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24} style={{ textAlign: 'right' }}>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleReset}
            style={{ marginRight: 8 }}
          >
            {t('common:buttons.reset')}
          </Button>
          <Button type="primary" icon={<SearchOutlined />} htmlType="submit">
            {t('common:buttons.search')}
          </Button>
        </Col>
      </Row>
    </Form>
  );
};

export default ToppingSearch;
