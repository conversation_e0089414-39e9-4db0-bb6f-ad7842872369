// filepath: /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/web/cms/src/pages/dinein-product-topping/list/list.tsx
import React from 'react';
import { Table, Space, Button, Popconfirm, Tag, Image } from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { TableProps } from 'antd/lib/table';
import { formatMoney } from '../../../utils/format';
import { DineinTopping } from '../type';

interface ToppingListProps extends TableProps<DineinTopping> {
  toppings: DineinTopping[];
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
}

const ToppingList: React.FC<ToppingListProps> = ({
  toppings,
  onEdit,
  onDelete,
  pagination,
  onChange,
}) => {
  const { t } = useTranslation();

  const columns = [
    {
      title: t('dinein-product-topping:list.image'),
      dataIndex: 'image_url',
      key: 'image_url',
      width: 80,
      render: (image_url: string) =>
        image_url ? (
          <Image
            width={60}
            src={image_url}
            preview={{ mask: t('common:buttons.preview') }}
          />
        ) : (
          <div
            className="ant-image-placeholder"
            style={{ width: 60, height: 60 }}
          ></div>
        ),
    },
    {
      title: t('dinein-product-topping:list.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('dinein-product-topping:list.price'),
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => formatMoney(price),
    },
    {
      title: t('dinein-product-topping:list.category'),
      dataIndex: 'category_name',
      key: 'category_name',
    },
    {
      title: t('dinein-product-topping:list.status'),
      dataIndex: 'is_active',
      key: 'is_active',
      render: (is_active: boolean) =>
        is_active ? (
          <Tag color="success">{t('common:status.active')}</Tag>
        ) : (
          <Tag color="error">{t('common:status.inactive')}</Tag>
        ),
    },
    {
      title: t('common:table.actions'),
      key: 'action',
      width: 120,
      render: (_: any, record: DineinTopping) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => onEdit(record.topping_id!)}
          />
          <Popconfirm
            title={t('common:messages.deleteConfirm')}
            onConfirm={() => onDelete(record.topping_id!)}
            okText={t('common:buttons.yes')}
            cancelText={t('common:buttons.no')}
          >
            <Button danger size="small" icon={<DeleteOutlined />} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={toppings}
      rowKey="topping_id"
      pagination={pagination}
      onChange={onChange}
    />
  );
};

export default ToppingList;
