{"title": "Topping Management", "create": {"title": "Create Topping"}, "edit": {"title": "Edit Topping"}, "form": {"name": "Name", "namePlaceholder": "Enter topping name", "description": "Description", "descriptionPlaceholder": "Enter topping description", "price": "Price", "pricePlaceholder": "Enter price", "category": "Category", "categoryPlaceholder": "Select category", "image": "Image", "status": "Status", "imageUrl": "Image URL"}, "list": {"image": "Image", "name": "Name", "price": "Price", "category": "Category", "status": "Status", "action": "Action"}, "search": {"name": "Name", "namePlaceholder": "Search by name", "category": "Category", "categoryPlaceholder": "Select category", "status": "Status", "statusPlaceholder": "Select status", "searchButton": "Search", "resetButton": "Reset"}, "validation": {"nameRequired": "Please enter topping name", "priceRequired": "Please enter price", "categoryRequired": "Please select a category"}, "groups": {"title": "Topping Groups", "create": "Create Group", "edit": "Edit Group", "name": "Group Name", "description": "Description", "minSelections": "Min Selections", "maxSelections": "Max Selections", "displayOrder": "Display Order", "items": "Group Items", "addItem": "Add Item"}, "messages": {"fetchFailed": "Failed to fetch data", "createSuccess": "Topping created successfully", "createFailed": "Failed to create topping", "updateSuccess": "Topping updated successfully", "updateFailed": "Failed to update topping", "deleteSuccess": "Topping deleted successfully", "deleteFailed": "Failed to delete topping", "deleteConfirm": "Are you sure you want to delete this topping?"}, "buttons": {"create": "Create", "update": "Update", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "back": "Back", "save": "Save", "change": "Change", "selectImage": "Select Image"}}