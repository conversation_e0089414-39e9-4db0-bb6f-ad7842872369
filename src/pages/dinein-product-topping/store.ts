// filepath: /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/web/cms/src/pages/dinein-product-topping/store.ts
import { observable, action, makeObservable } from 'mobx';
import { DineinTopping } from './type';

export class DineinProductToppingStore {
  @observable currentTopping?: DineinTopping = undefined;
  @observable isFormOpen: boolean = false;
  @observable isLoading: boolean = false;

  constructor() {
    makeObservable(this);
  }

  @action
  setCurrentTopping = (topping: DineinTopping) => {
    this.currentTopping = topping;
  };

  @action
  clearCurrentTopping = () => {
    this.currentTopping = undefined;
  };

  @action
  openForm = () => {
    this.isFormOpen = true;
  };

  @action
  closeForm = () => {
    this.isFormOpen = false;
  };

  @action
  setLoading = (loading: boolean) => {
    this.isLoading = loading;
  };
}

export default new DineinProductToppingStore();
