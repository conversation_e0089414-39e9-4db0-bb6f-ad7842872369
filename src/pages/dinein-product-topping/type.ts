// filepath: /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/web/cms/src/pages/dinein-product-topping/type.ts
export interface User {
  id?: number;
  username?: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
}

export interface DineinTopping {
  topping_id?: number;
  tenant_id?: number;
  category_id?: number;
  name?: string;
  description?: string;
  price?: number;
  image_url?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
}

export interface DineinToppingGroup {
  group_id?: number;
  tenant_id?: number;
  product_id?: number;
  name?: string;
  description?: string;
  min_selections?: number;
  max_selections?: number;
  display_order?: number;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
}

export interface DineinToppingGroupItem {
  item_id?: number;
  tenant_id?: number;
  group_id?: number;
  topping_id?: number;
  display_order?: number;
  price_override?: number;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
  topping?: DineinTopping;
}

export function transformTopping(topping: any): DineinTopping {
  return {
    ...topping,
    is_active: topping.is_active === 1 || topping.is_active === true,
  };
}
