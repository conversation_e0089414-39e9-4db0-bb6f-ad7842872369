import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../../services/api.service';
import { ToppingItem } from '../type';

const url = `/api/cms/v1/dinein/toppings`;

export async function getAllToppings(
  params?: any,
): Promise<ApiResponsePaginationCursor<ToppingItem[]>> {
  const response = await apiService.get<
    ApiResponsePaginationCursor<ToppingItem[]>
  >(url, params);
  return response.data;
}

export async function getToppingsByCategoryId(
  categoryId: string,
  params?: any,
): Promise<ApiResponsePaginationCursor<ToppingItem[]>> {
  const response = await apiService.get<
    ApiResponsePaginationCursor<ToppingItem[]>
  >(`${url}/by-category/${categoryId}`, params);
  return response.data;
}

export async function getToppingCategories(
  params?: any,
): Promise<ApiResponse<any[]>> {
  const response = await apiService.get<ApiResponse<any[]>>(
    `/api/cms/v1/dinein/topping-categories`,
    params,
  );
  return response.data;
}
