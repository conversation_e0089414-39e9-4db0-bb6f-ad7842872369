.dinein-product-form {
  .image-upload-container {
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image-preview-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
  }

  .image-preview-square {
    width: 200px;
    height: 200px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 4px;
    border: 1px solid #f0f0f0;
  }

  .image-actions {
    margin-top: 8px;
  }

  // Product Toppings styles
  .product-toppings {
    .ant-table {
      margin-top: 16px;
    }

    .ant-table-thead > tr > th {
      background-color: #f7f7f7;
      font-weight: 600;
    }

    .ant-table-tbody > tr.ant-table-row:hover > td {
      background-color: #f5f5f5;
    }
  }
}
