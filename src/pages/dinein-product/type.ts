import { SeoMeta } from '../seo-meta/type';

export enum PRODUCT_STATUS {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  PENDING = 'pending',
  PRIVATE = 'private',
  TRASH = 'trash',
}

export const PRODUCT_STATUS_LABELS = {
  [PRODUCT_STATUS.PUBLISHED]: 'Đang bán',
  [PRODUCT_STATUS.PENDING]: 'Chờ duyệt',
  [PRODUCT_STATUS.PRIVATE]: 'Riêng tư',
  [PRODUCT_STATUS.TRASH]: 'Đã xóa',
  [PRODUCT_STATUS.DRAFT]: 'Bản nháp',
};

export enum VISIBILITY {
  PUBLIC = 'public',
  PRIVATE = 'private',
}

export interface User {
  id?: number;
  username?: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
}

export interface DineinProduct {
  product_id?: number;
  tenant_id?: number;
  category_id?: number;
  name?: string;
  description?: string;
  content?: string;
  slug?: string;
  base_price?: number;
  cost_price?: number;
  product_code?: string;
  barcode?: string;
  unit?: string;
  status?: string;
  image_url?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
}

export const VisibilityOptions = [
  { label: 'Công khai', value: VISIBILITY.PUBLIC },
  { label: 'Riêng tư', value: VISIBILITY.PRIVATE },
];

export function transformProduct(product: any): DineinProduct {
  return {
    ...product,
  };
}

export interface DineinCategory {
  category_id?: number;
  tenant_id?: number;
  parent_id?: number | null;
  name?: string;
  description?: string;
  image_url?: string;
  display_order?: number;
  slug?: string;
  created_at?: string;
  updated_at?: string;
  children?: DineinCategory[];
}

export interface ToppingItem {
  topping_id?: number;
  tenant_id?: number;
  category_id?: number;
  name?: string;
  description?: string;
  price?: number;
  image_url?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface ProductTopping {
  product_topping_id?: number;
  product_id?: number;
  topping_id?: number;
  price_override?: number | null;
  is_default?: boolean;
  max_quantity?: number;
  display_order?: number;
  created_at?: string;
  updated_at?: string;
  topping?: ToppingItem;
}
