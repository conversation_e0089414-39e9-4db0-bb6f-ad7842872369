import { Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import IndexForm from './form';

interface ModalFormProps {
  visible: boolean;
  id?: string;
  onCancel: () => void;
  onOk: (reload: boolean) => void;
}

const ModalForm: React.FC<ModalFormProps> = ({
  visible,
  id,
  onCancel,
  onOk,
}) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    logger('[ModalForm] visible', visible);
  }, [visible]);

  const handleCancel = () => {
    onCancel();
  };

  const handleOk = (reload: boolean) => {
    setConfirmLoading(true);
    setTimeout(() => {
      onOk(reload);
      setConfirmLoading(false);
    }, 500);
  };

  return (
    <Modal
      title={id ? 'Chỉnh sửa sản phẩm' : 'Thêm sản phẩm mới'}
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={1000}
      destroyOnClose
    >
      <IndexForm id={id} onChange={handleOk} />
    </Modal>
  );
};

export default ModalForm;
