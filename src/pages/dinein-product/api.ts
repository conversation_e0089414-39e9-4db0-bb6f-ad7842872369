import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import { DineinCategory, DineinProduct, ProductTopping } from './type';

export const MODULE = 'dinein-product';
export const MODULE_NAME = 'Sản phẩm dùng tại chỗ';
export const MODULE_POPUP = false;

const url = `/api/cms/v1/admin/dinein/products`;
const categoryUrl = `/api/cms/v1/admin/dinein/categories`;

export async function getItems(
  params: any,
): Promise<ApiResponsePaginationCursor<DineinProduct[]>> {
  const response = await apiService.get<
    ApiResponsePaginationCursor<DineinProduct[]>
  >(url, params);
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<DineinProduct>> {
  const response = await apiService.get<ApiResponse<DineinProduct>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function createItem(
  payload: any,
): Promise<ApiResponse<DineinProduct>> {
  const response = await apiService.post<ApiResponse<DineinProduct>>(
    url,
    payload,
  );
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<DineinProduct>> {
  const response = await apiService.put<ApiResponse<DineinProduct>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(
  id: string,
): Promise<ApiResponse<DineinProduct>> {
  const response = await apiService.delete<ApiResponse<DineinProduct>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<DineinProduct[]>> {
  const response = await apiService.get<ApiResponse<DineinProduct[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getCategoryTree(): Promise<
  ApiResponse<DineinCategory[]>
> {
  const response = await apiService.get<ApiResponse<DineinCategory[]>>(
    `${categoryUrl}/tree`,
  );
  return response.data;
}

// API cho ProductTopping
export async function getProductToppings(
  productId: string,
  params?: any,
): Promise<ApiResponsePaginationCursor<ProductTopping[]>> {
  const response = await apiService.get<
    ApiResponsePaginationCursor<ProductTopping[]>
  >(`${url}/${productId}/toppings`, params);
  return response.data;
}

export async function createProductTopping(
  payload: any,
): Promise<ApiResponse<ProductTopping>> {
  const response = await apiService.post<ApiResponse<ProductTopping>>(
    `${url}/toppings`,
    payload,
  );
  return response.data;
}

export async function updateProductTopping(
  id: string,
  payload: any,
): Promise<ApiResponse<ProductTopping>> {
  const response = await apiService.put<ApiResponse<ProductTopping>>(
    `${url}/toppings/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteProductTopping(
  id: string,
): Promise<ApiResponse<ProductTopping>> {
  const response = await apiService.delete<ApiResponse<ProductTopping>>(
    `${url}/toppings/${id}`,
  );
  return response.data;
}

export async function batchAssignToppings(
  payload: any,
): Promise<ApiResponse<ProductTopping[]>> {
  const response = await apiService.post<ApiResponse<ProductTopping[]>>(
    `${url}/batch-assign-toppings`,
    payload,
  );
  return response.data;
}
