import {
  ApiResponse,
  ApiResponsePagination,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MODULE } from './config';
import { Domain } from './type';

const url = `/api/cms/v1/${MODULE}`;

export async function getItems(
  params: any,
): Promise<ApiResponsePagination<Domain[]>> {
  const response = await apiService.get<ApiResponsePagination<Domain[]>>(url, {
    params,
  });
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<Domain>> {
  const response = await apiService.get<ApiResponse<Domain>>(`${url}/${id}`);
  return response.data;
}

export async function createItem(payload: any): Promise<ApiResponse<Domain>> {
  const response = await apiService.post<ApiResponse<Domain>>(url, payload);
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<Domain>> {
  const response = await apiService.put<ApiResponse<Domain>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(id: string): Promise<ApiResponse<Domain>> {
  const response = await apiService.delete<ApiResponse<Domain>>(`${url}/${id}`);
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getSearch(params: any): Promise<ApiResponse<Domain[]>> {
  const response = await apiService.get<ApiResponse<Domain[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<Domain[]>> {
  const response = await apiService.get<ApiResponse<Domain[]>>(`${url}/all`);
  return response.data;
}
