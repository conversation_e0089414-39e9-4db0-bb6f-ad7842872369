import {
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Row,
  Select,
} from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { createItem, getItem, updateItem } from '../api';
import { MODULE } from '../config';
import useBlogStore from '../store';
import { Domain } from '../type';

const FormItem = Form.Item;
const { Option } = Select;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const IndexForm: React.FC<IndexFormProps> = ({ onChange, id }) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const { loading } = useBlogStore();
  const [form] = Form.useForm();
  const [isNew, setIsNew] = useState<boolean>(false);
  const [item, setItem] = useState<Domain>();
  const initForm = {};

  const getItemData = async (_id: string) => {
    const res = await getItem(_id);
    if (res.status.success) {
      setItem(res.data);
      form.setFieldsValue(res.data);
    } else {
      message.error(res.status.message);
    }
  };

  useEffect(() => {
    if (['create', undefined].includes(id)) {
      setIsNew(true);
    } else if (id) {
      setIsNew(false);
      getItemData(id);
    }
  }, [id]);

  const onFinish = async (values: Domain) => {
    try {
      let res;
      if (isNew) {
        res = await createItem(values);
        if (res.status.success) {
          message.success(t('addSuccess'));
        }
      } else {
        res = await updateItem(id!, values);
        if (res.status.success) {
          message.success(t('updateSuccess'));
        }
      }
      if (!res.status.success) {
        message.error(res.status.message);
      } else {
        setItem(res.data);
        form.resetFields();
        onChange(true);
      }
    } catch (error) {
      logger('Error submitting form', error);
      message.error(
        _.get(error, 'response.data.message.0') || t('submitError'),
      );
    }
  };

  return (
    <Form
      style={{ marginTop: 8 }}
      form={form}
      name="form"
      layout="vertical"
      onFinish={onFinish}
      autoComplete="off"
      initialValues={initForm}
    >
      <Row gutter={16}>
        <Col xs={24} lg={24}>
          <FormItem
            label={t('serverIP')}
            name="serverIP"
            rules={[{ required: true, message: t('pleaseEnterData') }]}
          >
            <Input />
          </FormItem>
        </Col>
        <Col xs={24} lg={24}>
          <FormItem
            label={t('domain')}
            name="domain"
            rules={[{ required: true, message: t('pleaseEnterData') }]}
          >
            <Input />
          </FormItem>
        </Col>

        <Col xs={24} lg={24}>
          <FormItem
            label={t('startDate')}
            name="startDate"
            rules={[{ required: false, message: t('pleaseEnterData') }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </FormItem>
        </Col>

        <Col xs={24} lg={24}>
          <FormItem
            label={t('status')}
            name="status"
            rules={[{ required: true, message: t('pleaseEnterData') }]}
          >
            <Select>
              <Option value="PENDING">{t('PENDING')}</Option>
              <Option value="ACTIVE">{t('ACTIVE')}</Option>
              <Option value="DEACTIVE">{t('DEACTIVE')}</Option>
              <Option value="LOCK">{t('LOCK')}</Option>
            </Select>
          </FormItem>
        </Col>
      </Row>
      <FormItem style={{ marginTop: 32, textAlign: 'center' }}>
        <Button type="primary" htmlType="submit" loading={loading}>
          {isNew ? t('btnAdd') : t('btnUpdate')}
        </Button>
      </FormItem>
    </Form>
  );
};

export default IndexForm;
