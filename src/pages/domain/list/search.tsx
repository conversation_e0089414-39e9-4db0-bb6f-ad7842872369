import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Row } from 'antd';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';

function SearchForm(props: any) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    logger('[values]', values);
    props.onChange(values);
  };

  function handlerRefresh() {
    form.resetFields();
    props.onChange({});
  }

  return (
    <Form
      layout="vertical"
      form={form}
      name="frmSearch"
      onFinish={onFinish}
      className="p-4 bg-white rounded-lg mb-4"
    >
      <Row>
        <Col span={24}>
          <Row gutter={16}>
            <Col xs={24} md={6} lg={6}>
              <Form.Item
                name="key"
                label={t('key')}
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Input placeholder="" />
              </Form.Item>
            </Col>

            <Col xs={24} md={6} lg={6}>
              <Form.Item
                name="name"
                label={t('name')}
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Input placeholder="" />
              </Form.Item>
            </Col>
          </Row>
        </Col>
      </Row>
      <Row>
        <Col span={24} style={{ textAlign: 'right' }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: '15px' }}
            loading={props.loading}
            icon={<SearchOutlined />}
          >
            {t('search')}
          </Button>

          <Button
            type="default"
            htmlType="button"
            loading={props.loading}
            onClick={handlerRefresh}
            icon={<ReloadOutlined />}
          >
            {t('reset')}
          </Button>
        </Col>
      </Row>
    </Form>
  );
}

export default SearchForm;
