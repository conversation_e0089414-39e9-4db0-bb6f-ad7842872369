import { Navigate, Route, Routes } from 'react-router-dom';
import { CloudCloudflareRouter } from '../cloud-cloudflare';
import { CloudGodaddyRouter } from '../cloud-godaddy';
import { Layout } from './layout';
import { CloudDashboardRouter } from '../cloud-dashboard';

export const CloudModuleRouter = () => (
  <Routes>
    <Route element={<Layout />}>
      <Route
        path="/"
        element={<Navigate to="/cloud/cloud-dashboard" replace />}
      />
      <Route path="/cloud-dashboard/*" element={<CloudDashboardRouter />} />
      <Route path="/cloud-cloudflare/*" element={<CloudCloudflareRouter />} />
      <Route path="/cloud-godaddy/*" element={<CloudGodaddyRouter />} />
    </Route>
  </Routes>
);
