import { DashboardOutlined, CloudOutlined } from '@ant-design/icons';
import { Menu } from 'antd';
import { NavLink, Outlet, useLocation } from 'react-router-dom';

export function Layout() {
  const location = useLocation();
  const selectedKey = location.pathname.split('/').slice(0, 3).join('/');

  return (
    <div className="flex">
      <div
        style={{
          width: '250px',
          position: 'sticky',
          top: '0',
          height: '100vh',
          overflowY: 'auto',
        }}
      >
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[selectedKey]}
          items={[
            {
              key: '/cloud/cloud-dashboard',
              label: (
                <NavLink
                  to="/cloud/cloud-dashboard"
                  className="flex justify-between items-center"
                >
                  <span className="flex items-center">
                    <DashboardOutlined className="mr-2" />
                    Cloud Dashboard
                  </span>
                </NavLink>
              ),
            },
            {
              key: '/cloud/cloud-cloudflare',
              label: (
                <NavLink
                  to="/cloud/cloud-cloudflare"
                  className="flex justify-between items-center"
                >
                  <span className="flex items-center">
                    <CloudOutlined className="mr-2" />
                    Cloudflare
                  </span>
                </NavLink>
              ),
            },
            {
              key: '/cloud/cloud-godaddy',
              label: (
                <NavLink
                  to="/cloud/cloud-godaddy"
                  className="flex justify-between items-center"
                >
                  <span className="flex items-center">
                    <CloudOutlined className="mr-2" />
                    GoDaddy
                  </span>
                </NavLink>
              ),
            },
          ]}
          style={{ height: '100%' }}
        />
      </div>

      <div style={{ flex: 1, paddingLeft: '14px' }}>
        <Outlet />
      </div>
    </div>
  );
}
