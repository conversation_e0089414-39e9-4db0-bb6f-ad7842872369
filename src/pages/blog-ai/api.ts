// Blog AI API Services
import { ApiResponse, apiService } from '../../services/api.service';
import ConsoleService from '../../services/console.service';
import { MODULE } from './config';
import {
  BLOG_AI_STATUS,
  BlogAI,
  GenerationRequest,
  GenerationResponse,
} from './type';

// Status Statistics Interface
export interface StatusCounts {
  draft: number;
  generating: number;
  generated: number;
  reviewing: number;
  approved: number;
  published: number;
  failed: number;
  cancelled: number;
  total: number;
}

export interface StatusStatistics {
  status_counts: StatusCounts;
}

const logger = ConsoleService.register(MODULE);

const blogAIUrl = `/api/cms/v1/agent-ai/posts`;
const statusUrl = `/api/cms/v1/blog-ai/status`;

// Blog AI Posts API Functions
export async function getItems(params: any): Promise<any> {
  logger('getItems', params);
  const response = await apiService.get<any>(blogAIUrl, { params: params });
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<BlogAI>> {
  const response = await apiService.get<ApiResponse<BlogAI>>(
    `${blogAIUrl}/${id}`,
  );
  return response.data;
}

export async function createItem(
  data: Partial<BlogAI>,
): Promise<ApiResponse<BlogAI>> {
  const response = await apiService.post<ApiResponse<BlogAI>>(blogAIUrl, data);
  return response.data;
}

export async function updateItem(
  id: string,
  data: Partial<BlogAI>,
): Promise<ApiResponse<BlogAI>> {
  const response = await apiService.put<ApiResponse<BlogAI>>(
    `${blogAIUrl}/${id}`,
    data,
  );
  return response.data;
}

export async function deleteItem(id: string): Promise<ApiResponse<any>> {
  const response = await apiService.delete<ApiResponse<any>>(
    `${blogAIUrl}/${id}`,
  );
  return response.data;
}

// Generation API Functions
export async function generateBlog(
  request: GenerationRequest,
): Promise<ApiResponse<GenerationResponse>> {
  logger('generateBlog', request);
  const response = await apiService.post<ApiResponse<GenerationResponse>>(
    `${blogAIUrl}/generate`,
    request,
  );
  return response.data;
}

export async function getGenerationStatus(
  id: string,
): Promise<ApiResponse<GenerationResponse>> {
  const response = await apiService.get<ApiResponse<GenerationResponse>>(
    `${blogAIUrl}/${id}/status`,
  );
  return response.data;
}

export async function cancelGeneration(id: string): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${blogAIUrl}/${id}/cancel`,
  );
  return response.data;
}

export async function regenerateBlog(
  id: string,
  request: Partial<GenerationRequest>,
): Promise<ApiResponse<GenerationResponse>> {
  const response = await apiService.post<ApiResponse<GenerationResponse>>(
    `${blogAIUrl}/${id}/regenerate`,
    request,
  );
  return response.data;
}

// Status Statistics API
export async function getStatusStatistics(): Promise<
  ApiResponse<StatusStatistics>
> {
  const response =
    await apiService.get<ApiResponse<StatusStatistics>>(statusUrl);
  return response.data;
}

// Batch Operations
export async function batchUpdateStatus(
  ids: string[],
  status: BLOG_AI_STATUS,
): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${blogAIUrl}/batch/status`,
    { ids, status },
  );
  return response.data;
}

export async function batchDelete(ids: string[]): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${blogAIUrl}/batch/delete`,
    { ids },
  );
  return response.data;
}

// Export to Blog
export async function exportToBlog(id: string): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${blogAIUrl}/${id}/export`,
  );
  return response.data;
}

// Quality Analysis
export async function analyzeQuality(id: string): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${blogAIUrl}/${id}/analyze`,
  );
  return response.data;
}

// Options API
export async function getOptions(): Promise<ApiResponse<any>> {
  const response = await apiService.get<ApiResponse<any>>(
    `/api/cms/v1/agent-ai/options`,
  );
  return response.data;
}

// AI Models API
export async function getAvailableModels(): Promise<ApiResponse<any>> {
  const response = await apiService.get<ApiResponse<any>>(
    `/api/cms/v1/agent-ai/models`,
  );
  return response.data;
}

// Templates API
export async function getPromptTemplates(): Promise<ApiResponse<any>> {
  const response = await apiService.get<ApiResponse<any>>(
    `/api/cms/v1/agent-ai/templates`,
  );
  return response.data;
}

export async function savePromptTemplate(data: any): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `/api/cms/v1/agent-ai/templates`,
    data,
  );
  return response.data;
}
