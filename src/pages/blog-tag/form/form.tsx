import {
  But<PERSON>,
  Col,
  ColorPicker,
  Form,
  Input,
  message,
  Row,
  Switch,
} from 'antd';
import { FormHeader } from '../../../components';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { InputSlug } from '../../../components/input';
import ConsoleService from '../../../services/console.service';
import { createItem, getItem, updateItem } from '../api';
import { MODULE } from '../config';
import useBlogStore from '../store';
import {
  BlogTagResponse,
  BlogTagCreateRequest,
  BlogTagUpdateRequest,
} from '../type';

const FormItem = Form.Item;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const IndexForm: React.FC<IndexFormProps> = ({ onChange, id }) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const { loading } = useBlogStore();
  const [form] = Form.useForm();
  const [isNew, setIsNew] = useState<boolean>(false);
  const [item, setItem] = useState<BlogTagResponse>();
  const [formValues, setFormValues] = useState<any>();

  const initForm = {
    is_active: true,
    color: '#3498db', // Default color
  };

  const getItemData = async (_id: string) => {
    const res = await getItem(_id);
    if (res.status.success) {
      setItem(res.data);
      // Convert color string to ColorPicker format if needed
      const formData = {
        ...res.data,
        color: res.data.color || '#3498db',
      };
      form.setFieldsValue(formData);
    } else {
      message.error(res.status.message);
    }
  };

  useEffect(() => {
    if (['create', undefined].includes(id)) {
      setIsNew(true);
    } else if (id) {
      setIsNew(false);
      getItemData(id);
    }
  }, [id, getItemData]);

  const onFinish = async (values: any) => {
    try {
      let res;

      // Get website_id from localStorage or other source
      const websiteId = localStorage.getItem('selectedWebsiteId') || '1';

      // Prepare payload according to API v3 DTOs
      const payload = {
        ...values,
        website_id: parseInt(websiteId),
        color:
          typeof values.color === 'string'
            ? values.color
            : values.color?.toHexString?.() || '#3498db',
      };

      if (isNew) {
        const createPayload: BlogTagCreateRequest = {
          tenant_id: 1, // This should come from context/auth
          ...payload,
        };
        res = await createItem(createPayload);
        if (res.status.success) {
          message.success(t('addSuccess'));
        }
      } else {
        const updatePayload: BlogTagUpdateRequest = {
          name: payload.name,
          slug: payload.slug,
          description: payload.description,
          color: payload.color,
          is_active: payload.is_active,
        };
        res = await updateItem(id!, updatePayload);
        if (res.status.success) {
          message.success(t('updateSuccess'));
        }
      }

      if (!res.status.success) {
        message.error(res.status.message);
      } else {
        setItem(res.data);
        form.resetFields();
        onChange(true);
      }
    } catch (error) {
      logger('Error submitting form', error);
      message.error(
        _.get(error, 'response.data.message.0') || t('submitError'),
      );
    }
  };
  const handleValuesChange = (newValue: any, allValues: any) => {
    logger(newValue);
    logger(allValues);
    setFormValues(allValues);
  };
  return (
    <div>
      <FormHeader
        title={isNew ? 'Thêm thẻ mới' : 'Chỉnh sửa thẻ'}
        backDestination="list"
      />
      <Form
        style={{ marginTop: 8 }}
        form={form}
        name="form"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        initialValues={initForm}
        onValuesChange={handleValuesChange}
      >
        <div className="form_content">
          <Row gutter={16}>
            <Col xs={24} lg={24}>
              <FormItem
                label={t('name')}
                name="name"
                rules={[{ required: true, message: t('pleaseEnterData') }]}
              >
                <Input />
              </FormItem>
            </Col>
            <Col xs={24} lg={24}>
              <FormItem
                label={t('Đường dẫn URL (tự động)')}
                name="slug"
                rules={[{ required: false, message: t('pleaseEnterData') }]}
              >
                <InputSlug
                  form={form}
                  name="slug"
                  initialValue={formValues?.name}
                  sourceField="name"
                  isEditMode={!isNew}
                />
              </FormItem>
            </Col>
            <Col xs={24} lg={24}>
              <FormItem
                label={t('description') || 'Mô tả'}
                name="description"
                rules={[{ required: false, message: t('pleaseEnterData') }]}
              >
                <Input.TextArea
                  rows={3}
                  placeholder={
                    t('Enter tag description') || 'Nhập mô tả cho thẻ'
                  }
                />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('color') || 'Màu sắc'}
                name="color"
                rules={[{ required: false, message: t('pleaseSelectColor') }]}
              >
                <ColorPicker
                  format="hex"
                  showText
                  presets={[
                    {
                      label: 'Recommended',
                      colors: [
                        '#3498db',
                        '#e74c3c',
                        '#2ecc71',
                        '#f39c12',
                        '#9b59b6',
                        '#1abc9c',
                        '#34495e',
                        '#e67e22',
                        '#95a5a6',
                        '#16a085',
                      ],
                    },
                  ]}
                />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem label={t('active')} name="is_active">
                <Switch
                  checkedChildren={t('statusActive')}
                  unCheckedChildren={t('statusDeActive')}
                />
              </FormItem>
            </Col>
          </Row>
        </div>

        <div className="form_footer">
          <FormItem>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isNew ? t('btnAdd') : t('btnUpdate')}
            </Button>
          </FormItem>
        </div>
      </Form>
    </div>
  );
};

export default IndexForm;
