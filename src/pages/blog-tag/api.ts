import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import {
  BlogTagResponse,
  BlogTagCreateRequest,
  BlogTagUpdateRequest,
  BlogTagFilter,
  BlogTagSelectOption,
  BlogTag, // Legacy compatibility
} from './type';

const url = `/api/cms/v1/blog/tags`;

// Get blog tags with cursor pagination - API v3
export async function getItems(
  params: BlogTagFilter,
): Promise<ApiResponsePaginationCursor<BlogTagResponse[]>> {
  const response = await apiService.get<
    ApiResponsePaginationCursor<BlogTagResponse[]>
  >(url, {
    params,
  });
  return response.data;
}

// Get single blog tag by ID - API v3
export async function getItem(
  id: string,
): Promise<ApiResponse<BlogTagResponse>> {
  const response = await apiService.get<ApiResponse<BlogTagResponse>>(
    `${url}/${id}`,
  );
  return response.data;
}

// Create blog tag - API v3
export async function createItem(
  payload: BlogTagCreateRequest,
): Promise<ApiResponse<BlogTagResponse>> {
  const response = await apiService.post<ApiResponse<BlogTagResponse>>(
    url,
    payload,
  );
  return response.data;
}

// Update blog tag - API v3
export async function updateItem(
  id: string,
  payload: BlogTagUpdateRequest,
): Promise<ApiResponse<BlogTagResponse>> {
  const response = await apiService.put<ApiResponse<BlogTagResponse>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

// Delete blog tag - API v3
export async function deleteItem(
  id: string,
): Promise<ApiResponse<BlogTagResponse>> {
  const response = await apiService.delete<ApiResponse<BlogTagResponse>>(
    `${url}/${id}`,
  );
  return response.data;
}

// Get tags for select components - API v3
export async function getSelectOptions(params?: {
  limit?: number;
  search?: string;
  is_active?: boolean;
}): Promise<ApiResponse<BlogTagSelectOption[]>> {
  const response = await apiService.get<ApiResponse<BlogTagSelectOption[]>>(
    `${url}/select`,
    { params },
  );
  return response.data;
}

// Legacy functions for backward compatibility (deprecated)
export async function getOptions(): Promise<
  ApiResponse<BlogTagSelectOption[]>
> {
  return getSelectOptions({ limit: 50, is_active: true });
}

export async function getAll(): Promise<ApiResponse<BlogTag[]>> {
  const response = await getItems({ limit: 1000, is_active: true });
  return {
    status: response.status,
    data: response.data || [],
    meta: response.meta,
  };
}
