import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import {
  Col,
  Dropdown,
  message,
  Pagination,
  Popconfirm,
  Row,
  Space,
  Table,
  Tooltip,
} from 'antd';
import queryString from 'query-string';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { BackButton, ButtonLink } from '../../../components/button';
import { useCursorPagination } from '../../../components/pagination';
import CursorPagination from '../../../components/pagination/cursor-pagination';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  formatDate,
  //changeBrowserLocation,
  getPageNumber,
} from '../../../services/utils.service';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import ModalForm from '../form/modal';
import useBlogTagStore from '../store';
import { BlogTagResponse, BlogTagFilter } from '../type';
import Search from './search';

export default function List() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useBlogTagStore();

  // Cursor pagination setup
  const { afterKey, isNext, isBack, setNextCursor, goNext, goBack } =
    useCursorPagination({
      defaultLimit: 10,
    });

  // Legacy pagination setup (for fallback)
  const [pagination, setPagination] = useState<any>({
    page: 1,
    limit: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState<BlogTagFilter>({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<BlogTagResponse[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();

  const fetchData = useCallback(
    async (payload?: any) => {
      const params: BlogTagFilter = {
        ...query,
        ...filters,
        ...payload,
        cursor: afterKey,
        limit: pagination.limit,
      };

      try {
        const response = await getItems(params);
        if (response.status.success) {
          setItems(response.data || []);
          if (response.meta) {
            setNextCursor(response.meta.next_cursor);
          }
        } else {
          message.error(response.status.message);
        }
      } catch (error: any) {
        logger('[fetchData] Error:', error);
        message.error(error.message || 'Failed to fetch data');
      }
    },
    [query, filters, pagination.limit, afterKey, setNextCursor, logger],
  );

  const onPagingChange = (page: number, limit: number) => {
    logger('[page]', { page, limit });
    setPagination({ page, limit });
    fetchData({ page, limit });
  };

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    // //changeBrowserLocation(navigate, pathname, values);
    setFilters(values);
    fetchData(values);
  };

  useEffectOnce(() => {
    fetchData();
  });

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    }
  };

  const handleTableChange = (page: number, pageSize: number) => {
    onPagingChange(page, pageSize);
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const columns = [
    {
      title: t('name'),
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: BlogTagResponse) => (
        <Space>
          {record.color && (
            <div
              style={{
                width: '16px',
                height: '16px',
                borderRadius: '50%',
                backgroundColor: record.color,
                border: '1px solid #d9d9d9',
                display: 'inline-block',
              }}
            />
          )}
          <a onClick={() => handleActions('edit', record)}>{text}</a>
        </Space>
      ),
    },
    {
      title: t('slug'),
      dataIndex: 'slug',
      key: 'slug',
    },
    {
      title: t('Bài viết'),
      dataIndex: 'post_count',
      key: 'post_count',
      width: 100,
      render: (count: number) => count || 0,
    },
    {
      title: t('description'),
      dataIndex: 'description',
      key: 'description',
      ellipsis: {
        showTitle: false,
      },
      render: (description: string) => (
        <Tooltip placement="topLeft" title={description}>
          {description || '-'}
        </Tooltip>
      ),
    },
    {
      title: t('color') || 'Màu sắc',
      dataIndex: 'color',
      key: 'color',
      width: 80,
      render: (color: string) =>
        color ? (
          <div
            style={{
              width: '24px',
              height: '24px',
              borderRadius: '4px',
              backgroundColor: color,
              border: '1px solid #d9d9d9',
              margin: '0 auto',
            }}
          />
        ) : (
          '-'
        ),
    },
    {
      title: t('created_at'),
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date: string) => formatDate(date),
    },
    {
      title: t('active'),
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (active: boolean) => (
        <span>{active ? t('statusActive') : t('statusDeActive')}</span>
      ),
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      width: 120,
      render: (dom: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div>
      <div className="bg-gray flex justify-between p-4">
        <div className="flex items-center space-x-2">
          <BackButton destination="dashboard" />
          <div className="text-xl font-bold">{t('module')}</div>
        </div>
        <div className="gap-4">
          <ButtonLink
            type="primary"
            icon={<PlusCircleOutlined />}
            to={`${MODULE}/create`}
          >
            {t('btnAdd')}
          </ButtonLink>
        </div>
      </div>
      <Search query={query} loading={loading} onChange={handleFilters}></Search>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        ></Table>
        <Row justify="end" className="p-4">
          <Col>
            <CursorPagination
              isNext={isNext}
              isBack={isBack}
              goNext={() => goNext(fetchData)}
              goBack={() => goBack(fetchData)}
              total={items.length}
            />
          </Col>
        </Row>
        {MODULE_POPUP && (
          <ModalForm
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></ModalForm>
        )}
      </Space>
    </div>
  );
}
