import { Select, SelectProps, Tag } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import ConsoleService from '../../../services/console.service';
import { getSelectOptions } from '../api';
import { MODULE } from '../config';
import { BlogTagSelectOption } from '../type';

interface Props extends SelectProps {
  isOptionAll?: boolean;
  showColors?: boolean;
}

export const SelectBlogTag: React.FC<Props> = (props) => {
  const logger = ConsoleService.register(MODULE);
  const { isOptionAll = false, showColors = true, ...selectProps } = props;
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<BlogTagSelectOption[]>([]);

  const getDataCallback = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getSelectOptions({
        limit: 50,
        is_active: true,
      });
      logger('[getSelectOptions response]', response);

      if (response.status.success) {
        const _options = response.data || [];

        logger('[_options]', _options);
        setOptions(_options);
      }
    } catch (error) {
      logger('[getSelectOptions error]', error);
    }
    setLoading(false);
  }, [logger]);

  useEffect(() => {
    getDataCallback();
  }, [getDataCallback]);

  // Transform options for Select component
  const selectOptions = options.map((option) => ({
    label:
      showColors && option.color ? (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <div
            style={{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              backgroundColor: option.color,
              border: '1px solid #d9d9d9',
            }}
          />
          {option.name}
          {option.post_count !== undefined && (
            <span style={{ color: '#999', fontSize: '12px' }}>
              ({option.post_count})
            </span>
          )}
        </span>
      ) : (
        <span>
          {option.name}
          {option.post_count !== undefined && (
            <span style={{ color: '#999', fontSize: '12px' }}>
              ({option.post_count})
            </span>
          )}
        </span>
      ),
    value: option.id,
    key: option.id,
  }));

  // Add "All" option if requested
  if (isOptionAll) {
    selectOptions.unshift({
      label: 'Tất cả',
      value: '',
      key: 'all',
    });
  }

  // Custom tag render for selected values
  const tagRender = (props: any) => {
    const { label, value, closable, onClose } = props;
    const option = options.find((opt) => opt.id === value);

    return (
      <Tag
        color={showColors && option?.color ? option.color : undefined}
        closable={closable}
        onClose={onClose}
        style={{
          marginRight: 3,
          color: showColors && option?.color ? '#fff' : undefined,
        }}
      >
        {option?.name || label}
      </Tag>
    );
  };

  return (
    <Select
      loading={loading}
      options={selectOptions}
      tagRender={showColors ? tagRender : undefined}
      placeholder="Chọn thẻ..."
      {...selectProps}
    />
  );
};
