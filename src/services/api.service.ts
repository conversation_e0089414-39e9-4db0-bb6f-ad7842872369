import axios, { AxiosError, AxiosInstance, AxiosResponse } from 'axios';
import config from './config.service';
import ConsoleService from './console.service';
import tokenService from './token.service';
import tenantService from './tenant.service';
import websiteService from './website.service';
const logger = ConsoleService.register('api.service.ts');

export interface Status {
  success: boolean;
  code: number;
  message: string;
  error_code?: string;
  path?: string;
  timestamp?: string;
  details?: any;
}

export interface Meta {
  currentPage: number;
  lastPage: number;
  limit: number;
  total: number;
}

export interface ApiResponse<T = object> {
  status: Status;
  data: T;
  errors?: string[] | Record<string, string | string[]>;
  error_details?: Array<{ field?: string; message: string }>;
}

export interface ApiResponsePagination<T = object> {
  status: Status;
  data: T;
  meta: Meta;
}

export interface MetaCurosr {
  cursorBefore?: string;
  cursor?: string;
  total?: number;
  next_cursor?: string;
  nextCursor?: string;
  has_more?: boolean;
  hasMore?: boolean;
}

export interface ApiResponsePaginationCursor<T = object> {
  status: Status;
  data: T;
  meta: MetaCurosr;
}

// API v3 Cursor Pagination types - aliases for consistency
export interface CursorPagination {
  cursor?: string;
  limit?: number;
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface CursorResponse<T = object>
  extends ApiResponsePaginationCursor<T> {}

logger('[API_URL]', config.API_URL);
export function setAuthToken(token: string): void {
  apiService.defaults.headers.common.Authorization = `Bearer ${token}`;
}

const apiService: AxiosInstance = axios.create({
  baseURL: config.API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor - Thêm tenant_id và website_id vào header
apiService.interceptors.request.use(
  (config) => {
    // Thêm tenant_id vào header nếu có
    const tenantId = tenantService.getCurrentTenantId();
    if (tenantId) {
      config.headers['X-Tenant-ID'] = tenantId;
    }

    // Thêm website_id vào header nếu có
    const websiteId = websiteService.getCurrentWebsiteId();
    if (websiteId) {
      config.headers['X-Website-ID'] = websiteId.toString();
    }

    return config;
  },
  (error) => Promise.reject(error),
);

// Response interceptor
apiService.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError) => Promise.reject(error),
);

// Create a dedicated upload service without default Content-Type header
export function createUploadService(): AxiosInstance {
  const uploadService = axios.create({
    baseURL: config.API_URL,
    // No default Content-Type header for uploads
  });

  // Add auth token if available
  const token = tokenService.getToken();
  if (token) {
    uploadService.defaults.headers.common.Authorization = `Bearer ${token}`;
  }

  // Thêm tenant_id vào header nếu có
  const tenantId = tenantService.getCurrentTenantId();
  if (tenantId) {
    uploadService.defaults.headers.common['X-Tenant-ID'] = tenantId;
  }

  // Thêm website_id vào header nếu có
  const websiteId = websiteService.getCurrentWebsiteId();
  if (websiteId) {
    uploadService.defaults.headers.common['X-Website-ID'] =
      websiteId.toString();
  }

  return uploadService;
}

const token = tokenService.getToken();
if (token) {
  setAuthToken(token);
  logger('[token]', token);
}

export { apiService };
