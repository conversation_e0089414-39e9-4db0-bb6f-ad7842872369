---
id: task-14
title: Add refresh-with-tenant endpoint to auth API
status: To Do
assignee: []
created_date: '2025-07-22'
labels: []
dependencies: []
---

## Description

Add the refresh-with-tenant endpoint that exists in Swagger but is missing from the current auth API implementation. This endpoint allows token refresh with specific tenant context.

## Acceptance Criteria

- [ ] refresh-with-tenant endpoint implemented in auth API
- [ ] Token refresh with tenant context works correctly
- [ ] Multi-tenant token refresh handled properly
- [ ] Existing refresh functionality remains unchanged
