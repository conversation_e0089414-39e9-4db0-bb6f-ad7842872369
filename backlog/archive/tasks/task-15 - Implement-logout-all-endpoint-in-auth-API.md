---
id: task-15
title: Implement logout-all endpoint in auth API
status: To Do
assignee: []
created_date: '2025-07-22'
labels: []
dependencies: []
---

## Description

Add logout-all endpoint that exists in Swagger documentation but is missing from current auth API implementation. This allows users to logout from all devices/sessions.

## Acceptance Criteria

- [ ] logout-all endpoint implemented in auth API
- [ ] Users can logout from all sessions
- [ ] Token invalidation works correctly for all devices
- [ ] UI provides logout-all option
- [ ] Error handling implemented for logout-all failures
