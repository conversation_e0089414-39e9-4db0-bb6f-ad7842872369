---
id: task-13
title: Update TOTP API endpoints to use new 2FA structure
status: Done
assignee:
  - '@max'
created_date: '2025-07-22'
updated_date: '2025-07-22'
labels: []
dependencies: []
---

## Description

Update existing TOTP implementation to use the new 2FA API structure from Swagger documentation. Current implementation uses /api/cms/v1/users-totp but should integrate with /2fa endpoints.

## Acceptance Criteria

- [x] TOTP functionality migrated to new 2FA structure
- [x] All existing TOTP features work correctly
- [x] 2FA enable/disable functionality updated
- [x] User profile 2FA settings updated
- [x] Legacy TOTP endpoints removed or marked deprecated

## Implementation Plan

1. Update API endpoints in user/api.ts to use new /api/cms/v1/2fa/ structure
2. Modify generateTotp to use /api/cms/v1/2fa/generate endpoint
3. Update getTotpStatus to use /api/cms/v1/2fa/status endpoint
4. Update verifyTotp to use /api/cms/v1/2fa/verify endpoint
5. Update enableTotp to use /api/cms/v1/2fa/enable endpoint
6. Update disableTotp to use /api/cms/v1/2fa/disable endpoint
7. Test all TOTP functionality with new endpoints
8. Update any UI components that depend on response structure changes

## Implementation Notes

Successfully migrated TOTP implementation to use new 2FA API structure.

## Implementation Summary:

**Modified Files:**

- `src/pages/user/api.ts` - Updated TOTP endpoints to use /api/cms/v1/2fa/ path and marked legacy functions as deprecated
- `src/pages/auth/api.ts` - Added new 2FA status endpoint to complement existing 2FA API structure
- `src/pages/user/components/totp-form.tsx` - Updated to use new twoFactorApi.status() instead of legacy getTotpStatus()

**Migration Details:**

- Migrated from legacy /api/cms/v1/users-totp/ endpoints to new /api/cms/v1/2fa/ structure
- TOTP form now uses twoFactorApi from auth module for all 2FA operations (enable, disable, verify, status)
- Added TwoFactorStatusResponse interface and twoFactorApi.status() method for getting current 2FA status
- All legacy TOTP functions in user/api.ts marked as deprecated with clear migration guidance

**Technical Decisions:**

- Maintained backward compatibility by keeping deprecated functions but adding clear deprecation warnings
- Used centralized twoFactorApi from auth module instead of scattered user API endpoints
- Updated status checking to use new 2FA status endpoint for consistency
- All components now use unified /api/cms/v1/2fa/ path structure

**Testing:**

- Build completed successfully with no TypeScript errors
- All TOTP functionality migrated to new 2FA endpoint structure
- User profile 2FA settings integration verified and working correctly
