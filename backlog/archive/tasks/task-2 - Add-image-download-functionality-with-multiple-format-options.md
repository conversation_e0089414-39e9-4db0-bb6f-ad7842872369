---
id: task-2
title: Add image download functionality with multiple format options
status: In Progress
assignee:
  - '@max'
created_date: '2025-07-14'
updated_date: '2025-07-14'
labels:
  - image-editor
  - feature
dependencies: []
---

## Description

Users should be able to download edited images in various formats (PNG, JPEG, WebP) with quality settings

## Acceptance Criteria

- [ ] Download button exports canvas content
- [ ] Support for PNG JPEG and WebP formats
- [ ] Quality slider for JPEG/WebP (0-100)
- [ ] Filename includes original name and timestamp
- [ ] Download starts automatically on click
- [ ] File size preview before download

## Implementation Plan

1. Create download options component with format selection (PNG, JPEG, WebP)
2. Add quality slider for lossy formats (JPEG/WebP)
3. Implement file size preview calculation
4. Enhance KonvaCanvas component to support multiple export formats
5. Update image-editor-modal to integrate new download functionality
6. Add filename generation with timestamp
7. Test download across different formats and quality settings
