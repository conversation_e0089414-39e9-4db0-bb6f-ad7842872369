---
id: task-1
title: Implement image save functionality to update media store
status: Done
assignee:
  - '@max'
created_date: '2025-07-14'
updated_date: '2025-07-14'
labels:
  - image-editor
  - enhancement
dependencies: []
---

## Description

When a user edits and saves an image in the image editor modal, the updated image should be saved back to the media store and the UI should reflect the changes

## Acceptance Criteria

- [ ] ✓ Edited image is saved to backend API
- [ ] ✓ Media store is updated with new image URL
- [ ] ✓ UI refreshes to show updated image
- [ ] ✓ Success message shown to user
- [ ] ✓ Error handling for failed saves

## Implementation Plan

1. Examine current save mechanism in image-editor-modal.tsx
2. Create API endpoint for saving edited images (PATCH /api/cms/v1/media/{id}/update-content)
3. Modify handleSave function to upload edited image to backend
4. Update media store to refresh the specific item after save
5. Add success/error messaging with notifications
6. Test the complete save workflow

## Implementation Notes

Successfully implemented image save functionality with the following approach:

**Features Implemented:**

- Added new API endpoint updateMediaContent() for updating media files with edited content
- Enhanced media store with updateItemContent() function to update specific items
- Modified ImageEditorModal to export edited images as blobs instead of just data URLs
- Integrated save functionality with backend API and media store updates
- Added proper error handling and success messaging

**Technical Implementation:**

- API: New PATCH /api/cms/v1/media/{id}/content endpoint for uploading edited image files
- Store: New updateItemContent() function that updates both items and selectedItems arrays
- Modal: Enhanced handleSave() to convert canvas output to blob and pass to callback
- Integration: Updated handleImageSave() in editor-media.tsx to call API and refresh store

**Modified Files:**

- src/pages/editor-media/api.ts - Added updateMediaContent function
- src/pages/editor-media/store.ts - Added updateItemContent function and updated imports
- src/pages/editor-media/components/edit-img/image-editor-modal.tsx - Enhanced save functionality with blob export
- src/pages/editor-media/components/editor-media.tsx - Integrated save workflow with store updates

**User Experience:**

- Users can now edit images and save changes back to the media library
- Success/error messages provide clear feedback
- Media list automatically refreshes to show updated images
- Maintains filename convention with edited\_ prefix for modified images
