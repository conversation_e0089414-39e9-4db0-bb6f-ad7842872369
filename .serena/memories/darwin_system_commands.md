# macOS (Darwin) System Commands

## File System Navigation

```bash
# List files (macOS ls)
ls -la

# Find files
find . -name "*.tsx" -type f

# Search in files (ripgrep recommended)
rg "pattern" --type tsx

# Copy files
cp source.txt destination.txt

# Move/rename files
mv oldname.txt newname.txt

# Remove files/directories
rm -f file.txt
rm -rf directory/
```

## Process Management

```bash
# Show running processes
ps aux | grep node

# Kill process by PID
kill -9 <PID>

# Kill process by name
pkill -f "rsbuild"
```

## Network and Ports

```bash
# Check what's running on port
lsof -i :3000

# Kill process on specific port
lsof -ti:3000 | xargs kill -9
```

## Development Environment

```bash
# Check Node.js version
node --version

# Check npm version
npm --version

# Clear npm cache
npm cache clean --force

# Check disk space
df -h

# Monitor file changes
fswatch src/ | head -20
```

## macOS Specific

```bash
# Open current directory in Finder
open .

# Open file with default application
open package.json

# Show hidden files in Finder
defaults write com.apple.finder AppleShowAllFiles YES
killall Finder

# Hide hidden files in Finder
defaults write com.apple.finder AppleShowAllFiles NO
killall Finder
```

## Text Processing

```bash
# View file content
cat package.json

# View file with line numbers
cat -n src/App.tsx

# Search and replace in files
sed -i '' 's/old/new/g' file.txt

# Count lines in file
wc -l src/App.tsx
```

## Archive Operations

```bash
# Create zip archive
zip -r archive.zip directory/

# Extract zip
unzip archive.zip

# Create tarball
tar -czf archive.tar.gz directory/

# Extract tarball
tar -xzf archive.tar.gz
```
