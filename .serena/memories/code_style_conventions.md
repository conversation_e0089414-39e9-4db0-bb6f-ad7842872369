# Code Style and Conventions

## File Naming Conventions

- **Files**: Use lowercase with hyphens for separation
  - Example: `select-blog.tsx`, `rbac-resource.ts`
- **Components**: PascalCase for React components
  - Example: `BlogTag`, `SelectBlog`

## Code Style Rules (Prettier)

- **Print Width**: 80 characters
- **Tab Width**: 2 spaces
- **Use Tabs**: false (use spaces)
- **Semicolons**: required
- **Quotes**: single quotes
- **Trailing Commas**: always
- **Line Endings**: LF

## TypeScript Configuration

- **Strict Mode**: enabled
- **No Unused Locals**: enabled
- **No Unused Parameters**: enabled
- **JSX**: react-jsx (no need to import React)
- **Module Resolution**: Bundler
- **Base URL**: `./src` for relative imports

## ESLint Rules

- TypeScript recommended rules enabled
- React hooks rules enforced
- Prettier integration for code formatting
- **Disabled Rules**:
  - `@typescript-eslint/no-explicit-any`: off
  - `@typescript-eslint/no-unused-vars`: off
  - `react/prop-types`: off (using TypeScript instead)

## Import Organization

- Auto-sorting imports configured
- Simple import sort plugin enabled
- Import sorter auto-formats on save

## Component Structure

- Use functional components with hooks
- TypeScript interfaces for props
- Proper React hooks usage (rules enforced)
- Suspense for lazy loading

## Styling Approach

- SCSS for component-specific styles
- Tailwind CSS for utility classes
- Styled Components for dynamic styling
- Ant Design for UI components

## Git Hooks

- Pre-commit hooks run lint-staged
- Automatic prettier formatting before commit
- ESLint fixes applied automatically
