# Task Completion Checklist

## Before Starting a Task

- [ ] Read the task requirements from Backlog.md
- [ ] Understand the acceptance criteria
- [ ] Mark task as "In Progress" in Backlog.md
- [ ] Create implementation plan if not exists

## During Development

- [ ] Follow file naming conventions (lowercase with hyphens)
- [ ] Follow code style guidelines (Pretti<PERSON>, ESLint rules)
- [ ] Use TypeScript for type safety
- [ ] Write components as functional components with hooks
- [ ] Use appropriate imports (relative from src/)

## Code Quality Checks

- [ ] Run ESLint and fix all issues: `npm run lint`
- [ ] Format code with Prettier: `npm run format`
- [ ] Check TypeScript compilation: `npx tsc --noEmit`
- [ ] Test the functionality manually

## Testing

- [ ] Write or update Playwright tests if applicable
- [ ] Run existing tests: `npm run test`
- [ ] Ensure no regression in existing functionality

## Documentation

- [ ] Update relevant documentation if needed
- [ ] Add comments for complex logic
- [ ] Update README.md if new features added

## Git Workflow

- [ ] Create feature branch from main
- [ ] Make commits with clear messages
- [ ] Pre-commit hooks should pass automatically
- [ ] Push to remote branch

## Task Completion

- [ ] All acceptance criteria met
- [ ] Code reviewed (self-review minimum)
- [ ] No ESLint or TypeScript errors
- [ ] Tests passing
- [ ] Mark task as "Done" in Backlog.md: `backlog task edit <id> -s "Done"`
- [ ] Add implementation notes to task

## Definition of Done

A task is complete when:

1. All acceptance criteria are satisfied
2. Code follows project conventions
3. All quality checks pass (lint, format, typecheck)
4. Tests are written and passing
5. Documentation is updated
6. Task marked as "Done" in Backlog.md with implementation notes
