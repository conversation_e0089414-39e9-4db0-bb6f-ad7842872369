# Suggested Commands for Blog UI v1

## Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## Code Quality Commands

```bash
# Run ESLint
npm run lint

# Auto-fix ESLint issues
npx eslint --fix src/

# Format code with Prettier
npm run format
```

## Testing Commands

```bash
# Run all Playwright tests
npm run test

# Run tests with browser UI
npm run test:headed

# Run tests with Playwright UI
npm run test:ui

# Debug tests
npm run test:debug

# Show test report
npm run test:report

# Run specific register test
npm run test:register

# Quick register test
npm run test:register-quick
```

## Project Management Commands

```bash
# List all tasks (requires Backlog.md CLI)
backlog task list --plain

# View specific task
backlog task <id> --plain

# Create new task
backlog task create "Task name"

# Edit task status
backlog task edit <id> -s "In Progress"
```

## Git Commands

```bash
# Check status
git status

# Add files
git add .

# Commit with message
git commit -m "message"

# Push changes
git push origin <branch-name>
```

## Development Utilities

```bash
# Install dependencies
npm install

# Clean node_modules and reinstall
rm -rf node_modules package-lock.json && npm install

# Check TypeScript errors
npx tsc --noEmit
```
