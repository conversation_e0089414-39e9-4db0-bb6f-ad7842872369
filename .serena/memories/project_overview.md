# Blog UI v1 - Project Overview

## Purpose

This is a React-based blog user interface application built with modern web technologies. It's a comprehensive blog management system with features including:

- User authentication and registration
- Tenant management and onboarding
- Blog content management
- WebSocket integration for real-time features
- Internationalization (i18n) support
- Admin dashboard functionality

## Tech Stack

- **Frontend**: React 18.3.1 with TypeScript
- **Build Tool**: RSBuild (modern build tool)
- **State Management**: Zustand, MobX
- **UI Framework**: Ant Design 5.21.5
- **Styling**: SCSS, Tailwind CSS, Styled Components
- **Routing**: React Router DOM v6
- **Testing**: Playwright
- **Internationalization**: i18next
- **Real-time**: Socket.io Client
- **Development**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> for git hooks

## Architecture

- Modern React application with functional components
- TypeScript for type safety
- Component-based architecture with reusable UI components
- Protected routes for authentication
- Tenant-based multi-tenancy support
- WebSocket integration for real-time features
