# Project Structure

## Root Directory Structure

```
blog-ui-v1/
├── src/                    # Source code
├── public/                 # Static assets
├── tests/                  # Playwright tests
├── docs/                   # Documentation
├── scripts/                # Build and utility scripts
├── backlog/                # Task management (using Backlog.md)
├── .github/                # GitHub workflows
├── .husky/                 # Git hooks
└── playwright-report/      # Test reports
```

## Source Code Structure (`src/`)

```
src/
├── components/             # Reusable UI components
│   ├── layout/            # Layout components (default, auth, onboarding)
│   ├── loading/           # Loading components
│   └── ...                # Other UI components
├── pages/                  # Page components
│   ├── auth/              # Authentication pages (login, register, verify)
│   ├── onboarding/        # User onboarding
│   ├── tenant-onboarding/ # Tenant setup
│   └── user/              # User management pages
├── router/                 # Routing configuration
├── services/              # API services and business logic
├── hooks/                 # Custom React hooks
├── utils/                 # Utility functions
├── assets/                # Static assets (SCSS, images)
├── i18n/                  # Internationalization files
├── websocket/             # WebSocket related code
├── App.tsx                # Main application component
└── index.tsx              # Application entry point
```

## Key Configuration Files

- `package.json` - Dependencies and scripts
- `tsconfig.json` - TypeScript configuration
- `rsbuild.config.ts` - Build configuration
- `eslint.config.mjs` - ESLint rules
- `.prettierrc` - Code formatting rules
- `tailwind.config.js` - Tailwind CSS configuration
